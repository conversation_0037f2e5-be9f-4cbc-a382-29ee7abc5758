﻿using Newtonsoft.Json;
using SDCPCWeb.Models.Mongo;
using SDCPCWeb.Models.Result;
using SDCPCWeb.Models.Share;
using SDCPCWeb.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;

namespace SDCPCWeb.Controllers {
    /// <summary>
    /// 共享服务入口
    /// </summary>
    [RoutePrefix("sdcapi/share")]
    public class ShareController : ApiController {
        /// <summary>
        /// 统一共享服务入口请求
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [Route]
        public HttpResponseMessage Entry(SDCEncryptedData input) {
            var rsa = new RSAService();
            //对数据报文进行解密
            if (input?.Data == null) {
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "参数错误，获取请求的数据密文失败");
            }
            if (input.Sign == null) {
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "参数错误，获取请求的数字签名失败");
            }

            //解密
            var data = rsa.Decrypt(input.Data);
            if (data == null) {
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "解密失败，请使用正确的密钥进行加密");
            }

            //反序列化
            var request = JsonConvert.DeserializeObject<ShareRequest>(data);

            if (request?.ExternIdentity == null) {
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "参数错误，获取身份标识失败");
            }

            //判断身份
            var externIdentityService = new MongoService<ExternIdentity>();
            var identity = externIdentityService.GetById(request.ExternIdentity);
            if (identity == null || !identity.IsValid) {
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, $"参数错误，身份标识{request.ExternIdentity}尚未配置");
            }

            //身份获取成功，配置好身份公钥
            rsa.SetPublicKey(identity.PublicKey);

            //验签
            if (!rsa.VerifyData(data, input.Sign)) {
                //验签失败，可返回报文了
                var response = new ShareResponse {
                    Request = request,
                    Response = new ApiResult() { StateCode = 0, Message = "数字签名验证失败" }
                };

                //todo 响应之前记录响应日志

                var responseData = response.ToJson();
                return Request.CreateResponse(HttpStatusCode.OK, new SDCEncryptedData() {
                    Data = rsa.Encrypt(responseData),
                    Sign = rsa.SignData(responseData)
                });
            }

            //验签成功
            //执行方法调用并获取响应
            try {
                var share = new ShareMethods();
                var result = share.ExecuteMethod(request.MethodName, request.Parameters);

                var response = new ShareResponse {
                    Request = request,
                    Response = result
                };

                //todo 响应之前记录响应日志

                var responseData = response.ToJson();
                return Request.CreateResponse(HttpStatusCode.OK, new SDCEncryptedData() {
                    Data = rsa.Encrypt(responseData),
                    Sign = rsa.SignData(responseData)
                });
            }
            catch (Exception e) {
                //发生异常，则返回异常信息
                var response = new ShareResponse {
                    Request = request,
                    Response = new ApiResult() { StateCode = 0, Message = e.Message }
                };

                //todo 响应之前记录响应日志

                var responseData = response.ToJson();
                return Request.CreateResponse(HttpStatusCode.OK, new SDCEncryptedData() {
                    Data = rsa.Encrypt(responseData),
                    Sign = rsa.SignData(responseData)
                });
            }
        }

        /// <summary>
        /// 解密测试
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("Decrypt")]
        public HttpResponseMessage Decrypt(SDCEncryptedData input) {
            var rsa = new RSAService();
            //对数据报文进行解密
            if (input?.Data == null) {
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "参数错误，获取请求的数据密文失败");
            }

            //解密
            var data = rsa.Decrypt(input.Data);
            if (data == null) {
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "解密失败，请使用正确的密钥进行加密");
            }

            return Request.CreateResponse(HttpStatusCode.OK, new SDCEncryptedData() {
                Data = data
            });
        }
    }
}

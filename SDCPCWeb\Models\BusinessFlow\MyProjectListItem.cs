﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Reflection;
using System.Web;

namespace SDCPCWeb.Models.BusinessFlow {
    public class MyProjectListItem {
        /// <summary>
        /// 业务ID
        /// </summary>
        public string ID { get; set; }

        /// <summary>
        /// 业务创建者ID
        /// </summary>
        public string CreateUserId { get; set; }

        /// <summary>
        /// 业务编号
        /// </summary>
        public string BusinessNumber { get; set; }

        /// <summary>
        /// 业务名称
        /// </summary>
        public string BusinessName { get; set; }
        /// <summary>
        /// 业务类型
        /// </summary>
        public string BusinessType { get; set; }

        /// <summary>
        /// 业务逻辑类
        /// </summary>
        public string BusinessClass { get; set; }

        /// <summary>
        /// 业务创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 业务创建者姓名
        /// </summary>
        public string CreatePersonName { get; set; }

        /// <summary>
        /// 开发企业名称
        /// </summary>
        public string DeveloperName { get; set; }

        /// <summary>
        /// 开发企业证件号
        /// </summary>
        public string DeveloperNo { get; set; }

        /// <summary>
        /// 测绘单位名称
        /// </summary>
        public string SurveyCompanyName { get; set; }

        /// <summary>
        /// 测绘单位证件号
        /// </summary>
        public string SurveyCompanyNo { get; set; }

        /// <summary>
        /// 业务状态
        /// </summary>
        public int StateCode { get; set; }

        /// <summary>
        /// 当前环节名称
        /// </summary>
        public string LinkName { get; set; }

        /// <summary>
        /// 环节开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 环节签收时间
        /// </summary>
        public DateTime? SignatureTime { get; set; }

        /// <summary>
        /// 当前环节用户
        /// </summary>
        public string CurrentUserId { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? FinishTime { get; set; }


        public static string MainSql =>
            "SELECT BASE.ID,BASE.CREATEUSERID \"CreateUserId\",BASE.BUSINESSNUMBER \"BusinessNumber\",BASE.BUSINESSNAME \"BusinessName\",BASE.BUSINESSTYPE \"BusinessType\",BASE.BUSINESSCLASS \"BusinessClass\",BASE.CREATETIME \"CreateTime\"\n" +
            ",BASE.CREATEPERSONNAME \"CreatePersonName\",BASE.DEVELOPERNAME \"DeveloperName\",BASE.DEVELOPERNO \"DeveloperNo\",BASE.SURVEYCOMPANYNAME \"SurveyCompanyName\",BASE.SURVEYCOMPANYNO \"SurveyCompanyNo\"\n" +
            ",BASE.STATECODE \"StateCode\",ACT.LINKNAME \"LinkName\",ACT.STARTTIME \"StartTime\",ACT.SIGNATURETIME \"SignatureTime\",ACT.CURRENTUSERID \"CurrentUserId\",BASE.FINISHTIME \"FinishTime\"\n" +
            ",ACT.ENDTIME \n" +
            "FROM BUSINESSBASEINFO BASE\n" +
            "LEFT JOIN BUSINESSLINKINFO ACT ON BASE.ID=ACT.BUSINESSID AND ACT.STATECODE IN(0,1,3)\n" +
            "LEFT JOIN BUSINESSLINKTOSIGN TS ON ACT.ID=TS.BUSINESSLINKINFOID AND ACT.CURRENTUSERID IS NULL\n";
    }
}
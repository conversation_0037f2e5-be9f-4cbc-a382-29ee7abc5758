﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using SDCPCWeb.Services;

namespace SDCPCWeb.Models.EstateConstruction {
    /// <summary>
    /// 不动产实测绘办结结果信息
    /// </summary>
    public class ActualSurveyResultInfo : IOracleDataTable {
        /// <summary>
        /// 楼栋GUID
        /// </summary>
        public string ID { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 业务Id
        /// </summary>
        public string BusinessBaseInfoId { get; set; }

        /// <summary>
        /// 开发商名称
        /// </summary>
        public string DeveloperName { get; set; }

        /// <summary>
        /// 开发商证件号
        /// </summary>
        public string DeveloperNo { get; set; }

        /// <summary>
        /// 不动产实测绘备案编号
        /// </summary>
        public string SDCNo { get; set; }

        /// <summary>
        /// 坐落
        /// </summary>
        public string ZL { get; set; }

        /// <summary>
        /// 原始交房日期
        /// </summary>
        public DateTime? OriginalDeliveryDate { get; set; }

        /// <summary>
        /// 开发商设置的交房日期
        /// </summary>
        public DateTime? DeliveryDate { get; set; }

        /// <summary>
        /// 是否已开放办理转移业务
        /// </summary>
        public int IsOpen { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 不动产实核业务名称
        /// </summary>
        public string ProjectName { get; set; }

        /// <summary>
        /// 是否首登已办理，"1"是，其它否
        /// </summary>
        public string IsSDCompleted { get; set; }

        /// <summary>
        /// 是否已无效（楼栋作过实核成果变更业务），"1"是，其它否
        /// </summary>
        public string IsInvalid { get; set; }

        /// <summary>
        /// 楼栋GUID
        /// </summary>
        public string ZRZGUID { get; set; }
    }
}
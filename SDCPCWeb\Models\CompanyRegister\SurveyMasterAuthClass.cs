﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Newtonsoft.Json;
using Oracle.ManagedDataAccess.Client;
using SDCPCWeb.Services;

namespace SDCPCWeb.Models.CompanyRegister {
    /// <summary>
    /// 注册测绘师授权业务类型表
    /// </summary>
    public class SurveyMasterAuthClassModel {

        public string ID { get; set; }
        /// <summary>
        /// 授权ID
        /// </summary>
        public string AuthID { get; set; }
        /// <summary>
        /// 注册测绘师ID
        /// </summary>
        public string BusinessClass { get; set; }

        public static List<SurveyMasterAuthClassModel> GetListFromAuthId(string authId, OracleDataService oracle = null) {
            if (string.IsNullOrWhiteSpace(authId)) {
                return null;
            }
            if (oracle == null) {
                using (oracle = new OracleDataService()) {
                    return GetListFromAuthId(authId, oracle);
                }
            }
            else {
                return oracle.GetList<SurveyMasterAuthClass>("AuthID=:aid"
                            , new OracleParameter(":aid", OracleDbType.Varchar2) { Value = authId }
                        ).Select(c => c.ToModel()).ToList();
            }
        }
    }

    public class SurveyMasterAuthClass : SurveyMasterAuthClassModel, IOracleDataTable {
        public SurveyMasterAuthClassModel ToModel() {
            return JsonConvert.DeserializeObject<SurveyMasterAuthClassModel>(JsonConvert.SerializeObject(this));
        }

        public static SurveyMasterAuthClass FromModel(SurveyMasterAuthClassModel model) {
            return JsonConvert.DeserializeObject<SurveyMasterAuthClass>(JsonConvert.SerializeObject(model));
        }
    }
}
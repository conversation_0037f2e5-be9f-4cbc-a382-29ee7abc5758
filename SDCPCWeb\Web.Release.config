﻿<?xml version="1.0" encoding="utf-8"?>

<!-- 有关使用 Web.config 转换的详细信息，请访问 https://go.microsoft.com/fwlink/?LinkId=301874 -->

<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">
  <!--
    在下例中，“SetAttributes”转换将更改
    “connectionString”的值，仅在“Match”定位器找到值为“MyDB”的
    特性“name”时使用“ReleaseSQLServer”。

    <connectionStrings>
      <add name="MyDB"
        connectionString="Data Source=ReleaseSQLServer;Initial Catalog=MyReleaseDB;Integrated Security=True"
        xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>
    </connectionStrings>
  -->
  <connectionStrings>
    <add name="SDCWorkDB" providerName="Oracle.ManagedDataAccess.Client" connectionString="Data Source=172.16.3.68:21521/sdcdb;User Id=sdcwork;Password=*********" xdt:Transform="SetAttributes" xdt:Locator="Match(name)" />
    <add name="HangfireMongoDB" connectionString="*************************************************************************************************" xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>
  </connectionStrings>
  <appSettings>
    <!--单点登录主站-->
    <add key="LoginServer" value="sdc-bdcweb-api.nnland.cn" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <!--附件存储根目录-->
    <add key="RootPath" value="\\172.16.1.43\sdcweb_annexes\Uploads" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <!--工程规划许可接口地址-->
    <add key="ServiceUrl" value="http://172.16.4.234:8998/projectinfo/select/selectProjectInfo" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="ServiceUrl1" value="http://jiansgc-prod.nnzrj.com/api/jsgcghxkz/noAuth/getTuWenData" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="xCloudApiUrl" value="https://guihhs-prod.nnzrj.com/api" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <!--测绘子系统-云平台接口地址-->
    <add key="CH_xCloudApiUrl" value="https://ch-prod.nnzrj.com/api" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="EPSSDCServices" value="http://172.16.3.58/EPSSDCServices/EPSSDCServices.asmx" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="EPSDownUrl" value="http://172.16.3.58/SDCDownloadFile/Download" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <!--EPS上传全面核实MDB检查接口-->
    <add key="EPSOverallUploadMDBUrl" value="http://172.16.3.58/SDCUploadService/api/Attachment/Upload" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <!--MDB检查接口基础地址-->
    <add key="MdbCheckApiUrl" value="http://172.16.48.133:5000" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="BDCSystemApiUrl" value="http://onemap-api-nn.bdc.nnbdc.cn" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="DataApiUrl" value="http://172.16.0.211:9900" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="BDCWebServiceUrl" value="https://sdc-bdcweb-api.nnland.cn" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="EnableFlows" value="BaseSurveyDataDownloadFlow,MarkPointSurveyFlow,MeasurePutLineFlow,RealEstatePreSurveyFlow,RealEstatePreCheckSurveyFlow,RealEstateActualSurveyFlow,RealEstatePreCheckSurveyAutoFlow,RealEstatePreSurveyBuildingTableChangeFlow,RealEstatePreSurveyResultChangeFlow,RealEstateActualBuildingTableChangeFlow,RealEstateActualResultChangeFlow,MeasurePutLinePreCheckFlow,CouncilPlanCheckFlow,CouncilMeasurePutLinePreCheckFlow,RealEstateOverallActualSurveyFlow" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="VisibleFlows" value="BaseSurveyDataDownloadFlow,MarkPointSurveyFlow,RealEstatePreCheckSurveyFlow,RealEstateActualSurveyFlow,RealEstatePreCheckSurveyAutoFlow,RealEstatePreSurveyBuildingTableChangeFlow,RealEstatePreSurveyResultChangeFlow,RealEstateActualResultChangeFlow,MeasurePutLinePreCheckFlow,CouncilPlanCheckFlow,CouncilMeasurePutLinePreCheckFlow,RealEstateOverallActualSurveyFlow" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
  </appSettings>
  <system.web>
    <compilation xdt:Transform="RemoveAttributes(debug)" />
    <!--
      在以下示例中，"Replace" 转换将替换 Web.config 文件的
      整个 <customErrors> 节。
      请注意，由于在 <system.web> 节点下只有一个
       customErrors 节，因此无需使用 "xdt:Locator" 属性。

      <customErrors defaultRedirect="GenericError.htm"
        mode="RemoteOnly" xdt:Transform="Replace">
        <error statusCode="500" redirect="InternalError.htm"/>
      </customErrors>
    -->
    <customErrors xdt:Transform="Remove"></customErrors>
  </system.web>
</configuration>

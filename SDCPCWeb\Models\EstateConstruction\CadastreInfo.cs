﻿using Newtonsoft.Json;
using SDCPCWeb.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace SDCPCWeb.Models.EstateConstruction {
    /// <summary>
    /// 房建项目管理--宗地信息
    /// </summary>
    public class CadastreInfoModel {
        /// <summary>
        /// ID
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 宗地代码
        /// </summary>
        public string CadastreCode { get; set; }
        /// <summary>
        /// 宗地不动产权证号
        /// </summary>
        public string EstateNo { get; set; }
        /// <summary>
        /// 小区名称
        /// </summary>
        public string QuartersName { get; set; }
        /// <summary>
        /// 统一社会信用代码
        /// </summary>
        public string CreditCode { get; set; }
    }
    public class CadastreInfo : CadastreInfoModel, IOracleDataTable {
        public CadastreInfoModel ToModel() {
            return JsonConvert.DeserializeObject<CadastreInfoModel>(JsonConvert.SerializeObject(this));
        }
    }
}
﻿/*
 * Design by liqisheng
 */
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;

namespace SDCPCWeb.Helpers {
    public class ExportToOffice2003XmlHelper {

        private string _sheetName = "Sheet"; //工作表名称
        private int _sheetIndex = 1; //工作表数量索引

        private ExportExcelInfo _exportExcelInfo;
        private List<ExportExcelInfo> _exportExcelInfoList;

        private StreamWriter _streamWriter; //写内存

        #region Public
        /// <summary>
        /// 生成Excel Xml字符串
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public StringBuilder CreateXmlString(List<ExportExcelInfo> list) {
            _exportExcelInfoList = list;

            StringBuilder sb = new StringBuilder();
            sb.Append(CreateHead());
            sb.Append(CreateDocumentProperties());
            sb.Append(CreateExcelWorkbookProperties());
            sb.Append(CreateStyleProperties());

            foreach (var t in _exportExcelInfoList) {
                sb.Append(CreateSimpleData(t));
            }

            sb.Append(CreateEnd());

            return sb;
        }

        /// <summary>
        /// 生成Excel Xml流
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public Stream CreateXmlStream(List<ExportExcelInfo> list) {
            _exportExcelInfoList = list;

            //系统临时文件夹
            string tempFilePath = Environment.GetEnvironmentVariable("TEMP") + "\\";
            if (!Directory.Exists(tempFilePath)) {
                Directory.CreateDirectory(tempFilePath);
            }

            //临时文件名称，不包括后缀名
            string tempFileName = string.Format("{0}_{1:yyyy-MM-dd}_{2:N}", "OutPutXML", DateTime.Now, Guid.NewGuid());

            //完整路径+文件名+后缀名
            string tempFilePathName = string.Format("{0}{1}{2}", tempFilePath, tempFileName, ".xml");

            _streamWriter = new StreamWriter(tempFilePathName, false);
            try {
                CreateHead(_streamWriter);
                CreateDocumentProperties(_streamWriter);
                CreateExcelWorkbookProperties(_streamWriter);
                CreateStyleProperties(_streamWriter);
                foreach (var t in _exportExcelInfoList) {
                    CreateSheet(_streamWriter, t);
                }
                CreateEnd(_streamWriter);
            } finally {
                _streamWriter.Close();
            }

            FileStream fileStream = File.Open(tempFilePathName, FileMode.Open);
            try {
                fileStream.Position = 0;
                MemoryStream memoryStream = new MemoryStream();
                fileStream.CopyTo(memoryStream);
                memoryStream.Position = 0;
                return memoryStream;
            } finally {
                Task.Run(() => {
                    fileStream.Close();
                    File.Delete(tempFilePathName);
                });
            }
        }

        #endregion


        /// <summary>
        /// 生成单个DataTable数据
        /// </summary>
        /// <param name="dt"></param>
        /// <param name="sheetName"></param>
        /// <param name="sumDataColumnList"></param>
        /// <param name="mainTitle"></param>
        /// <param name="subTitle"></param>
        /// <returns></returns>
        private StringBuilder CreateSimpleData(ExportExcelInfo exportExcelInfo) {
            _exportExcelInfo = exportExcelInfo;

            StringBuilder sb = new StringBuilder();

            sb.Append(CreateSheet());

            return sb;
        }

        private void CreateXmlNodeData(DataTable dt, XmlNode xmlNodeTempleteDataRow) {
            XmlNode templeteRowNode = xmlNodeTempleteDataRow.Clone();
            XmlNode parentNode = xmlNodeTempleteDataRow.ParentNode;
            XmlNode bottomNode = xmlNodeTempleteDataRow.NextSibling;

            bool hasIndex = false;
            int beginIndex = 0;

            if (parentNode == null)
                throw new Exception("未找到任何父节点");

            if (xmlNodeTempleteDataRow.Attributes == null)
                throw new Exception("未找到节点的属性");

            XmlAttribute attrIndex = xmlNodeTempleteDataRow.Attributes["ss:Index"];
            if (attrIndex != null) {
                hasIndex = true;
                beginIndex = Convert.ToInt32(attrIndex.Value);
            }


            XmlNode prevNode = null;
            for (int i = 0; i < dt.Rows.Count; i++) {
                XmlNode node;
                if (i == 0) {
                    node = xmlNodeTempleteDataRow;
                    int j = 0;
                    foreach (XmlNode item in node) {
                        string value = FormatSheetCellValue(dt.Columns[j].DataType, dt.Rows[i][j].ToString());
                        item.InnerXml = item.InnerXml.Replace("[数据]", value);
                        j++;
                    }

                    prevNode = node;
                } else {
                    node = templeteRowNode.Clone();

                    if (hasIndex) {
                        if (node.Attributes == null)
                            throw new Exception("未找到节点的属性");

                        attrIndex = node.Attributes["ss:Index"];
                        if (attrIndex != null) {
                            attrIndex.Value = (i + beginIndex).ToString();
                        }
                    }

                    int j = 0;
                    foreach (XmlNode item in node) {
                        string value = FormatSheetCellValue(dt.Columns[j].DataType, dt.Rows[i][j].ToString());
                        item.InnerXml = item.InnerXml.Replace("[数据]", value);
                        j++;
                    }



                    if (bottomNode == null) {

                        parentNode.InsertAfter(node, prevNode);
                        prevNode = node;
                    } else
                        parentNode.InsertBefore(node, bottomNode);
                }
            }

            if (dt.Rows.Count == 0) {
                parentNode.RemoveChild(xmlNodeTempleteDataRow);

                if (hasIndex) {
                    if (bottomNode != null && bottomNode.Attributes != null) {
                        attrIndex = bottomNode.Attributes["ss:Index"];
                        if (attrIndex != null) {
                            attrIndex.Value = (Convert.ToInt32(attrIndex.Value) - 1).ToString();
                        }

                        while (bottomNode.NextSibling != null) {
                            attrIndex = bottomNode.Attributes["ss:Index"];
                            if (attrIndex != null) {
                                attrIndex.Value = (Convert.ToInt32(attrIndex.Value) - 1).ToString();
                            }
                        }
                    }
                }

            } else {
                if (dt.Rows.Count > 1) {
                    if (hasIndex) {
                        if (bottomNode != null && bottomNode.Attributes != null) {
                            attrIndex = bottomNode.Attributes["ss:Index"];
                            if (attrIndex != null) {
                                attrIndex.Value = (Convert.ToInt32(attrIndex.Value) + dt.Rows.Count - 1).ToString();
                            }

                            while (bottomNode.NextSibling != null) {
                                attrIndex = bottomNode.Attributes["ss:Index"];
                                if (attrIndex != null) {
                                    attrIndex.Value =
                                        (Convert.ToInt32(attrIndex.Value) + dt.Rows.Count - 1).ToString();
                                }
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 生成XML文件头
        /// </summary>
        /// <returns></returns>
        private string CreateHead() {
            StringBuilder head = new StringBuilder();
            head.Append("<?xml version=\"1.0\"?>" + "\r\n");
            head.Append("<?mso-application progid=\"Excel.Sheet\"?>" + "\r\n");

            head.Append("<Workbook xmlns=\"urn:schemas-microsoft-com:office:spreadsheet\"" + "\r\n");
            head.Append(" xmlns:o=\"urn:schemas-microsoft-com:office:office\"" + "\r\n");
            head.Append(" xmlns:x=\"urn:schemas-microsoft-com:office:excel\"" + "\r\n");
            head.Append(" xmlns:ss=\"urn:schemas-microsoft-com:office:spreadsheet\"" + "\r\n");
            head.Append(" xmlns:html=\"http://www.w3.org/TR/REC-html40\">" + "\r\n");

            return head.ToString();
        }

        private void CreateHead(StreamWriter sw) {
            StringBuilder head = new StringBuilder();
            head.Append("<?xml version=\"1.0\"?>" + "\r\n");
            head.Append("<?mso-application progid=\"Excel.Sheet\"?>" + "\r\n");

            head.Append("<Workbook xmlns=\"urn:schemas-microsoft-com:office:spreadsheet\"" + "\r\n");
            head.Append(" xmlns:o=\"urn:schemas-microsoft-com:office:office\"" + "\r\n");
            head.Append(" xmlns:x=\"urn:schemas-microsoft-com:office:excel\"" + "\r\n");
            head.Append(" xmlns:ss=\"urn:schemas-microsoft-com:office:spreadsheet\"" + "\r\n");
            head.Append(" xmlns:html=\"http://www.w3.org/TR/REC-html40\">" + "\r\n");

            sw.Write(head);
        }


        /// <summary>
        /// 生成XML文档属性
        /// </summary>
        /// <param name="author"></param>
        /// <param name="lastAuthor"></param>
        /// <returns></returns>
        private string CreateDocumentProperties(string author = "管理员", string lastAuthor = "管理员") {
            StringBuilder sb = new StringBuilder();
            sb.Append(" <DocumentProperties xmlns=\"urn:schemas-microsoft-com:office:office\">" + "\r\n");
            sb.Append("  <Author>" + author + "</Author>" + "\r\n");
            sb.Append("  <LastAuthor>" + lastAuthor + "</LastAuthor>" + "\r\n");
            sb.Append("  <Created>" + DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ssZ") + "</Created>" + "\r\n");
            sb.Append("  <LastSaved>" + DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ssZ") + "</LastSaved>" + "\r\n");
            sb.Append("  <Company>China</Company>" + "\r\n");
            sb.Append("  <Version>12.00</Version>" + "\r\n");
            sb.Append(" </DocumentProperties>" + "\r\n");

            return sb.ToString();
        }

        private void CreateDocumentProperties(StreamWriter sw, string author = "管理员", string lastAuthor = "管理员") {
            StringBuilder sb = new StringBuilder();
            sb.Append(" <DocumentProperties xmlns=\"urn:schemas-microsoft-com:office:office\">" + "\r\n");
            sb.Append("  <Author>" + author + "</Author>" + "\r\n");
            sb.Append("  <LastAuthor>" + lastAuthor + "</LastAuthor>" + "\r\n");
            sb.Append("  <Created>" + DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ssZ") + "</Created>" + "\r\n");
            sb.Append("  <LastSaved>" + DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ssZ") + "</LastSaved>" + "\r\n");
            sb.Append("  <Company>China</Company>" + "\r\n");
            sb.Append("  <Version>12.00</Version>" + "\r\n");
            sb.Append(" </DocumentProperties>" + "\r\n");

            sw.Write(sb);
        }


        /// <summary>
        /// 生成XML Workbook属性
        /// </summary>
        /// <returns></returns>
        private string CreateExcelWorkbookProperties() {
            StringBuilder sb = new StringBuilder();
            sb.Append(" <ExcelWorkbook xmlns=\"urn:schemas-microsoft-com:office:excel\">" + "\r\n");
            sb.Append("  <WindowHeight>14805</WindowHeight>" + "\r\n");
            sb.Append("  <WindowWidth>24795</WindowWidth>" + "\r\n");
            sb.Append("  <WindowTopX>240</WindowTopX>" + "\r\n");
            sb.Append("  <WindowTopY>60</WindowTopY>" + "\r\n");
            sb.Append("  <ProtectStructure>False</ProtectStructure>" + "\r\n");
            sb.Append("  <ProtectWindows>False</ProtectWindows>" + "\r\n");
            sb.Append(" </ExcelWorkbook>" + "\r\n");

            return sb.ToString();
        }

        private void CreateExcelWorkbookProperties(StreamWriter sw) {
            StringBuilder sb = new StringBuilder();
            sb.Append(" <ExcelWorkbook xmlns=\"urn:schemas-microsoft-com:office:excel\">" + "\r\n");
            sb.Append("  <WindowHeight>14805</WindowHeight>" + "\r\n");
            sb.Append("  <WindowWidth>24795</WindowWidth>" + "\r\n");
            sb.Append("  <WindowTopX>240</WindowTopX>" + "\r\n");
            sb.Append("  <WindowTopY>60</WindowTopY>" + "\r\n");
            sb.Append("  <ProtectStructure>False</ProtectStructure>" + "\r\n");
            sb.Append("  <ProtectWindows>False</ProtectWindows>" + "\r\n");
            sb.Append(" </ExcelWorkbook>" + "\r\n");

            sw.Write(sb);
        }


        /// <summary>
        /// 生成XML 样式属性
        /// </summary>
        /// <returns></returns>
        private string CreateStyleProperties() {
            StringBuilder sb = new StringBuilder();

            sb.Append(" <Styles>" + "\r\n");

            sb.Append("  <Style ss:ID=\"Default\" ss:Name=\"Normal\">" + "\r\n");
            sb.Append("   <Alignment ss:Vertical=\"Bottom\"/>" + "\r\n");
            sb.Append("   <Borders/>" + "\r\n");
            sb.Append("   <Font ss:FontName=\"Arial\" x:Family=\"Swiss\"/>" + "\r\n");
            sb.Append("   <Interior/>" + "\r\n");
            sb.Append("   <NumberFormat/>" + "\r\n");
            sb.Append("   <Protection/>" + "\r\n");
            sb.Append("  </Style>" + "\r\n");

            //单元格样式（#BFBFBF背景颜色，粗体，宋体，文本格式）
            sb.Append("  <Style ss:ID=\"s62\">" + "\r\n");
            sb.Append("   <Alignment ss:Horizontal=\"Center\" ss:Vertical=\"Center\" />" + "\r\n");
            sb.Append("   <Borders>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Bottom\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Left\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Right\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Top\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("   </Borders>" + "\r\n");
            sb.Append("   <Font ss:FontName=\"宋体\" x:CharSet=\"134\" ss:Bold=\"1\"/>" + "\r\n");
            sb.Append("   <Interior ss:Color=\"#BFBFBF\" ss:Pattern=\"Solid\"/>" + "\r\n");
            sb.Append("   <NumberFormat ss:Format=\"@\"/>" + "\r\n");
            sb.Append("   <Protection/>" + "\r\n");
            sb.Append("  </Style>" + "\r\n");

            //单元格样式（#BFBFBF背景颜色，文字红色，粗体，宋体，文本格式）
            sb.Append("  <Style ss:ID=\"s63\">" + "\r\n");
            sb.Append("   <Alignment ss:Horizontal=\"Center\" ss:Vertical=\"Center\" />" + "\r\n");
            sb.Append("   <Borders>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Bottom\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Left\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Right\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Top\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("   </Borders>" + "\r\n");
            sb.Append("   <Font ss:FontName=\"宋体\" x:CharSet=\"134\" ss:Color=\"#FF0000\" ss:Bold=\"1\"/>" + "\r\n");
            sb.Append("   <Interior ss:Color=\"#BFBFBF\" ss:Pattern=\"Solid\"/>" + "\r\n");
            sb.Append("   <NumberFormat ss:Format=\"@\"/>" + "\r\n");
            sb.Append("   <Protection/>" + "\r\n");
            sb.Append("  </Style>" + "\r\n");

            //单元格样式（文本格式）
            sb.Append("  <Style ss:ID=\"s64\">" + "\r\n");
            sb.Append("   <Alignment ss:Horizontal=\"Center\" ss:Vertical=\"Center\" />" + "\r\n");
            sb.Append("   <Borders>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Bottom\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Left\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Right\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Top\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("   </Borders>" + "\r\n");
            sb.Append("   <Font ss:FontName=\"宋体\" x:CharSet=\"134\"/>" + "\r\n");
            sb.Append("   <NumberFormat ss:Format=\"@\"/>" + "\r\n");
            sb.Append("  </Style>" + "\r\n");

            //单元格样式（数值格式，保留2位小数）
            sb.Append("  <Style ss:ID=\"s65\">" + "\r\n");
            sb.Append("   <Alignment ss:Horizontal=\"Center\" ss:Vertical=\"Center\" />" + "\r\n");
            sb.Append("   <Borders>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Bottom\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Left\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Right\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Top\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("   </Borders>" + "\r\n");
            sb.Append("   <Font ss:FontName=\"宋体\" x:CharSet=\"134\"/>" + "\r\n");
            sb.Append("   <NumberFormat ss:Format=\"0.00_ \"/>" + "\r\n");
            sb.Append("  </Style>" + "\r\n");

            //单元格样式（短日期格式）
            sb.Append("  <Style ss:ID=\"s66\">" + "\r\n");
            sb.Append("   <Alignment ss:Horizontal=\"Center\" ss:Vertical=\"Center\" />" + "\r\n");
            sb.Append("   <Borders>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Bottom\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Left\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Right\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Top\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("   </Borders>" + "\r\n");
            sb.Append("   <Font ss:FontName=\"宋体\" x:CharSet=\"134\"/>" + "\r\n");
            sb.Append("   <NumberFormat ss:Format=\"Short Date\"/>" + "\r\n");
            sb.Append("  </Style>" + "\r\n");

            //单元格样式（整型）
            sb.Append("  <Style ss:ID=\"s67\">" + "\r\n");
            sb.Append("   <Alignment ss:Horizontal=\"Center\" ss:Vertical=\"Center\" />" + "\r\n");
            sb.Append("   <Borders>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Bottom\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Left\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Right\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Top\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("   </Borders>" + "\r\n");
            sb.Append("   <Font ss:FontName=\"宋体\" x:CharSet=\"134\"/>" + "\r\n");
            sb.Append("   <NumberFormat ss:Format=\"0_ \"/>" + "\r\n");
            sb.Append("  </Style>" + "\r\n");

            //合计行样式
            sb.Append("  <Style ss:ID=\"s68\">" + "\r\n");
            sb.Append("   <Alignment ss:Horizontal=\"Center\" ss:Vertical=\"Center\"/>" + "\r\n");
            sb.Append("   <Borders>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Bottom\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Left\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Right\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Top\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("   </Borders>" + "\r\n");
            sb.Append("   <Font ss:FontName=\"宋体\" x:CharSet=\"134\"/>" + "\r\n");
            sb.Append("   <Interior ss:Color=\"#D8D8D8\" ss:Pattern=\"Solid\"/>" + "\r\n");
            sb.Append("   <NumberFormat ss:Format=\"@\"/>" + "\r\n");
            sb.Append("  </Style>" + "\r\n");

            sb.Append("  <Style ss:ID=\"s69\">" + "\r\n");
            sb.Append("   <Alignment ss:Horizontal=\"Center\" ss:Vertical=\"Center\"/>" + "\r\n");
            sb.Append("   <Borders>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Bottom\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Left\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Right\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Top\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("   </Borders>" + "\r\n");
            sb.Append("   <Font ss:FontName=\"宋体\" x:CharSet=\"134\"/>" + "\r\n");
            sb.Append("   <Interior ss:Color=\"#D8D8D8\" ss:Pattern=\"Solid\"/>" + "\r\n");
            sb.Append("   <NumberFormat ss:Format=\"0.00_ \"/>" + "\r\n");
            sb.Append("  </Style>" + "\r\n");

            sb.Append("  <Style ss:ID=\"s70\">" + "\r\n");
            sb.Append("   <Alignment ss:Horizontal=\"Center\" ss:Vertical=\"Center\"/>" + "\r\n");
            sb.Append("   <Borders>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Bottom\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Left\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Right\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Top\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("   </Borders>" + "\r\n");
            sb.Append("   <Font ss:FontName=\"宋体\" x:CharSet=\"134\"/>" + "\r\n");
            sb.Append("   <Interior ss:Color=\"#D8D8D8\" ss:Pattern=\"Solid\"/>" + "\r\n");
            sb.Append("   <NumberFormat ss:Format=\"Short Date\"/>" + "\r\n");
            sb.Append("  </Style>" + "\r\n");

            sb.Append("  <Style ss:ID=\"s71\">" + "\r\n");
            sb.Append("   <Alignment ss:Horizontal=\"Center\" ss:Vertical=\"Center\"/>" + "\r\n");
            sb.Append("   <Borders>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Bottom\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Left\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Right\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Top\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("   </Borders>" + "\r\n");
            sb.Append("   <Font ss:FontName=\"宋体\" x:CharSet=\"134\"/>" + "\r\n");
            sb.Append("   <Interior ss:Color=\"#D8D8D8\" ss:Pattern=\"Solid\"/>" + "\r\n");
            sb.Append("   <NumberFormat ss:Format=\"0_ \"/>" + "\r\n");
            sb.Append("  </Style>" + "\r\n");

            //主标题行样式
            sb.Append("  <Style ss:ID=\"s72\">" + "\r\n");
            sb.Append("   <Alignment ss:Horizontal=\"Center\" ss:Vertical=\"Center\"/>" + "\r\n");
            sb.Append("   <Borders/>" + "\r\n");
            sb.Append("   <Font ss:FontName=\"宋体\" x:CharSet=\"134\"/>" + "\r\n");
            sb.Append("   <NumberFormat ss:Format=\"@\"/>" + "\r\n");
            sb.Append("  </Style>" + "\r\n");

            //小标题行样式
            sb.Append("  <Style ss:ID=\"s73\">" + "\r\n");
            sb.Append("   <Alignment ss:Horizontal=\"Center\" ss:Vertical=\"Center\"/>" + "\r\n");
            sb.Append("   <Borders/>" + "\r\n");
            sb.Append("   <Font ss:FontName=\"宋体\" x:CharSet=\"134\"/>" + "\r\n");
            sb.Append("   <NumberFormat ss:Format=\"@\"/>" + "\r\n");
            sb.Append("  </Style>" + "\r\n");

            //主标题单元格样式
            sb.Append("  <Style ss:ID=\"s74\">" + "\r\n");
            sb.Append("   <Alignment ss:Horizontal=\"Center\" ss:Vertical=\"Center\"/>" + "\r\n");
            sb.Append("   <Borders/>" + "\r\n");
            sb.Append("   <Font ss:FontName=\"宋体\" x:CharSet=\"134\" ss:Size=\"20\" ss:Bold=\"1\"/>" + "\r\n");
            sb.Append("   <NumberFormat ss:Format=\"@\"/>" + "\r\n");
            sb.Append("  </Style>" + "\r\n");

            //小标题单元格样式
            sb.Append("  <Style ss:ID=\"s75\">" + "\r\n");
            sb.Append("   <Alignment ss:Horizontal=\"Center\" ss:Vertical=\"Center\"/>" + "\r\n");
            sb.Append("   <Borders/>" + "\r\n");
            sb.Append("   <Font ss:FontName=\"宋体\" x:CharSet=\"134\" ss:Size=\"12\"/>" + "\r\n");
            sb.Append("   <NumberFormat ss:Format=\"@\"/>" + "\r\n");
            sb.Append("  </Style>" + "\r\n");

            //单元格样式（文本格式，左对齐）
            sb.Append("  <Style ss:ID=\"s76\">" + "\r\n");
            sb.Append("   <Alignment ss:Horizontal=\"Left\" ss:Vertical=\"Center\" />" + "\r\n");
            sb.Append("   <Borders>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Bottom\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Left\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Right\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Top\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("   </Borders>" + "\r\n");
            sb.Append("   <Font ss:FontName=\"宋体\" x:CharSet=\"134\"/>" + "\r\n");
            sb.Append("   <NumberFormat ss:Format=\"@\"/>" + "\r\n");
            sb.Append("  </Style>" + "\r\n");


            sb.Append(" </Styles>" + "\r\n");

            return sb.ToString();
        }

        private void CreateStyleProperties(StreamWriter sw) {
            StringBuilder sb = new StringBuilder();

            sb.Append(" <Styles>" + "\r\n");

            sb.Append("  <Style ss:ID=\"Default\" ss:Name=\"Normal\">" + "\r\n");
            sb.Append("   <Alignment ss:Vertical=\"Bottom\"/>" + "\r\n");
            sb.Append("   <Borders/>" + "\r\n");
            sb.Append("   <Font ss:FontName=\"Arial\" x:Family=\"Swiss\"/>" + "\r\n");
            sb.Append("   <Interior/>" + "\r\n");
            sb.Append("   <NumberFormat/>" + "\r\n");
            sb.Append("   <Protection/>" + "\r\n");
            sb.Append("  </Style>" + "\r\n");

            //单元格样式（#BFBFBF背景颜色，粗体，宋体，文本格式）
            sb.Append("  <Style ss:ID=\"s62\">" + "\r\n");
            sb.Append("   <Alignment ss:Horizontal=\"Center\" ss:Vertical=\"Center\" />" + "\r\n");
            sb.Append("   <Borders>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Bottom\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Left\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Right\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Top\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("   </Borders>" + "\r\n");
            sb.Append("   <Font ss:FontName=\"宋体\" x:CharSet=\"134\" ss:Bold=\"1\"/>" + "\r\n");
            sb.Append("   <Interior ss:Color=\"#BFBFBF\" ss:Pattern=\"Solid\"/>" + "\r\n");
            sb.Append("   <NumberFormat ss:Format=\"@\"/>" + "\r\n");
            sb.Append("   <Protection/>" + "\r\n");
            sb.Append("  </Style>" + "\r\n");

            //单元格样式（#BFBFBF背景颜色，文字红色，粗体，宋体，文本格式）
            sb.Append("  <Style ss:ID=\"s63\">" + "\r\n");
            sb.Append("   <Alignment ss:Horizontal=\"Center\" ss:Vertical=\"Center\" />" + "\r\n");
            sb.Append("   <Borders>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Bottom\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Left\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Right\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Top\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("   </Borders>" + "\r\n");
            sb.Append("   <Font ss:FontName=\"宋体\" x:CharSet=\"134\" ss:Color=\"#FF0000\" ss:Bold=\"1\"/>" + "\r\n");
            sb.Append("   <Interior ss:Color=\"#BFBFBF\" ss:Pattern=\"Solid\"/>" + "\r\n");
            sb.Append("   <NumberFormat ss:Format=\"@\"/>" + "\r\n");
            sb.Append("   <Protection/>" + "\r\n");
            sb.Append("  </Style>" + "\r\n");

            //单元格样式（文本格式）
            sb.Append("  <Style ss:ID=\"s64\">" + "\r\n");
            sb.Append("   <Alignment ss:Horizontal=\"Center\" ss:Vertical=\"Center\" />" + "\r\n");
            sb.Append("   <Borders>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Bottom\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Left\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Right\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Top\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("   </Borders>" + "\r\n");
            sb.Append("   <Font ss:FontName=\"宋体\" x:CharSet=\"134\"/>" + "\r\n");
            sb.Append("   <NumberFormat ss:Format=\"@\"/>" + "\r\n");
            sb.Append("  </Style>" + "\r\n");

            //单元格样式（数值格式，保留2位小数）
            sb.Append("  <Style ss:ID=\"s65\">" + "\r\n");
            sb.Append("   <Alignment ss:Horizontal=\"Center\" ss:Vertical=\"Center\" />" + "\r\n");
            sb.Append("   <Borders>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Bottom\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Left\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Right\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Top\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("   </Borders>" + "\r\n");
            sb.Append("   <Font ss:FontName=\"宋体\" x:CharSet=\"134\"/>" + "\r\n");
            sb.Append("   <NumberFormat ss:Format=\"0.00_ \"/>" + "\r\n");
            sb.Append("  </Style>" + "\r\n");

            //单元格样式（短日期格式）
            sb.Append("  <Style ss:ID=\"s66\">" + "\r\n");
            sb.Append("   <Alignment ss:Horizontal=\"Center\" ss:Vertical=\"Center\" />" + "\r\n");
            sb.Append("   <Borders>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Bottom\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Left\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Right\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Top\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("   </Borders>" + "\r\n");
            sb.Append("   <Font ss:FontName=\"宋体\" x:CharSet=\"134\"/>" + "\r\n");
            sb.Append("   <NumberFormat ss:Format=\"Short Date\"/>" + "\r\n");
            sb.Append("  </Style>" + "\r\n");

            //单元格样式（整型）
            sb.Append("  <Style ss:ID=\"s67\">" + "\r\n");
            sb.Append("   <Alignment ss:Horizontal=\"Center\" ss:Vertical=\"Center\" />" + "\r\n");
            sb.Append("   <Borders>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Bottom\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Left\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Right\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Top\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("   </Borders>" + "\r\n");
            sb.Append("   <Font ss:FontName=\"宋体\" x:CharSet=\"134\"/>" + "\r\n");
            sb.Append("   <NumberFormat ss:Format=\"0_ \"/>" + "\r\n");
            sb.Append("  </Style>" + "\r\n");

            //合计行样式
            sb.Append("  <Style ss:ID=\"s68\">" + "\r\n");
            sb.Append("   <Alignment ss:Horizontal=\"Center\" ss:Vertical=\"Center\"/>" + "\r\n");
            sb.Append("   <Borders>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Bottom\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Left\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Right\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Top\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("   </Borders>" + "\r\n");
            sb.Append("   <Font ss:FontName=\"宋体\" x:CharSet=\"134\"/>" + "\r\n");
            sb.Append("   <Interior ss:Color=\"#D8D8D8\" ss:Pattern=\"Solid\"/>" + "\r\n");
            sb.Append("   <NumberFormat ss:Format=\"@\"/>" + "\r\n");
            sb.Append("  </Style>" + "\r\n");

            sb.Append("  <Style ss:ID=\"s69\">" + "\r\n");
            sb.Append("   <Alignment ss:Horizontal=\"Center\" ss:Vertical=\"Center\"/>" + "\r\n");
            sb.Append("   <Borders>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Bottom\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Left\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Right\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Top\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("   </Borders>" + "\r\n");
            sb.Append("   <Font ss:FontName=\"宋体\" x:CharSet=\"134\"/>" + "\r\n");
            sb.Append("   <Interior ss:Color=\"#D8D8D8\" ss:Pattern=\"Solid\"/>" + "\r\n");
            sb.Append("   <NumberFormat ss:Format=\"0.00_ \"/>" + "\r\n");
            sb.Append("  </Style>" + "\r\n");

            sb.Append("  <Style ss:ID=\"s70\">" + "\r\n");
            sb.Append("   <Alignment ss:Horizontal=\"Center\" ss:Vertical=\"Center\"/>" + "\r\n");
            sb.Append("   <Borders>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Bottom\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Left\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Right\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Top\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("   </Borders>" + "\r\n");
            sb.Append("   <Font ss:FontName=\"宋体\" x:CharSet=\"134\"/>" + "\r\n");
            sb.Append("   <Interior ss:Color=\"#D8D8D8\" ss:Pattern=\"Solid\"/>" + "\r\n");
            sb.Append("   <NumberFormat ss:Format=\"Short Date\"/>" + "\r\n");
            sb.Append("  </Style>" + "\r\n");

            sb.Append("  <Style ss:ID=\"s71\">" + "\r\n");
            sb.Append("   <Alignment ss:Horizontal=\"Center\" ss:Vertical=\"Center\"/>" + "\r\n");
            sb.Append("   <Borders>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Bottom\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Left\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Right\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Top\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("   </Borders>" + "\r\n");
            sb.Append("   <Font ss:FontName=\"宋体\" x:CharSet=\"134\"/>" + "\r\n");
            sb.Append("   <Interior ss:Color=\"#D8D8D8\" ss:Pattern=\"Solid\"/>" + "\r\n");
            sb.Append("   <NumberFormat ss:Format=\"0_ \"/>" + "\r\n");
            sb.Append("  </Style>" + "\r\n");

            //主标题行样式
            sb.Append("  <Style ss:ID=\"s72\">" + "\r\n");
            sb.Append("   <Alignment ss:Horizontal=\"Center\" ss:Vertical=\"Center\"/>" + "\r\n");
            sb.Append("   <Borders/>" + "\r\n");
            sb.Append("   <Font ss:FontName=\"宋体\" x:CharSet=\"134\"/>" + "\r\n");
            sb.Append("   <NumberFormat ss:Format=\"@\"/>" + "\r\n");
            sb.Append("  </Style>" + "\r\n");

            //小标题行样式
            sb.Append("  <Style ss:ID=\"s73\">" + "\r\n");
            sb.Append("   <Alignment ss:Horizontal=\"Center\" ss:Vertical=\"Center\"/>" + "\r\n");
            sb.Append("   <Borders/>" + "\r\n");
            sb.Append("   <Font ss:FontName=\"宋体\" x:CharSet=\"134\"/>" + "\r\n");
            sb.Append("   <NumberFormat ss:Format=\"@\"/>" + "\r\n");
            sb.Append("  </Style>" + "\r\n");

            //主标题单元格样式
            sb.Append("  <Style ss:ID=\"s74\">" + "\r\n");
            sb.Append("   <Alignment ss:Horizontal=\"Center\" ss:Vertical=\"Center\"/>" + "\r\n");
            sb.Append("   <Borders/>" + "\r\n");
            sb.Append("   <Font ss:FontName=\"宋体\" x:CharSet=\"134\" ss:Size=\"20\" ss:Bold=\"1\"/>" + "\r\n");
            sb.Append("   <NumberFormat ss:Format=\"@\"/>" + "\r\n");
            sb.Append("  </Style>" + "\r\n");

            //小标题单元格样式
            sb.Append("  <Style ss:ID=\"s75\">" + "\r\n");
            sb.Append("   <Alignment ss:Horizontal=\"Center\" ss:Vertical=\"Center\"/>" + "\r\n");
            sb.Append("   <Borders/>" + "\r\n");
            sb.Append("   <Font ss:FontName=\"宋体\" x:CharSet=\"134\" ss:Size=\"12\"/>" + "\r\n");
            sb.Append("   <NumberFormat ss:Format=\"@\"/>" + "\r\n");
            sb.Append("  </Style>" + "\r\n");

            //单元格样式（文本格式，左对齐）
            sb.Append("  <Style ss:ID=\"s76\">" + "\r\n");
            sb.Append("   <Alignment ss:Horizontal=\"Left\" ss:Vertical=\"Center\" />" + "\r\n");
            sb.Append("   <Borders>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Bottom\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Left\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Right\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("    <Border ss:Position=\"Top\" ss:LineStyle=\"Continuous\" ss:Weight=\"1\"/>" + "\r\n");
            sb.Append("   </Borders>" + "\r\n");
            sb.Append("   <Font ss:FontName=\"宋体\" x:CharSet=\"134\"/>" + "\r\n");
            sb.Append("   <NumberFormat ss:Format=\"@\"/>" + "\r\n");
            sb.Append("  </Style>" + "\r\n");

            sb.Append(" </Styles>" + "\r\n");

            sw.Write(sb);
        }


        /// <summary>
        /// 生成Sheet数据
        /// </summary>
        /// <returns></returns>
        private StringBuilder CreateSheet() {
            StringBuilder sb = new StringBuilder();


            sb.Append(CreateSheetHead(_sheetName + _sheetIndex));

            //创建表格头
            sb.Append(CreateSheetTableHead());

            int m = 0;
            if (_exportExcelInfo != null && _exportExcelInfo.Titles != null) {
                for (int i = 0; i < _exportExcelInfo.Titles.Count; i++) {
                    if (i == 0) {
                        sb.Append(CreateSheetMainTitleRow(_exportExcelInfo.Titles[i])); //创建主标题
                        m++;
                    } else {
                        sb.Append(CreateSheetSubTitleRow(_exportExcelInfo.Titles[i])); //创建副标题
                        m++;
                    }
                }
            }

            //创建列标题行
            sb.Append(CreateSheetTableColumnRow());

            if (_exportExcelInfo.Data.Rows.Count == 0) {
                sb.Append(CreateSheetTableEnd());
                sb.Append(CreateWorksheetOptions());
                sb.Append(CreateSheetEnd());
            } else {
                int rowIndex = 0;
                for (int i = 0; i < _exportExcelInfo.Data.Rows.Count; i++) {
                    //创建行头
                    sb.Append(CreateSheetTableRowHead());

                    //创建单元格
                    for (int j = 0; j < _exportExcelInfo.Data.Columns.Count; j++) {
                        string value = _exportExcelInfo.Data.Rows[i][j].ToString();
                        sb.Append(CreateSheetCell(_exportExcelInfo.Data.Columns[j], value));
                    }

                    //行尾
                    sb.Append(CreateSheetTableRowEnd());

                    //如果行超过65530就使用第二个sheet
                    if ((i + 1) % 65530 == 0) {
                        sb.Append(CreateSheetSumRow(i + m + 2)); //生成合计行
                        sb.Append(CreateSheetTableEnd());
                        sb.Append(CreateWorksheetOptions());
                        sb.Append(CreateSheetEnd());

                        if (_exportExcelInfo.Data.Rows.Count % 65530 > 0) {
                            _sheetIndex++;
                            sb.Append(CreateSheetHead(_sheetName + _sheetIndex));
                            sb.Append(CreateSheetTableHead());

                            if (_exportExcelInfo != null && _exportExcelInfo.Titles != null) {
                                for (int n = 0; n < _exportExcelInfo.Titles.Count; n++) {
                                    if (n == 0) {
                                        sb.Append(CreateSheetMainTitleRow(_exportExcelInfo.Titles[n])); //创建主标题
                                    } else {
                                        sb.Append(CreateSheetSubTitleRow(_exportExcelInfo.Titles[n])); //创建副标题                                        
                                    }
                                }
                            }

                            //创建列标题行
                            sb.Append(CreateSheetTableColumnRow());
                        }
                    }

                    rowIndex = i;
                }

                if (_exportExcelInfo.Data.Rows.Count % 65530 > 0) {
                    sb.Append(CreateSheetSumRow(rowIndex + m + 2)); //生成合计行
                    sb.Append(CreateSheetTableEnd());
                    sb.Append(CreateWorksheetOptions());
                    sb.Append(CreateSheetEnd());
                }
            }

            return sb;
        }

        private void CreateSheet(StreamWriter sw, ExportExcelInfo exportExcelInfo) {
            _exportExcelInfo = exportExcelInfo;

            //循环生成数据
            CreateSheetHead(sw, _sheetName + _sheetIndex);

            //创建表格头
            CreateSheetTableHead(sw);

            int m = 0;
            if (_exportExcelInfo != null && _exportExcelInfo.Titles != null) {
                for (int i = 0; i < _exportExcelInfo.Titles.Count; i++) {
                    if (i == 0) {
                        CreateSheetMainTitleRow(sw, _exportExcelInfo.Titles[i]); //创建主标题
                        m++;
                    } else {
                        CreateSheetSubTitleRow(sw, _exportExcelInfo.Titles[i]); //创建副标题
                        m++;
                    }
                }
            }

            //创建列标题行
            CreateSheetTableColumnRow(sw);

            //数据行高
            int? dataRowHeight = null;
            var dataRowHeightObj = _exportExcelInfo.Data.ExtendedProperties[ExcelRowStyle.DataRowHeight];
            if (dataRowHeightObj != null) {
                dataRowHeight = Convert.ToInt32(dataRowHeightObj);
            }

            //获取合并单元格数据列表
            List<ExcelMergeInfo> mergeList = null;
            var mergeRowsObj = _exportExcelInfo.Data.ExtendedProperties[ExcelRowStyle.DataRowMerge];
            if (mergeRowsObj != null) {
                mergeList = (List<ExcelMergeInfo>)mergeRowsObj;
            }

            if (_exportExcelInfo.Data.Rows.Count == 0) {
                CreateSheetTableEnd(sw);
                CreateWorksheetOptions(sw);
                CreateSheetEnd(sw);
            } else {
                int rowIndex = 0;

                bool startMerge = false; //是否开始合并单元格
                int mergeRowIndex = 0; //合并行序号
                int mergeRowCount = 0; //合并单元格行数
                ExcelMergeInfo mergeInfo = null; //合并信息

                for (int i = 0; i < _exportExcelInfo.Data.Rows.Count; i++) {
                    if (i % 10000 == 0) {
                        sw.Flush();
                    }

                    //创建行头
                    if (dataRowHeight.HasValue)
                        CreateSheetTableRowHead(sw, "ss:AutoFitHeight=\"0\" ss:Height=\"" + dataRowHeight.Value.ToString() + "\"");
                    else
                        CreateSheetTableRowHead(sw);

                    //创建单元格
                    DataRow row = _exportExcelInfo.Data.Rows[i];
                    if (mergeList != null && mergeList.Any()) {
                        if (mergeList.Where(s => s.BeginDataRow == row).Any()) {
                            mergeInfo = mergeList.Where(s => s.BeginDataRow == row).FirstOrDefault();
                            if (mergeInfo.BeginDataRow != mergeInfo.EndDataRow) {
                                startMerge = true;
                                mergeRowIndex = 0;

                                //计算合并行数
                                int n = i;
                                while (n < _exportExcelInfo.Data.Rows.Count) {
                                    if (_exportExcelInfo.Data.Rows[n] == mergeInfo.EndDataRow)
                                        break;
                                    n++;
                                }

                                mergeRowCount = n - i;
                            }
                        }
                    }

                    for (int j = 0; j < _exportExcelInfo.Data.Columns.Count; j++) {
                        DataColumn col = _exportExcelInfo.Data.Columns[j];
                        string value = row[j].ToString();

                        string cellProperties = "";
                        if (startMerge) {
                            //如果是合并的第一行
                            if (mergeRowIndex == 0) {
                                if (col == mergeInfo.Column) {
                                    cellProperties = "ss:MergeDown=\"" + mergeRowCount.ToString() + "\"";
                                }
                            }
                            //否则
                            else {
                                //需要跳过合并列
                                if (col == mergeInfo.Column) {
                                    continue;
                                }

                                //如果当前列是合并列的下一列
                                int colIndex = _exportExcelInfo.Data.Columns.IndexOf(col);
                                if (colIndex == _exportExcelInfo.Data.Columns.IndexOf(mergeInfo.Column) + 1) {
                                    cellProperties = "ss:Index=\"" + (colIndex + 1).ToString() + "\"";
                                }
                            }
                        }

                        CreateSheetCell(sw, col, value, cellProperties);
                    }


                    if (mergeInfo != null) {
                        mergeRowIndex++;

                        if (mergeInfo.EndDataRow == row) {
                            startMerge = false;
                            mergeInfo = null;
                            mergeRowIndex = 0;
                            mergeRowCount = 0;
                        }
                    }

                    //行尾
                    CreateSheetTableRowEnd(sw);

                    //如果行超过65530就使用第二个sheet
                    if ((i + 1) % 65530 == 0) {
                        CreateSheetSumRow(sw, i + m + 2); //生成合计行
                        CreateSheetTableEnd(sw);
                        CreateWorksheetOptions(sw);
                        CreateSheetEnd(sw);

                        if (_exportExcelInfo.Data.Rows.Count % 65530 > 0) {
                            _sheetIndex++;
                            CreateSheetHead(sw, _sheetName + _sheetIndex);
                            CreateSheetTableHead(sw);

                            if (_exportExcelInfo != null && _exportExcelInfo.Titles != null) {
                                for (int n = 0; n < _exportExcelInfo.Titles.Count; n++) {
                                    if (n == 0) {
                                        CreateSheetMainTitleRow(sw, _exportExcelInfo.Titles[n]); //创建主标题                                        
                                    } else {
                                        CreateSheetSubTitleRow(sw, _exportExcelInfo.Titles[n]); //创建副标题                                        
                                    }
                                }
                            }

                            //创建列标题行
                            CreateSheetTableColumnRow(sw);
                        }
                    }

                    rowIndex = i;
                }

                if (_exportExcelInfo.Data.Rows.Count % 65530 > 0) {
                    CreateSheetSumRow(sw, (rowIndex % 65530) + m + 2); //生成合计行
                    CreateSheetTableEnd(sw);
                    CreateWorksheetOptions(sw);
                    CreateSheetEnd(sw);
                }
            }

        }


        private string CreateWorksheetOptions() {
            StringBuilder sb = new StringBuilder();
            sb.Append("  <WorksheetOptions xmlns=\"urn:schemas-microsoft-com:office:excel\">" + "\r\n");
            sb.Append("   <PageSetup>" + "\r\n");
            sb.Append("    <Header x:Margin=\"0.3\"/>" + "\r\n");
            sb.Append("    <Footer x:Margin=\"0.3\"/>" + "\r\n");
            sb.Append("    <PageMargins x:Bottom=\"0.75\" x:Left=\"0.7\" x:Right=\"0.7\" x:Top=\"0.75\"/>" + "\r\n");
            sb.Append("   </PageSetup>" + "\r\n");
            sb.Append("   <Unsynced/>" + "\r\n");
            sb.Append("   <Selected/>" + "\r\n");
            sb.Append("   <ProtectObjects>False</ProtectObjects>" + "\r\n");
            sb.Append("   <ProtectScenarios>False</ProtectScenarios>" + "\r\n");
            sb.Append("  </WorksheetOptions>" + "\r\n");

            return sb.ToString();
        }

        private void CreateWorksheetOptions(StreamWriter sw) {
            StringBuilder sb = new StringBuilder();
            sb.Append("  <WorksheetOptions xmlns=\"urn:schemas-microsoft-com:office:excel\">" + "\r\n");
            sb.Append("   <PageSetup>" + "\r\n");
            sb.Append("    <Header x:Margin=\"0.3\"/>" + "\r\n");
            sb.Append("    <Footer x:Margin=\"0.3\"/>" + "\r\n");
            sb.Append("    <PageMargins x:Bottom=\"0.75\" x:Left=\"0.7\" x:Right=\"0.7\" x:Top=\"0.75\"/>" + "\r\n");
            sb.Append("   </PageSetup>" + "\r\n");
            sb.Append("   <Unsynced/>" + "\r\n");
            sb.Append("   <Selected/>" + "\r\n");
            sb.Append("   <ProtectObjects>False</ProtectObjects>" + "\r\n");
            sb.Append("   <ProtectScenarios>False</ProtectScenarios>" + "\r\n");
            sb.Append("  </WorksheetOptions>" + "\r\n");

            sw.Write(sb);
        }


        /// <summary>
        /// 生成Sheet头
        /// </summary>
        /// <param name="sheetName"></param>
        /// <returns></returns>
        private string CreateSheetHead(string sheetName) {
            return " <Worksheet ss:Name=\"" + sheetName + "\">" + "\r\n";
        }

        private void CreateSheetHead(StreamWriter sw, string sheetName) {
            sw.Write(" <Worksheet ss:Name=\"" + sheetName + "\">" + "\r\n");
        }


        /// <summary>
        /// 生成表格头
        /// </summary>
        /// <returns></returns>
        private string CreateSheetTableHead() {
            StringBuilder sb = new StringBuilder();
            sb.Append("  <Table ss:StyleID=\"s64\" ss:DefaultColumnWidth=\"54\">" + "\r\n");

            int i = 0;
            //遍历列名称
            foreach (DataColumn dc in _exportExcelInfo.Data.Columns) {
                decimal width = 54;

                if (dc.ExtendedProperties.ContainsKey("width")) {
                    width = Convert.ToInt32(dc.ExtendedProperties["width"].ToString());
                }

                Type dataType = dc.DataType;

                if (dataType == typeof(int) || dataType == typeof(Int64) || dataType == typeof(Int32) || dataType == typeof(Int16)) {
                    sb.Append("   <Column ss:Index=\"" + (i + 1) + "\" ss:StyleID=\"s67\" ss:Width=\"" +
                              width + "\"/>" + "\r\n");
                } else if (dataType == typeof(double) || dataType == typeof(decimal)) {
                    sb.Append("   <Column ss:Index=\"" + (i + 1) + "\" ss:StyleID=\"s65\" ss:Width=\"" +
                              width + "\"/>" + "\r\n");
                } else if (dataType == typeof(DateTime)) {
                    sb.Append("   <Column ss:Index=\"" + (i + 1) + "\" ss:StyleID=\"s66\" ss:Width=\"" +
                              width + "\"/>" + "\r\n");
                } else {
                    string styleId = "s64";
                    if (dc.ExtendedProperties.ContainsKey(ExcelCellStyle.HorizontalAlignment) &&
                        (ExcelCellStyleHorizontalAlignment)dc.ExtendedProperties[ExcelCellStyle.HorizontalAlignment] ==
                        ExcelCellStyleHorizontalAlignment.Left) {
                        styleId = "s76";
                    }

                    sb.Append("   <Column ss:Index=\"" + (i + 1) + "\" ss:StyleID=\"" + styleId +
                              "\" ss:Width=\"" + width + "\"/>" + "\r\n");
                }

                i++;
            }



            return sb.ToString();
        }

        private void CreateSheetTableHead(StreamWriter sw) {
            StringBuilder sb = new StringBuilder();
            sb.Append("  <Table ss:StyleID=\"s64\" ss:DefaultColumnWidth=\"54\">" + "\r\n");

            int i = 0;
            //遍历列名称
            foreach (DataColumn dc in _exportExcelInfo.Data.Columns) {
                decimal width = 54;

                if (dc.ExtendedProperties.ContainsKey("width")) {
                    width = Convert.ToInt32(dc.ExtendedProperties["width"].ToString());
                }

                Type dataType = dc.DataType;

                if (dataType == typeof(int)) {
                    sb.Append("   <Column ss:Index=\"" + (i + 1) + "\" ss:StyleID=\"s67\" ss:Width=\"" +
                              width + "\"/>" + "\r\n");
                } else if (dataType == typeof(double) || dataType == typeof(decimal)) {
                    sb.Append("   <Column ss:Index=\"" + (i + 1) + "\" ss:StyleID=\"s65\" ss:Width=\"" +
                              width + "\"/>" + "\r\n");
                } else if (dataType == typeof(DateTime)) {
                    sb.Append("   <Column ss:Index=\"" + (i + 1) + "\" ss:StyleID=\"s66\" ss:Width=\"" +
                              width + "\"/>" + "\r\n");
                } else {
                    string styleId = "s64";
                    if (dc.ExtendedProperties.ContainsKey("text-align") &&
                        dc.ExtendedProperties["text-align"].ToString() == "left") {
                        styleId = "s76";
                    }

                    sb.Append("   <Column ss:Index=\"" + (i + 1) + "\" ss:StyleID=\"" + styleId +
                              "\" ss:Width=\"" + width + "\"/>" + "\r\n");
                }

                i++;
            }

            sw.Write(sb);
        }


        /// <summary>
        /// 创建主标题行
        /// </summary>
        /// <returns></returns>
        private string CreateSheetMainTitleRow(string title) {
            double height = 35.0625;

            StringBuilder sb = new StringBuilder();
            sb.Append(CreateSheetTableRowHead("ss:AutoFitHeight=\"0\" ss:Height=\"" + height.ToString() + "\" ss:StyleID=\"s72\""));
            sb.Append("    <Cell ss:MergeAcross=\"" + (_exportExcelInfo.Data.Columns.Count - 1) +
                      "\"  ss:StyleID=\"s74\"><Data ss:Type=\"String\">" + title + "</Data></Cell>");
            sb.Append(CreateSheetTableRowEnd());

            return sb + "\r\n";
        }

        private void CreateSheetMainTitleRow(StreamWriter sw, string title) {
            double height = 35.0625;

            StringBuilder sb = new StringBuilder();
            sb.Append(CreateSheetTableRowHead("ss:AutoFitHeight=\"0\" ss:Height=\"" + height.ToString() + "\" ss:StyleID=\"s72\""));
            sb.Append("    <Cell ss:MergeAcross=\"" + (_exportExcelInfo.Data.Columns.Count - 1) +
                      "\"  ss:StyleID=\"s74\"><Data ss:Type=\"String\">" + title + "</Data></Cell>");
            sb.Append(CreateSheetTableRowEnd());

            sw.Write(sb + "\r\n");
        }


        /// <summary>
        /// 创建副标题行
        /// </summary>
        /// <returns></returns>
        private string CreateSheetSubTitleRow(string title) {
            StringBuilder sb = new StringBuilder();
            sb.Append(CreateSheetTableRowHead("ss:AutoFitHeight=\"0\" ss:Height=\"24.9375\" ss:StyleID=\"s73\""));
            sb.Append("    <Cell ss:MergeAcross=\"" + (_exportExcelInfo.Data.Columns.Count - 1) +
                      "\" ss:StyleID=\"s75\"><Data ss:Type=\"String\">" + title + "</Data></Cell>");
            sb.Append(CreateSheetTableRowEnd());

            return sb + "\r\n";
        }

        private void CreateSheetSubTitleRow(StreamWriter sw, string title) {
            CreateSheetTableRowHead(sw, "ss:AutoFitHeight=\"0\" ss:Height=\"24.9375\" ss:StyleID=\"s73\"");
            sw.Write("    <Cell ss:MergeAcross=\"" + (_exportExcelInfo.Data.Columns.Count - 1) +
                     "\" ss:StyleID=\"s75\"><Data ss:Type=\"String\">" + title + "</Data></Cell>");
            CreateSheetTableRowEnd(sw);
        }


        /// <summary>
        /// 创建表格列标题行
        /// </summary>
        /// <returns></returns>
        private string CreateSheetTableColumnRow() {
            StringBuilder sb = new StringBuilder();

            sb.Append("   <Row ss:AutoFitHeight=\"0\" ss:Height=\"25\" ss:StyleID=\"s62\">" + "\r\n");
            for (int i = 0; i < _exportExcelInfo.Data.Columns.Count; i++) {
                sb.Append("    <Cell><Data ss:Type=\"String\">" + _exportExcelInfo.Data.Columns[i].ColumnName + "</Data></Cell>" + "\r\n");
            }

            sb.Append("   </Row>" + "\r\n");

            return sb.ToString();
        }

        /// <summary>
        /// 创建列标题行
        /// </summary>
        /// <param name="sw"></param>
        private void CreateSheetTableColumnRow(StreamWriter sw) {
            StringBuilder sb = new StringBuilder();

            sb.Append("   <Row ss:AutoFitHeight=\"0\" ss:Height=\"25\" ss:StyleID=\"s62\">" + "\r\n");
            for (int i = 0; i < _exportExcelInfo.Data.Columns.Count; i++) {
                sb.Append("    <Cell><Data ss:Type=\"String\">" + _exportExcelInfo.Data.Columns[i].ColumnName + "</Data></Cell>" + "\r\n");
            }

            sb.Append("   </Row>" + "\r\n");

            sw.Write(sb);
        }


        /// <summary>
        /// 生成表格行头
        /// </summary>
        /// <returns></returns>
        private string CreateSheetTableRowHead(string rowStyle = "ss:AutoFitHeight=\"1\"") {
            return "   <Row " + rowStyle + ">" + "\r\n";
        }

        private void CreateSheetTableRowHead(StreamWriter sw, string rowStyle = "ss:AutoFitHeight=\"1\"") {
            sw.Write("   <Row " + rowStyle + ">" + "\r\n");
        }


        /// <summary>
        /// 生成表格行尾
        /// </summary>
        /// <returns></returns>
        private string CreateSheetTableRowEnd() {
            return "   </Row>" + "\r\n";
        }

        private void CreateSheetTableRowEnd(StreamWriter sw) {
            sw.Write("   </Row>" + "\r\n");
        }


        /// <summary>
        /// 生成单元格
        /// </summary>
        /// <param name="col"></param>
        /// <param name="value">值</param>
        /// <param name="properties"></param>
        /// <returns></returns>
        private string CreateSheetCell(DataColumn col, string value, string properties = "") {
            Type dateType = col.DataType;

            string cell;

            if (value == null) {
                value = "";
            }

            if (dateType == typeof(int)) {
                if (value == "") {
                    cell = "    <Cell " + properties + " ss:StyleID=\"s67\"><Data ss:Type=\"String\"></Data></Cell>";
                } else {
                    cell = "    <Cell " + properties + "  ss:StyleID=\"s67\"><Data ss:Type=\"Number\">" + value +
                           "</Data></Cell>";
                }
            } else if (dateType == typeof(decimal) || dateType == typeof(double)) {
                if (value == "") {
                    cell = "    <Cell " + properties + "  ss:StyleID=\"s65\"><Data ss:Type=\"String\"></Data></Cell>";
                } else {
                    cell = "    <Cell " + properties + "  ss:StyleID=\"s65\"><Data ss:Type=\"Number\">" + value +
                           "</Data></Cell>";
                }
            } else if (dateType == typeof(DateTime)) {
                if (value == "") {
                    cell = "    <Cell " + properties + "  ss:StyleID=\"s66\"><Data ss:Type=\"String\"></Data></Cell>";
                } else {
                    cell = "    <Cell " + properties + "  ss:StyleID=\"s66\"><Data ss:Type=\"DateTime\">" +
                           Convert.ToDateTime(value).ToString("yyyy-MM-ddTHH:mm:ss.fff") + "</Data></Cell>";
                }
            } else {
                string styleId = "s64";
                if (col.ExtendedProperties.ContainsKey("text-align") && col.ExtendedProperties["text-align"].ToString() == "left") {
                    styleId = "s76";
                }

                if (value == "") {
                    cell = "    <Cell " + properties + "  ss:StyleID=\"" + styleId +
                           "\"><Data ss:Type=\"String\"></Data></Cell>";
                } else {
                    cell = "    <Cell " + properties + "  ss:StyleID=\"" + styleId + "\"><Data ss:Type=\"String\">" +
                           XMLFormat(value) + "</Data></Cell>";
                }
            }

            return cell + "\r\n";
        }

        /// <summary>
        /// 生成单元格
        /// </summary>
        /// <param name="sw"></param>
        /// <param name="col"></param>
        /// <param name="row"></param>
        /// <param name="properties"></param>
        private void CreateSheetCell(StreamWriter sw, DataColumn col, string value, string properties = "") {
            Type dateType = col.DataType;

            string cell;

            value = (value ?? "");

            if (dateType == typeof(int)) {
                if (value == "") {
                    cell = "    <Cell " + properties + " ss:StyleID=\"s67\"><Data ss:Type=\"String\"></Data></Cell>";
                } else {
                    cell = "    <Cell " + properties + "  ss:StyleID=\"s67\"><Data ss:Type=\"Number\">" + value +
                           "</Data></Cell>";
                }
            } else if (dateType == typeof(decimal) || dateType == typeof(double)) {
                if (value == "") {
                    cell = "    <Cell " + properties + "  ss:StyleID=\"s65\"><Data ss:Type=\"String\"></Data></Cell>";
                } else {
                    cell = "    <Cell " + properties + "  ss:StyleID=\"s65\"><Data ss:Type=\"Number\">" + value +
                           "</Data></Cell>";
                }
            } else if (dateType == typeof(DateTime)) {
                if (value == "") {
                    cell = "    <Cell " + properties + "  ss:StyleID=\"s66\"><Data ss:Type=\"String\"></Data></Cell>";
                } else {
                    cell = "    <Cell " + properties + "  ss:StyleID=\"s66\"><Data ss:Type=\"DateTime\">" +
                           Convert.ToDateTime(value).ToString("yyyy-MM-ddTHH:mm:ss.fff") + "</Data></Cell>";
                }
            } else {
                string styleId = "s64";
                if (col.ExtendedProperties.ContainsKey(ExcelCellStyle.HorizontalAlignment) &&
                    (ExcelCellStyleHorizontalAlignment)col.ExtendedProperties[ExcelCellStyle.HorizontalAlignment] ==
                    ExcelCellStyleHorizontalAlignment.Left) {
                    styleId = "s76";
                }

                if (value == "") {
                    cell = "    <Cell " + properties + "  ss:StyleID=\"" + styleId +
                           "\"><Data ss:Type=\"String\"></Data></Cell>";
                } else {
                    cell = "    <Cell " + properties + "  ss:StyleID=\"" + styleId + "\"><Data ss:Type=\"String\">" +
                           XMLFormat(value) + "</Data></Cell>";
                }
            }

            sw.Write(cell + "\r\n");
        }


        /// <summary>
        /// 创建合计行
        /// </summary>
        /// <param name="rowIndex"></param>
        /// <returns></returns>
        private string CreateSheetSumRow(int rowIndex) {
            if (_exportExcelInfo.SumDataColumnList == null || !_exportExcelInfo.SumDataColumnList.Any())
                return "";

            StringBuilder sb = new StringBuilder();

            //创建行头
            sb.Append(CreateSheetTableRowHead("ss:AutoFitHeight=\"0\" ss:Height=\"25\" ss:StyleID=\"s68\""));

            //加入“合计”文字
            sb.Append("    <Cell ss:StyleID=\"s68\"><Data ss:Type=\"String\">合计</Data></Cell>" + "\r\n");

            foreach (DataColumn col in _exportExcelInfo.SumDataColumnList) {
                int index = _exportExcelInfo.Data.Columns.IndexOf(col) + 1;

                string style;
                if (col.DataType == typeof(int) || col.DataType == typeof(Int16) || col.DataType == typeof(Int32) || col.DataType == typeof(Int64)) {
                    style = "ss:StyleID=\"s71\"";
                } else if (col.DataType == typeof(double) || col.DataType == typeof(decimal) || col.DataType == typeof(float)) {
                    style = "ss:StyleID=\"s69\"";
                } else if (col.DataType == typeof(DateTime)) {
                    style = "ss:StyleID=\"s70\"";
                } else {
                    style = "ss:StyleID=\"s68\"";
                }

                sb.Append("    <Cell " + style + " ss:Index=\"" + index + "\" ss:Formula=\"=SUM(R[-" +
                          (rowIndex - 1) + "]C:R[-1]C)\"><Data ss:Type=\"Number\"></Data></Cell>" + "\r\n");
            }

            //行尾
            sb.Append(CreateSheetTableRowEnd());

            return sb.ToString();
        }

        private void CreateSheetSumRow(StreamWriter sw, int rowIndex) {
            if (_exportExcelInfo.SumDataColumnList == null || !_exportExcelInfo.SumDataColumnList.Any())
                return;


            //创建行头
            CreateSheetTableRowHead(sw, "ss:AutoFitHeight=\"0\" ss:Height=\"25\" ss:StyleID=\"s68\"");

            StringBuilder sb = new StringBuilder();


            //加入“合计”文字
            sb.Append("    <Cell ss:StyleID=\"s68\"><Data ss:Type=\"String\">合计</Data></Cell>" + "\r\n");

            foreach (DataColumn col in _exportExcelInfo.SumDataColumnList) {
                int index = _exportExcelInfo.Data.Columns.IndexOf(col) + 1;

                string style;
                if (col.DataType == typeof(int)) {
                    style = "ss:StyleID=\"s71\"";
                } else if (col.DataType == typeof(double) || col.DataType == typeof(decimal)) {
                    style = "ss:StyleID=\"s69\"";
                } else if (col.DataType == typeof(DateTime)) {
                    style = "ss:StyleID=\"s70\"";
                } else {
                    style = "ss:StyleID=\"s68\"";
                }

                sb.Append("    <Cell " + style + " ss:Index=\"" + index + "\" ss:Formula=\"=SUM(R[-" +
                          (rowIndex - 1 - (_exportExcelInfo.Titles != null ? _exportExcelInfo.Titles.Count : 0)) + "]C:R[-1]C)\"><Data ss:Type=\"Number\"></Data></Cell>" + "\r\n");
            }

            sw.Write(sb);

            //行尾
            CreateSheetTableRowEnd(sw);
        }


        /// <summary>
        /// 生成表格尾
        /// </summary>
        /// <returns></returns>
        private string CreateSheetTableEnd() {
            return "  </Table>" + "\r\n";
        }

        private void CreateSheetTableEnd(StreamWriter sw) {
            sw.Write("  </Table>" + "\r\n");
        }


        /// <summary>
        /// 生成Sheet尾
        /// </summary>
        /// <returns></returns>
        private string CreateSheetEnd() {
            return " </Worksheet>" + "\r\n";
        }

        private void CreateSheetEnd(StreamWriter sw) {
            sw.Write(" </Worksheet>" + "\r\n");
        }


        /// <summary>
        /// 生成XML文件尾
        /// </summary>
        /// <returns></returns>
        private string CreateEnd() {
            return "</Workbook>" + "\r\n\r\n";
        }

        private void CreateEnd(StreamWriter sw) {
            sw.Write("</Workbook>" + "\r\n\r\n");
        }


        /// <summary>
        /// 获取DataTable列的最大数据长度
        /// </summary>
        /// <param name="column"></param>
        /// <returns></returns>
        public int GetColumnValueMaxLength(DataColumn column) {
            int len = 0;
            var query = _exportExcelInfo.Data.AsEnumerable().Select(s => s.ItemArray[_exportExcelInfo.Data.Columns.IndexOf(column)]).ToList();
            var query1 = query.Select(s => s == null ? 0 : Encoding.Default.GetBytes(s.ToString()).Length).ToArray();
            if (query1.Any()) {
                len = query1.Max(s => s);
            }

            return len;
        }

        /// <summary>
        /// 转义字符成xml识别的格式
        /// </summary>
        /// <param name="source"></param>
        /// <returns></returns>
        private string XMLFormat(string source) {
            return source.Replace("&", "&amp;").Replace("<", "&lt;").Replace(">", "&gt;").Replace("\"", "&quot;")
                .Replace("'", "&apos;").Replace("\r", "&#13;").Replace("\n", "&#10;");
        }

        ///// <summary>
        ///// 用于excel表格中列号字母转成列索引，从1对应A开始
        ///// </summary>
        ///// <param name="column">列号</param>
        ///// <returns>列索引</returns>
        //private int ColumnToIndex(string column)
        //{
        //    if (!Regex.IsMatch(column.ToUpper(), @"[A-Z]+"))
        //    {
        //        throw new Exception("Invalid parameter");
        //    }

        //    int index = 0;
        //    char[] chars = column.ToUpper().ToCharArray();
        //    for (int i = 0; i < chars.Length; i++)
        //    {
        //        index += ((int) chars[i] - (int) 'A' + 1) * (int) Math.Pow(26, chars.Length - i - 1);
        //    }

        //    return index;
        //}

        ///// <summary>
        ///// 用于将excel表格中列索引转成列号字母，从A对应1开始
        ///// </summary>
        ///// <param name="index">列索引</param>
        ///// <returns>列号</returns>
        //private string IndexToColumn(int index)
        //{
        //    if (index <= 0)
        //    {
        //        throw new Exception("Invalid parameter");
        //    }

        //    index--;
        //    string column = string.Empty;
        //    do
        //    {
        //        if (column.Length > 0)
        //        {
        //            index--;
        //        }

        //        column = ((char) (index % 26 + (int) 'A')) + column;
        //        index = (int) ((index - index % 26) / 26);
        //    } while (index > 0);

        //    return column;
        //}

        //private byte[] StreamToBytes(Stream stream)
        //{
        //    byte[] bytes = new byte[stream.Length];
        //    stream.Read(bytes, 0, bytes.Length);
        //    // 设置当前流的位置为流的开始
        //    stream.Seek(0, SeekOrigin.Begin);
        //    return bytes;
        //}

        private Stream BytesToStream(byte[] bytes) {
            Stream stream = new MemoryStream(bytes);
            return stream;
        }

        private string FormatSheetCellValue(Type dateType, string value) {
            if (value == null)
                value = "";

            if (dateType == typeof(int))
                return string.IsNullOrEmpty(value) ? "" : value;

            if (dateType == typeof(decimal) || dateType == typeof(double))
                return string.IsNullOrEmpty(value) ? "" : value;

            if (dateType == typeof(DateTime))
                return string.IsNullOrEmpty(value) ? "" : Convert.ToDateTime(value).ToString("yyyy-MM-ddTHH:mm:ss.fff");

            return string.IsNullOrEmpty(value) ? "" : XMLFormat(value);

        }

    }

    /// <summary>
    /// 导出Excel数据信息类
    /// </summary>
    public class ExportExcelInfo {
        /// <summary>
        /// 需要导出的数据
        /// </summary>
        public DataTable Data { get; set; }

        /// <summary>
        /// 工作表名称
        /// </summary>
        public string SheetName { get; set; }

        /// <summary>
        /// 标题列表，一行一条
        /// </summary>
        public List<string> Titles { get; set; }

        /// <summary>
        /// 合计列列表
        /// </summary>
        public List<DataColumn> SumDataColumnList { get; set; }

    }

    /// <summary>
    /// Excel表格样式
    /// </summary>
    public enum ExcelCellStyle {
        /// <summary>
        /// 水平对齐方式
        /// </summary>
        HorizontalAlignment,

    }

    /// <summary>
    /// 水平对齐方式
    /// </summary>
    public enum ExcelCellStyleHorizontalAlignment {
        /// <summary>
        /// 左对齐
        /// </summary>
        Left,

        /// <summary>
        /// 居中对齐
        /// </summary>
        Center,

        /// <summary>
        /// 右对齐
        /// </summary>
        Right
    }

    /// <summary>
    /// 表格行属性样式
    /// </summary>
    public enum ExcelRowStyle {
        /// <summary>
        /// 数据行高
        /// </summary>
        DataRowHeight,

        /// <summary>
        /// 合并行
        /// </summary>
        DataRowMerge
    }

    /// <summary>
    /// 合并表格信息
    /// </summary>
    public class ExcelMergeInfo {
        /// <summary>
        /// 指定合并的列
        /// </summary>
        public DataColumn Column { get; set; }

        /// <summary>
        /// 开始行
        /// </summary>
        public DataRow BeginDataRow { get; set; }

        /// <summary>
        /// 结束行
        /// </summary>
        public DataRow EndDataRow { get; set; }
    }
}
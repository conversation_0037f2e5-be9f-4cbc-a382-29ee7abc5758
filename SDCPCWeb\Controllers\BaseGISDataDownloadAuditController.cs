﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using SDCPCWeb.Extensions;
using SDCPCWeb.Models;
using SDCPCWeb.Models.BusinessContent;
using SDCPCWeb.Models.BusinessContent.GuiHuaDingDian;
using SDCPCWeb.Models.BusinessFlow;
using SDCPCWeb.Models.CompanyRegister;
using SDCPCWeb.Models.Result;
using SDCPCWeb.Services;

namespace SDCPCWeb.Controllers {
    /// <summary>
    /// 基础数据下载审核
    /// </summary>
    [RoutePrefix("internal/BaseGISDataDownloadAudit"), SDCAuthorize(SDCAuthorizeRole.xCloud)]
    public class BaseGISDataDownloadAuditController : ApiController {
        private readonly OracleDataService service = new OracleDataService();
        private UserInfo UserInfo => Request.Properties.ContainsKey("SDC-UserInfo") ? (UserInfo)Request.Properties["SDC-UserInfo"] : null;

        /// <summary>
        /// 获取审核业务列表，带分页
        /// </summary>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpGet, Route("GetBusinessList")]
        public PageResult<BusinessBaseInfo> GetBusinessList(int pageIndex = 1, int pageSize = 20) {
            var where = "STATECODE IN(0,1) AND BUSINESSCLASS='BaseSurveyDataDownloadFlow' AND EXISTS(SELECT 1 FROM BUSINESSLINKINFO WHERE BUSINESSID=BUSINESSBASEINFO.ID AND STATECODE IN (0,1) AND ACTIONID=2)" +
                        " AND EXISTS(SELECT 1 FROM BaseGISData_ContentInfo WHERE ID=BusinessBaseInfo.ID AND DownLoadState=0)";
            var result = service.GetPagerList<BusinessBaseInfo>(pageIndex, pageSize, where, "CreateTime");
            var total = service.GetTotalCountWithSql($"SELECT 1 FROM BusinessBaseInfo WHERE {where}");

            return new PageResult<BusinessBaseInfo>() {
                DataTable = result,
                Page = new Page() {
                    PageSize = pageSize,
                    PageIndex = pageIndex,
                    Total = total
                }
            };
        }

        /// <summary>
        /// 获取业务详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("GetProjectById")]
        public BaseSurveyDataDownloadProject GetProjectById(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return null;
            }

            return BaseSurveyDataDownloadProject.GetByBusinessID(id);
        }

        /// <summary>
        /// 签收审核业务
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost, Route("SignBusiness")]
        public ApiResult SignBusiness(string id) {
            var project = BaseSurveyDataDownloadProject.GetByBusinessID(id);
            if (project.ContentInfo == null) {
                return new ApiResult() { StateCode = 0, Message = "签收失败，找不到业务" };
            }
            project.ContentInfo.DownLoadState = 3;
            service.UpdateOne(BaseGISData_ContentInfo.FromModel(project.ContentInfo));
            Request.WriteSDCLog("测绘数据申请", $"后台签收业务 >> 业务ID：{project.BaseInfo.ID} >> 申请单位：{project.BaseInfo.DeveloperName}");
            return new ApiResult() { StateCode = 1, Message = "签收完成" };

        }

        /// <summary>
        /// 通过审核
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost, Route("AcceptBusiness")]
        public ApiResult AcceptBusiness(string id) {
            var project = BaseSurveyDataDownloadProject.GetByBusinessID(id);
            if (project.ContentInfo == null) {
                return new ApiResult() { StateCode = 0, Message = "操作失败，找不到业务" };
            }
            Request.WriteSDCLog("测绘数据申请", $"通过审核 >> 业务ID：{project.BaseInfo.ID} >> 申请单位：{project.BaseInfo.DeveloperName}");
            var result = new EPSApiController().PostEpsDownLoadResult(id);

            return result;
        }

        /// <summary>
        /// 驳回申请，关闭业务
        /// </summary>
        /// <param name="id"></param>
        /// <param name="reason"></param>
        /// <returns></returns>
        [HttpPost, Route("RejectBusiness")]
        public ApiResult RejectBusiness(string id, string reason) {
            var project = BaseSurveyDataDownloadProject.GetByBusinessID(id);
            if (project.ContentInfo == null) {
                return new ApiResult() { StateCode = 0, Message = "驳回失败，找不到业务" };
            }
            //整条业务关闭掉
            BaseSurveyDataDownloadProject.Close(project, reason);
            Request.WriteSDCLog("测绘数据申请", $"驳回申请 >> 业务ID：{project.BaseInfo.ID} >> 申请单位：{project.BaseInfo.DeveloperName}");
            return new ApiResult() { StateCode = 1, Message = "驳回完成" };
        }

        /// <summary>
        /// 获取拨地定桩项目范围审核列表
        /// </summary>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpGet, Route("GetMarkPointCheckScopeList")]
        public PageResult<BusinessBaseInfo> GetMarkPointCheckScopeList(int pageIndex = 1, int pageSize = 20) {
            var where = "STATECODE IN(0,1) AND BUSINESSCLASS='MarkPointSurveyFlow'\n" +
                        " AND EXISTS(SELECT 1 FROM MarkPointSurveyContentInfo WHERE ID=BusinessBaseInfo.ID AND DataCheckState=3)";
            var result = service.GetPagerList<BusinessBaseInfo>(pageIndex, pageSize, where, "CreateTime");
            var total = service.GetTotalCountWithSql($"SELECT 1 FROM BusinessBaseInfo WHERE {where}");

            return new PageResult<BusinessBaseInfo>() {
                DataTable = result,
                Page = new Page() {
                    PageSize = pageSize,
                    PageIndex = pageIndex,
                    Total = total
                }
            };
        }


        /// <summary>
        /// 项目范围通过审核
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost, Route("AcceptProjectScope")]
        public ApiResult AcceptProjectScope(string id) {
            var project = MarkPointSurveyProject.GetByBusinessID(id);
            if (project.ContentInfo == null) {
                return new ApiResult() { StateCode = 0, Message = "操作失败，找不到业务" };
            }
            var result = new EPSApiController().PostEpsDownLoadResult(id);

            Request.WriteSDCLog("拨地定桩", $"项目范围通过审核 >> 业务ID：{project.BaseInfo.ID} >> 申请单位：{project.BaseInfo.DeveloperName}");
            return result;
        }

        /// <summary>
        /// 驳回项目范围
        /// </summary>
        /// <param name="id"></param>
        /// <param name="reason"></param>
        /// <returns></returns>
        [HttpPost, Route("RejectProjectScope")]
        public ApiResult RejectProjectScope(string id, string reason) {
            var project = MarkPointSurveyProject.GetByBusinessID(id);
            if (project.ContentInfo == null) {
                return new ApiResult() { StateCode = 0, Message = "操作失败，找不到业务" };
            }

            project.ContentInfo.DataCheckState = 2;
            project.ContentInfo.FailedReason = reason;
            service.UpdateOne(MarkPointSurveyContentInfo.FromModel(project.ContentInfo));
            Request.WriteSDCLog("拨地定桩", $"驳回项目范围 >> 业务ID：{project.BaseInfo.ID} >> 申请单位：{project.BaseInfo.DeveloperName}");
            return new ApiResult() { StateCode = 1, Message = "驳回完成" };
        }


    }
}

﻿using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using System.Web.Http.Controllers;

namespace SDCPCWeb.Models {
    /// <summary>
    /// 数据中台验证
    /// </summary>
    public class SJZTAuthorizeAttribute : AuthorizeAttribute {
        protected override bool IsAuthorized(HttpActionContext actionContext) {
            return Authorized(actionContext);
        }

        internal static bool Authorized(HttpActionContext actionContext) {
            if (actionContext.Request.Headers.TryGetValues("ClientId", out IEnumerable<string> clientId) == false) {
                return false;
            }

            if (actionContext.Request.Headers.TryGetValues("Token", out IEnumerable<string> token) == false) {
                return false;
            }

            if (actionContext.Request.Headers.TryGetValues("TimeKey", out IEnumerable<string> timeKey) == false) {
                return false;
            }

            if (clientId.FirstOrDefault() == "eff7521a46fb486f8640c8b33ffbde7a"
                && token.FirstOrDefault() == "NNSJZTAuthorize"
                && !string.IsNullOrWhiteSpace(timeKey.FirstOrDefault())) {
                return true;
            }

            return false;
        }
    }
}
﻿using SDCPCWeb.Models.Result;
using SDCPCWeb.Models.System;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace SDCPCWeb.Models.Share {
    /// <summary>
    /// 共享响应报文体
    /// </summary>
    public class ShareResponse {
        /// <summary>
        /// 响应身份标识
        /// </summary>
        public string ResponseIdentity { get; set; } = SystemConfig.LocalIdentity;
        /// <summary>
        /// 请求源，解密失败或者反序列化失败时为null
        /// </summary>
        public ShareRequest Request;
        /// <summary>
        /// 响应结果
        /// </summary>
        public ApiResult Response;
        /// <summary>
        /// 转换为JSON
        /// </summary>
        /// <returns></returns>
        protected internal string ToJson() {
            return JsonConvert.SerializeObject(this,
                new IsoDateTimeConverter() { DateTimeFormat = "yyyy-MM-dd HH:mm:ss" });
        }
    }
}
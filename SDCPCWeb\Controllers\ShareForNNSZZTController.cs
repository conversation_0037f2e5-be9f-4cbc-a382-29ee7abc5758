﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Configuration;
using System.Web.Http;
using Oracle.ManagedDataAccess.Client;
using SDCPCWeb.Extensions;
using SDCPCWeb.Helpers;
using SDCPCWeb.Models;
using SDCPCWeb.Models.Attachment;
using SDCPCWeb.Models.BusinessContent;
using SDCPCWeb.Models.BusinessContent.GuiHuaDingDian;
using SDCPCWeb.Models.BusinessContent.GuiHuaHeShi;
using SDCPCWeb.Models.BusinessContent.YanXian;
using SDCPCWeb.Models.BusinessFlow;
using SDCPCWeb.Models.CompanyRegister;
using SDCPCWeb.Models.Result;
using SDCPCWeb.Services;

namespace SDCPCWeb.Controllers {

    /// <summary>
    /// 共享给南宁数据中台接口
    /// </summary>
    [RoutePrefix("sdcapi/nnszzt")]
    [SJZTAuthorize]
    public class ShareForNNSZZTController : ApiController {

        private readonly OracleDataService _service = new OracleDataService();

        private readonly string _rootPath = WebConfigurationManager.AppSettings["RootPath"];

        /// <summary>
        /// 获取业务成果
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("getbusinessresult")]
        public async Task<ApiResult> GetBusinessResult(dynamic input) {
            string businessNo = input?.businessNo?.ToString();
            string resultType = input?.resultType?.ToString();

            if (string.IsNullOrWhiteSpace(businessNo)) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "businessNo参数无效"
                };
            }

            if (businessNo.IsSqlInject()) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "businessNo参数包含非法字符"
                };
            }

            if (string.IsNullOrWhiteSpace(resultType)) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "resultType参数无效"
                };
            }

            if (resultType.IsSqlInject()) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "resultType参数包含非法字符"
                };
            }

            //获取业务信息
            var model = _service.GetList<BusinessBaseInfo>("BUSINESSNUMBER=:businessNo",
                new OracleParameter(":businessNo", OracleDbType.Varchar2) {
                    Value = businessNo
                }).FirstOrDefault();

            if (model == null) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "业务不存在"
                };
            }

            if (model.StateCode != 2) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "业务未办结"
                };
            }

            switch (model.BusinessClass) {
                case nameof(MarkPointSurveyFlow): {
                        break;
                    }
                default: {
                        return new ApiResult() {
                            StateCode = 0,
                            Message = "不支持的业务类型"
                        };
                    }

            }

            //获取业务附件
            var attachmentInfos = _service.GetList<AttachmentInfo>($"BUSINESSID='{model.ID}' AND STATECODE=0");
            if (attachmentInfos.Any() == false) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "未查询到业务成果"
                };
            }

            List<AttachmentInfo> resultAttachmentInfos = new List<AttachmentInfo>();
            switch (resultType) {
                case "项目用地图": {
                        resultAttachmentInfos = attachmentInfos.Where(s => s.AttachmentCategories == "地形数据").ToList();
                        break;
                    }
                case "电子版用地范围": {
                        resultAttachmentInfos = attachmentInfos.Where(s => s.AttachmentCategories == "项目范围坐标").ToList();
                        break;
                    }
                case "用地圈内/外面积表": {
                        resultAttachmentInfos = attachmentInfos.Where(s => s.AttachmentCategories == "拨地定桩汇交成果").ToList();
                        break;
                    }
                case "用地地理位置图":
                case "用地测绘量图": {
                        resultAttachmentInfos = attachmentInfos.Where(s => s.AttachmentCategories == "测绘成果图").ToList();
                        break;
                    }
                case "建设用地规划红线图": {
                        resultAttachmentInfos = attachmentInfos.Where(s => s.AttachmentCategories == "规划路网数据").ToList();
                        break;
                    }
                default: {
                        return new ApiResult() {
                            StateCode = 0,
                            Message = "不支持的业务成果类型"
                        };
                    }
            }

            if (resultAttachmentInfos.Any() == false) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "未查询到业务成果"
                };
            }

            return new ApiResult() {
                StateCode = 1,
                Data = resultAttachmentInfos.Select(s => new {
                    s.ID,
                    s.AttachmentName,
                    s.AttachmentExt,
                    s.AttachmentType,
                    s.AttachmentPath
                })
            };


            //创建临时文件夹
            string tempFolder = Path.GetTempPath();
            string folder = Guid.NewGuid().ToString("N");
            string tmpPath = tempFolder + folder;
            Directory.CreateDirectory(tmpPath);

            try {

                //复制文件
                foreach (var resultAttachmentInfo in resultAttachmentInfos) {

                    string sourceFile = _rootPath + "\\" + resultAttachmentInfo.AttachmentPath;

                    string fileName = resultAttachmentInfo.AttachmentCategories + "_" + DateTime.Now.ToString("yyyyMMddHHmmssfff") + resultAttachmentInfo.AttachmentExt;

                    string descFile = tmpPath + "\\" + fileName;

                    File.Copy(sourceFile, descFile);
                }

                //压缩文件
                ZipHelper zipHelper = new ZipHelper(tempFolder);
                string zipPath = zipHelper.CompressionFile(tmpPath, folder + ".zip");

                string base64 = Convert.ToBase64String(File.ReadAllBytes(zipPath));

                File.Delete(zipPath);

                return new ApiResult() {
                    StateCode = 1,
                    Data = base64
                };
            } finally {
                //删除临时文件夹
                try {
                    if (Directory.Exists(tmpPath))
                        Directory.Delete(tmpPath, true);
                } catch (Exception e) {
                }
            }
        }

        /// <summary>
        /// 分页获取测绘单位信息列表
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("getsurveycompanypagelist")]
        public ApiResult GetCompanyList(dynamic input) {
            string companyName = input?.companyName?.ToString() ?? "";
            string strPageIndex = input?.pageIndex?.ToString();

            if (companyName.IsSqlInject()) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "companyName参数包含非法字符"
                };
            }

            int pageIndex = 1;
            if (int.TryParse(strPageIndex, out pageIndex) == false) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "pageIndex参数无效"
                };
            }

            if (pageIndex < 1) {
                pageIndex = 1;
            }

            CompanyManageController companyManageController = new CompanyManageController();
            return companyManageController.GetCompanyList(CompanyType.SurveyCompany, companyName, pageIndex, isSJZT: true);
        }


        /// <summary>
        /// 根据业务号获取业务信息接口
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost, Route("getbusinessinfo")]
        public async Task<ApiResult> GetBusinessInfo(dynamic input) {
            string businessNo = input?.businessNo?.ToString();

            if (string.IsNullOrWhiteSpace(businessNo)) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "businessNo参数无效"
                };
            }

            if (businessNo.IsSqlInject()) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "businessNo参数包含非法字符"
                };
            }

            //获取业务信息
            var record = _service.GetList<BusinessBaseInfo>("BUSINESSNUMBER=:businessNo",
                new OracleParameter(":businessNo", OracleDbType.Varchar2) {
                    Value = businessNo
                }).FirstOrDefault();


            if (record == null) {
                return new ApiResult { StateCode = 0, Message = "无法找到业务信息" };
            }

            string id = record.ID;

            try {
                if (record.BusinessClass == "BaseSurveyDataDownloadFlow") {
                    BaseSurveyDataDownloadProject project = new BaseSurveyDataDownloadProject();
                    using (OracleDataService service = new OracleDataService()) {
                        project.BaseInfo = service.GetById<BusinessBaseInfo>(id);
                        project.ContentInfo = service.GetById<BaseGISData_ContentInfo>(id);
                        project.Attachments = service.GetList<AttachmentInfo>("BusinessID='" + id + "' and StateCode=0")?.Select(a => a.ToModel()).ToList();
                    }

                    var data = new {
                        project.BaseInfo,
                        ContentInfo = project.ContentInfo,
                        Attachments = project.Attachments
                    };

                    return new ApiResult { StateCode = 1, Data = data, Message = "" };
                }
                if (record.BusinessClass == "RealEstatePreSurveyFlow") {
                    RealEstatePreSurveyProject project = new RealEstatePreSurveyProject();
                    using (OracleDataService service = new OracleDataService()) {;
                        project.BaseInfo = service.GetById<BusinessBaseInfo>(id);
                        project.ContentInfo = service.GetById<PreSurvey_ContentInfo>(id);
                        project.Attachments = service.GetList<AttachmentInfo>("BusinessID='" + id + "' and StateCode=0")?.Select(a => a.ToModel()).ToList();
                    }

                    var data = new {
                        project.BaseInfo,
                        ContentInfo = project.ContentInfo,
                        Attachments = project.Attachments
                    };

                    return new ApiResult { StateCode = 1, Data = data, Message = "" };
                }
                if (record.BusinessClass == "RealEstatePreCheckSurveyFlow") {
                    RealEstatePreCheckSurveyProject project = new RealEstatePreCheckSurveyProject();
                    using (OracleDataService service = new OracleDataService()) {
                        project.BaseInfo = service.GetById<BusinessBaseInfo>(id);
                        project.PreSurveyContentInfo = service.GetById<PreSurvey_ContentInfo>(id);
                        project.PutLineContentInfo = service.GetById<PutLine_ContentInfo>(id);
                        project.Attachments = service.GetList<AttachmentInfo>("BusinessID='" + id + "' and StateCode=0")?.Select(a => a.ToModel()).ToList();

                    }

                    var data = new {
                        project.BaseInfo,
                        ContentInfo = project.PreSurveyContentInfo,
                        Attachments = project.Attachments
                    };

                    return new ApiResult { StateCode = 1, Data = data, Message = "" };
                }
                if (record.BusinessClass == "MarkPointSurveyFlow") {
                    MarkPointSurveyProject project = new MarkPointSurveyProject();
                    using (OracleDataService service = new OracleDataService()) {
                        project.BaseInfo = service.GetById<BusinessBaseInfo>(id);
                        project.ContentInfo = service.GetById<MarkPointSurveyContentInfo>(id);
                        project.Attachments = service.GetList<AttachmentInfo>("BusinessID='" + id + "' and StateCode=0")?.Select(a => a.ToModel()).ToList();

                    }

                    var data = new {
                        project.BaseInfo,
                        ContentInfo = project.ContentInfo,
                        Attachments = project.Attachments
                    };

                    return new ApiResult { StateCode = 1, Data = data, Message = "" };
                }
                if (record.BusinessClass == "RealEstateActualSurveyFlow") {

                    RealEstateActualSurveyProject project = new RealEstateActualSurveyProject();
                    using (OracleDataService service = new OracleDataService()) {
                        project.BaseInfo = service.GetById<BusinessBaseInfo>(id);
                        project.ContentInfo = service.GetById<EstateActualSurveyContentInfo>(id);
                        project.Attachments = service.GetList<AttachmentInfo>("BusinessID='" + id + "' and StateCode=0")?.Select(a => a.ToModel()).ToList();
                    }

                    var data = new {
                        project.BaseInfo,
                        ContentInfo = project.ContentInfo,
                        Attachments = project.Attachments
                    };

                    return new ApiResult { StateCode = 1, Data = data, Message = "" };
                }
                if (record.BusinessClass == "RealEstatePreSurveyBuildingTableChangeFlow") {
                    RealEstatePreSurveyBuildingTableChangeProject project = new RealEstatePreSurveyBuildingTableChangeProject();
                    using (OracleDataService service = new OracleDataService()) {
                        project.BaseInfo = service.GetById<BusinessBaseInfo>(id);
                        project.ContentInfo = service.GetById<PreSurvey_ContentInfo>(id);
                        project.Attachments = service.GetList<AttachmentInfo>("BusinessID='" + id + "' and StateCode=0")?.Select(a => a.ToModel()).ToList();
                    }

                    var data = new {
                        project.BaseInfo,
                        ContentInfo = project.ContentInfo,
                        Attachments = project.Attachments
                    };

                    return new ApiResult { StateCode = 1, Data = data, Message = "" };
                }
                if (record.BusinessClass == "RealEstatePreSurveyResultChangeFlow") {

                    RealEstatePreSurveyResultChangeProject project = new RealEstatePreSurveyResultChangeProject();
                    using (OracleDataService service = new OracleDataService()) {
                        project.BaseInfo = service.GetById<BusinessBaseInfo>(id);
                        project.ContentInfo = service.GetById<PreSurvey_ContentInfo>(id);
                        project.Attachments = service.GetList<AttachmentInfo>("BusinessID='" + id + "' and StateCode=0")?.Select(a => a.ToModel()).ToList();
                    }

                    var data = new {
                        project.BaseInfo,
                        ContentInfo = project.ContentInfo,
                        Attachments = project.Attachments
                    };

                    return new ApiResult { StateCode = 1, Data = data, Message = "" };
                }
                if (record.BusinessClass == "RealEstateActualResultChangeFlow") {
                    RealEstateActualResultChangeProject project = new RealEstateActualResultChangeProject();
                    using (OracleDataService service = new OracleDataService()) {
                        project.BaseInfo = service.GetById<BusinessBaseInfo>(id);
                        project.ContentInfo = service.GetById<EstateActualSurveyContentInfo>(id);
                        project.Attachments = service.GetList<AttachmentInfo>("BusinessID='" + id + "' and StateCode=0")?.Select(a => a.ToModel()).ToList();
                    }

                    var data = new {
                        project.BaseInfo,
                        ContentInfo = project.ContentInfo,
                        Attachments = project.Attachments
                    };

                    return new ApiResult { StateCode = 1, Data = data, Message = "" };
                }
                if (record.BusinessClass == "MeasurePutLinePreCheckFlow") {
                    MeasurePutLinePreCheckProject project = new MeasurePutLinePreCheckProject();
                    using (OracleDataService service = new OracleDataService()) {
                        project.BaseInfo = service.GetById<BusinessBaseInfo>(id);
                        project.ContentInfo = service.GetById<PutLine_ContentInfo>(id);
                        project.Attachments = service.GetList<AttachmentInfo>("BusinessID='" + id + "' and StateCode=0")?.Select(a => a.ToModel()).ToList();
                    }

                    var data = new {
                        project.BaseInfo,
                        ContentInfo = project.ContentInfo,
                        Attachments = project.Attachments
                    };

                    return new ApiResult { StateCode = 1, Data = data, Message = "" };
                }
                if (record.BusinessClass == "RealEstatePreCheckSurveyAutoFlow") {
                    RealEstatePreCheckSurveyAutoProject project = new RealEstatePreCheckSurveyAutoProject();
                    using (OracleDataService service = new OracleDataService()) {
                        project.BaseInfo = service.GetById<BusinessBaseInfo>(id);
                        project.PreSurveyContentInfo = service.GetById<PreSurvey_ContentInfo>(id);
                        project.PutLineContentInfo = service.GetById<PutLine_ContentInfo>(id);
                        project.Attachments = service.GetList<AttachmentInfo>("BusinessID='" + id + "' and StateCode=0")?.Select(a => a.ToModel()).ToList();
                        //为了兼容附件分类名称做修改
                        foreach (var attachmentInfoModel in project.Attachments.Where(s => s.AttachmentCategories == "报批规划指标校验图")) {
                            attachmentInfoModel.AttachmentCategories = "楼层平面图面积计算框线图";
                        }
                    }

                    var data = new {
                        project.BaseInfo,
                        ContentInfo = project.PreSurveyContentInfo,
                        Attachments = project.Attachments
                    };

                    return new ApiResult { StateCode = 1, Data = data, Message = "" };
                }
                if (record.BusinessClass == "CouncilPlanCheckFlow") {
                    CouncilPlanCheckProject project = new CouncilPlanCheckProject();
                    using (OracleDataService service = new OracleDataService()) {
                        project.BaseInfo = service.GetById<BusinessBaseInfo>(id);
                        project.ContentInfo = service.GetById<CouncilPlanCheckContentInfo>(id);
                        project.Attachments = service.GetList<AttachmentInfo>("BusinessID='" + id + "' and StateCode=0")?.Select(a => a.ToModel()).ToList();

                    }

                    var data = new {
                        project.BaseInfo,
                        ContentInfo = project.ContentInfo,
                        Attachments = project.Attachments
                    };

                    return new ApiResult { StateCode = 1, Data = data, Message = "" };
                }
                if (record.BusinessClass == "CouncilMeasurePutLinePreCheckFlow") {
                    CouncilMeasurePutLinePreCheckProject project = new CouncilMeasurePutLinePreCheckProject();
                    using (OracleDataService service = new OracleDataService()) {
                        project.BaseInfo = service.GetById<BusinessBaseInfo>(id);
                        project.ContentInfo = service.GetById<PutLine_ContentInfo>(id);
                        project.Attachments = service.GetList<AttachmentInfo>("BusinessID='" + id + "' and StateCode=0")?.Select(a => a.ToModel()).ToList();
                    }

                    var data = new {
                        project.BaseInfo,
                        ContentInfo = project.ContentInfo,
                        Attachments = project.Attachments
                    };

                    return new ApiResult { StateCode = 1, Data = data, Message = "" };
                }
                if (record.BusinessClass == "RealEstateOverallActualSurveyFlow") {
                    RealEstateOverallActualSurveyProject project = new RealEstateOverallActualSurveyProject();
                    using (OracleDataService service = new OracleDataService()) {
                        project.BaseInfo = service.GetById<BusinessBaseInfo>(id);
                        project.ContentInfo = service.GetById<EstateActualSurveyContentInfo>(id);
                        project.Attachments = service.GetList<AttachmentInfo>("BusinessID='" + id + "' and StateCode=0")?.Select(a => a.ToModel()).ToList(); ;

                    }

                    var data = new {
                        project.BaseInfo,
                        ContentInfo = project.ContentInfo,
                        Attachments = project.Attachments
                    };

                    return new ApiResult { StateCode = 1, Data = data, Message = "" };
                }
                return new ApiResult { StateCode = 0, Data = "", Message = "未识别的业务" };
            } catch (Exception e) {
                Request.WriteSDCLog("获取业务详情", $"业务ID：{id} >> 错误信息：{e.GetStackTraces()} ");
                return new ApiResult { StateCode = 0, Message = "获取失败，错误信息：" + e.Message + "" };
            }
        }
    }
}

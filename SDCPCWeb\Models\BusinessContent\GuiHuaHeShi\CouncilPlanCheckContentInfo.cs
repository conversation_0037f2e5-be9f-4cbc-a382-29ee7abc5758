﻿using Newtonsoft.Json;
using SDCPCWeb.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace SDCPCWeb.Models.BusinessContent.GuiHuaHeShi {
    /// <summary>
    /// 市政工程建设竣工规划核实
    /// </summary>
    public class CouncilPlanCheckContentInfoModel {
        /// <summary>
        /// 主键ID，与BusinessBaseInfo表的ID关联的外键
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 4种业务类型：1.道路桥梁类；2.地上下管线类；3.市政场站类；4.水系工程类
        /// </summary>
        public string BusinessType { get; set; }
        /// <summary>
        /// 4种业务类型名称
        /// </summary>
        public string BusinessTypeName { get; set; }
        /// <summary>
        /// 工程规划许可
        /// </summary>
        public string ProjectPlanPermission { get; set; }

        /// <summary>
        /// 测绘成果汇总信息
        /// </summary>
        public string ProjectResultInfo { get; set; }

        /// <summary>
        /// 楼盘表成果信息
        /// </summary>
        public string BuildingTableInfo { get; set; }

        /// <summary>
        /// 成果检查任务ID
        /// </summary>
        public string DataCheckID { get; set; }
        /// <summary>
        /// 成果检查状态 0检查中  1检查通过  2检查不通过 3正在进行备案审核 -1备案审核不通过 -2备案审核通过
        /// </summary>
        public int DataCheckState { get; set; }
        /// <summary>
        /// 注册测绘师姓名
        /// </summary>
        public string SurveyMasterName { get; set; }
        /// <summary>
        /// 注册测绘师身份证号
        /// </summary>
        public string SurveyMasterNo { get; set; }
        /// <summary>
        /// 注册测绘师确认时间
        /// </summary>
        public DateTime? SurveyMasterSureTime { get; set; }
        /// <summary>
        /// 测绘备案不通过的原因
        /// </summary>
        public string RejectMessage { get; set; }

        /// <summary>
        /// 单位地址
        /// </summary>
        public string CompanyAddress { get; set; }

        /// <summary>
        /// 法定代表人姓名
        /// </summary>
        public string LegalPersonName { get; set; }

        /// <summary>
        /// 法定代表人电话
        /// </summary>
        public string LegalPersonNumber { get; set; }

        /// <summary>
        /// 法定代表人联系电话
        /// </summary>
        public string LegalPersonPhone { get; set; }

        /// <summary>
        /// 规划条件编号（建设项目规划设计条件通知书的审批号）
        /// </summary>
        public string PlanConditionNumber { get; set; }

        /// <summary>
        /// 统一项目代码（发改立项批文中的项目代码）
        /// </summary>
        public string UnifiedProjectCode { get; set; }

        /// <summary>
        /// 项目类型：建筑类、工程管线类、交通类
        /// </summary>
        public string ProjectType { get; set; }

        /// <summary>
        /// 是否自建房
        /// </summary>
        public string IsZJ { get; set; }

        /// <summary>
        /// 申请人类型：企业或个人
        /// </summary>
        public string PersonType { get; set; }
        /// <summary>
        /// 竣工用地面积
        /// </summary>
        public string CompletionLandArea { get; set; }
        /// <summary>
        /// 竣工总建筑面积
        /// </summary>
        public string CompletionBuildingArea { get; set; }
    }
    /// <summary>
    /// 市政工程建设竣工规划核实
    /// </summary>
    public class CouncilPlanCheckContentInfo : CouncilPlanCheckContentInfoModel, IOracleDataTable {
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public CouncilPlanCheckContentInfoModel ToModel() {
            return JsonConvert.DeserializeObject<CouncilPlanCheckContentInfoModel>(JsonConvert.SerializeObject(this));
        }
    }
}
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace SDCPCWeb.Models.CompanyRegister {
    public class SurveyCompanyListItem {
        public string CompanyId { get; set; }
        public string CompanyName { get; set; }
        public string CreditCode { get; set; }
        public string CreditRating { get; set; }
        public string QualificationLevel { get; set; }
        public string Contacter { get; set; }
        public string ContacterPhone { get; set; }

        public static string MainSql =>
            "SELECT C.ID \"CompanyId\",C.COMPANYNAME \"CompanyName\",C.CREDITCODE \"CreditCode\",B.BUSINESSCLASS,Q.QUALIFICATIONLEVEL \"QualificationLevel\",<PERSON><PERSON> \"Contacter\",<PERSON><PERSON> \"ContacterPhone\",'A' \"CreditRating\",O.LISTORDER\n" +
            "FROM SURVEYCOMPANYORDER O\n" +
            "INNER JOIN COMPANYBASEINFO C ON O.SURVEYCOMPANYID=C.ID\n" +
            "INNER JOIN COMPANYQUALIFICATION Q ON C.ID=Q.ID\n" +
            "INNER JOIN SURVEYCOMPANYBUSINESSCLASS B ON O.SURVEYCOMPANYID=B.SURVEYCOMPANYID\n";
    }
}
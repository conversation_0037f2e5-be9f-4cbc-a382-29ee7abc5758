﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Web;
using Oracle.ManagedDataAccess.Client;
using SDCPCWeb.Extensions;
using SDCPCWeb.Models.CompanyRegister;
using SDCPCWeb.Services;

namespace SDCPCWeb.Models {
    public class UserInfo {
        public string UserId { get; set; }
        public string PersonName { get; set; }
        public string PersonNo { get; set; }
        public string Phone { get; set; }
        public bool IsRealName { get; set; }
        public string From { get; set; }

        public CompanyBaseInfoModel GetRelateCompany() {
            if (string.IsNullOrWhiteSpace(PersonNo)) {
                return null;
            }
            using (var oracle = new OracleDataService()) {
                var companyId = oracle.GetList<CompanyEmployees>($"PersonNumber='{PersonNo}'").FirstOrDefault()
                    ?.RelationRequestId;
                if (!string.IsNullOrWhiteSpace(companyId)) {
                    return oracle.GetById<CompanyBaseInfo>(companyId);
                }
                return null;
            }
        }
        public CompanyBaseInfoModel GetRelateSurveyCompany() {
            if (string.IsNullOrWhiteSpace(PersonNo)) {
                return null;
            }
            using (var oracle = new OracleDataService()) {
                var companyId = oracle.GetList<CompanyEmployees>($"PersonNumber='{PersonNo}'").FirstOrDefault()
                    ?.RelationRequestId;
                if (!string.IsNullOrWhiteSpace(companyId)) {
                    return oracle.GetById<CompanyBaseInfo>(companyId);
                }
                return null;
            }
        }
        public Dictionary<CompanyBaseInfoModel, string> GetRelateDevelopCompanyList() {
            if (string.IsNullOrWhiteSpace(PersonNo)) {
                return null;
            }
            using (var oracle = new OracleDataService()) {
                var result = new Dictionary<CompanyBaseInfoModel, string>();
                var employees = oracle.GetList<DeveloperEmployee>($"PersonNumber='{PersonNo}'");
                if (employees.Any()) {
                    foreach (var employee in employees) {
                        var company = oracle.GetById<CompanyBaseInfo>(employee.RelationRequestId);
                        result.Add(company.ToModel(), employee.PersonRole);
                    }
                    return result;
                }
                return null;
            }
        }

        public bool IsSDCCompanyAdmin(string companyid) {
            if (string.IsNullOrWhiteSpace(companyid)) {
                return false;
            }
            using (var oracle = new OracleDataService()) {
                var company = oracle.GetById<CompanyBaseInfo>(companyid);
                if (company.CompanyType.CompanyBaseTypeFromName() == CompanyBaseType.ConstructionCompany) {
                    var models = GetRelateDevelopCompanyList();
                    return models.Any(item => item.Key.ID == company.ID && item.Value == "单位管理员");
                }

                if (company.CompanyType.CompanyTypeFromName() == CompanyType.SurveyCompany) {
                    var records = oracle.GetList<CompanyEmployees>("PersonNumber=:pno AND RelationRequestId=:cid AND PersonRole='单位管理员'"
                        , new OracleParameter(":pno", OracleDbType.Varchar2) { Value = PersonNo }
                        , new OracleParameter(":cid", OracleDbType.Varchar2) { Value = company.ID }
                    );
                    return records.Any();
                }
            }

            return false;
        }

        /// <summary>
        /// 获取用户所属的单位类型名称
        /// </summary>
        /// <returns></returns>
        public string[] GetRelateCompanyTypes() {
            if (string.IsNullOrWhiteSpace(PersonNo)) {
                return null;
            }
            using (var oracle = new OracleDataService()) {
                if (oracle.Exists<CompanyEmployees>($"PersonNumber='{PersonNo}'")) {
                    return new[] {"测绘单位"};
                }
                else {
                    string sqlText = "SELECT B.COMPANYTYPE FROM DEVELOPEREMPLOYEE A ";
                    sqlText += "INNER JOIN COMPANYBASEINFO B ON A.RELATIONREQUESTID = B.ID ";
                    sqlText += $"WHERE A.PERSONNUMBER = '{PersonNo}'";

                    var dt = oracle.ExecuteQuerySql(sqlText);
                    List<string> result = new List<string>();
                    foreach (DataRow row in dt.Rows) {
                        result.Add(row[0].ToString());
                    }

                    return result.ToArray();
                }
            }
        }
    }
}
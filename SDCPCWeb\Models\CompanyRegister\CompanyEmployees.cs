﻿using Newtonsoft.Json;
using SDCPCWeb.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace SDCPCWeb.Models.CompanyRegister {
    /// <summary>
    /// 单位人员信息
    /// </summary>
    public class CompanyEmployeesModel {
        /// <summary>
        /// ID
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 关联已通过的申请ID，或者变更ID
        /// </summary>
        public string RelationRequestId { get; set; }
        /// <summary>
        /// 姓名
        /// </summary>
        public string PersonName { get; set; }
        /// <summary>
        /// 身份证号
        /// </summary>
        public string PersonNumber { get; set; }
        /// <summary>
        /// 手机号
        /// </summary>
        public string PersonPhone { get; set; }
        /// <summary>
        /// 角色
        /// </summary>
        public string PersonRole { get; set; }
        /// <summary>
        /// 注册测绘师证书编号
        /// </summary>
        public string RegisteredSurveyorNo { get; set; }
        /// <summary>
        /// 注册测绘师证书有效日期
        /// </summary>
        public DateTime? ValidityTime { get; set; }
        /// <summary>
        /// 附件信息
        /// </summary>
        public string AttachmentInfo { get; set; }

        /// <summary>
        /// 用户类型：1企业类型，其它普通用户类型
        /// </summary>
        public string UserType { get; set; }

    }
    /// <summary>
    /// 
    /// </summary>
    public class CompanyEmployees : CompanyEmployeesModel, IOracleDataTable {
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public CompanyEmployeesModel ToModel() {
            return JsonConvert.DeserializeObject<CompanyEmployeesModel>(JsonConvert.SerializeObject(this));
        }
    }
}
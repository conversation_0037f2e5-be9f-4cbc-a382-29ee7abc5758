﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Newtonsoft.Json;
using Oracle.ManagedDataAccess.Client;
using SDCPCWeb.Services;

namespace SDCPCWeb.Models.CompanyRegister {
    /// <summary>
    /// 注册测绘师授权表
    /// </summary>
    public class SurveyMasterAuthModel {

        public string ID { get; set; }
        /// <summary>
        /// 单位ID
        /// </summary>
        public string CompanyID { get; set; }
        /// <summary>
        /// 注册测绘师ID
        /// </summary>
        public string SurveyMasterID { get; set; }
        /// <summary>
        /// 注册测绘师身份证号
        /// </summary>
        public string SurveyMasterPersonNo { get; set; }
        /// <summary>
        /// 授权结束时间
        /// </summary>
        public DateTime AuthEndDate { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateDate { get; set; }
        /// <summary>
        /// 创建者用户Id
        /// </summary>
        public string CreatorID { get; set; }
        /// <summary>
        /// 创建者
        /// </summary>
        public string Creator { get; set; }
        /// <summary>
        /// 测绘师确认授权时间
        /// </summary>
        public DateTime? AcceptDate { get; set; }
        /// <summary>
        /// 状态，0待确认，1已确认，2已过期，-1已撤销
        /// </summary>
        public int StateCode { get; set; }
    }

    public class SurveyMasterAuth : SurveyMasterAuthModel, IOracleDataTable {
        public SurveyMasterAuthModel ToModel() {
            return JsonConvert.DeserializeObject<SurveyMasterAuthModel>(JsonConvert.SerializeObject(this));
        }
    }

    /// <summary>
    /// 注册测绘师授权申请信息
    /// </summary>
    public class SurveyMasterAuthRequest : SurveyMasterAuthModel {
        public List<SurveyMasterAuthClassModel> BusinessClasses { get; set; }

        public SurveyMasterAuth ToSurveyMasterAuth() {
            var entity = JsonConvert.DeserializeObject<SurveyMasterAuth>(JsonConvert.SerializeObject(this));
            entity.ID = entity.ID ?? Guid.NewGuid().ToString("N");
            return entity;
        }

        public static SurveyMasterAuthRequest FromSurveyMasterAuth(SurveyMasterAuth model) {
            var entity = JsonConvert.DeserializeObject<SurveyMasterAuthRequest>(JsonConvert.SerializeObject(model));
            entity.ID = entity.ID ?? Guid.NewGuid().ToString("N");
            return entity;
        }

        public static SurveyMasterAuthRequest GetFromAuthId(string authId, OracleDataService oracle = null) {
            if (string.IsNullOrWhiteSpace(authId)) {
                return null;
            }
            if (oracle == null) {
                using (oracle = new OracleDataService()) {
                    return GetFromAuthId(authId, oracle);
                }
            }
            else {
                var auth = oracle.GetById<SurveyMasterAuth>(authId);
                if (auth == null) {
                    return null;
                }
                var result = FromSurveyMasterAuth(auth);
                result.BusinessClasses = oracle.GetList<SurveyMasterAuthClass>("AuthID=:aid"
                    , new OracleParameter(":aid", OracleDbType.Varchar2) { Value = auth.ID }
                        ).Select(c => c.ToModel()).ToList();
                return result;
            }
        }
    }

    /// <summary>
    /// 注册测绘师和授权申请信息
    /// </summary>
    public class SurveyMasterWithAuthModel : CompanyEmployeesModel {

        public SurveyMasterAuthRequest AuthRequest { get; set; }

        /// <summary>
        /// 注册测绘师是否已过期
        /// </summary>
        public bool? IsExpired {
            get {
                return base.ValidityTime.HasValue && ValidityTime.Value < DateTime.Now.Date;
            }
        }

        public static SurveyMasterWithAuthModel FromModel(CompanyEmployeesModel model) {
            return JsonConvert.DeserializeObject<SurveyMasterWithAuthModel>(JsonConvert.SerializeObject(model));
        }
    }
}
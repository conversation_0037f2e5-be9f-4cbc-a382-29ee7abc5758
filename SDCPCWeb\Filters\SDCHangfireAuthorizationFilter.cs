﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Web;
using System.Web.Configuration;
using Hangfire.Dashboard;
using Newtonsoft.Json.Linq;
using SDCPCWeb.Models.Mongo;
using SDCPCWeb.Models.System;
using SDCPCWeb.Services;

namespace SDCPCWeb.Filters {
    public class SDCHangfireAuthorizationFilter : IDashboardAuthorizationFilter {
        private readonly MongoService<HangfireAdmin> hangfireAdminMongoService = new MongoService<HangfireAdmin>();
        public bool Authorize(DashboardContext context) {
            //内网安全访问
            if (context.Request.RemoteIpAddress.StartsWith("172.16.48") 
                || context.Request.RemoteIpAddress.StartsWith("172.16.47") 
                || context.Request.RemoteIpAddress.StartsWith("172.16.46") 
                || context.Request.RemoteIpAddress.StartsWith("172.16.151")  //市民中心C313
                || context.Request.RemoteIpAddress.StartsWith("172.16.150")  //市民中心C602
                || context.Request.RemoteIpAddress == "************"
                || context.Request.RemoteIpAddress == "127.0.0.1"
                || context.Request.RemoteIpAddress == "::1"
                || context.Request.RemoteIpAddress.StartsWith("10.100.203") 
                ) {
                return true;
            }

            //多测合一授权访问
            var actionContext = HttpContext.Current;
            //取MongoDB中Users集合，判断上下文请求中是否满足条件
            string personName = null;
            string personNo = null;


            //判断请求Cookie是否已登录邕e登
            var cookies = actionContext.Request.Cookies;
            if (cookies.Count > 0) {
                if (cookies.Get(".AspNet.ApplicationCookie") != null) {
                    var cookieVal = cookies.Get(".AspNet.ApplicationCookie")?.Value;
                    //存在此Cookie，去主站点获取一次用户信息，验证是否有效
                    using (var web = new WebClient() { Proxy = null, Encoding = Encoding.UTF8 }) {
                        web.Headers.Set(HttpRequestHeader.Cookie, $@".AspNet.ApplicationCookie={cookieVal}");
                        try {
                            var res = web.DownloadString($@"https://{ExternalApiConfig.LoginServer}/suapi/GetUserInfo");
                            if (res != "null") {
                                var userInfo = JObject.Parse(res);
                                personName = userInfo["PersonName"].Value<string>();
                                personNo = userInfo["PersonNo"].Value<string>();
                            }
                        }
                        catch (Exception) {
                            throw;
                        }
                    }
                    //return true; //先默认有效，但是Cookie会过期
                }
            }

            //如果是调试模式或者令牌模式，则会根据token来处理接口的调用判断是否登录邕e登
            var auth = actionContext.Request.Headers.Get("Authorization");
            if (!string.IsNullOrWhiteSpace(auth) && string.IsNullOrWhiteSpace(personNo)) {
                //调用接口传了token
                using (var web = new WebClient() { Proxy = null, Encoding = Encoding.UTF8 }) {
                    web.Headers.Set(HttpRequestHeader.Authorization, $@"{auth}");
                    try {
                        var res = web.DownloadString($@"https://{ExternalApiConfig.LoginServer}/suapi/GetUserInfo");
                        if (res != "null") {
                            var userInfo = JObject.Parse(res);
                            personName = userInfo["PersonName"].Value<string>();
                            personNo = userInfo["PersonNo"].Value<string>();
                        }
                    }
                    catch (Exception) {
                        throw;
                    }
                }
            }

            if (!string.IsNullOrWhiteSpace(personName) && !string.IsNullOrWhiteSpace(personNo)) {
                var admins = hangfireAdminMongoService.Get(admin => admin.AdminPersonName == personName && admin.AdminPersonNo == personNo);
                return admins.Any();
            }

            return false;
        }
    }
}
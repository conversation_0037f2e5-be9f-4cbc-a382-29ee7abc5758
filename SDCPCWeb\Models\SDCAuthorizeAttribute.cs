﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http;
using System.Web.Http.Controllers;

namespace SDCPCWeb.Models {

    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = true)]
    public class SDCAuthorizeAttribute : AuthorizeAttribute {
        public SDCAuthorizeRole[] Roles { get; set; }

        public SDCAuthorizeAttribute(params SDCAuthorizeRole[] roles) {
            Roles = roles;
        }

        /// <summary>指示指定的控件是否已获得授权。</summary>
        /// <returns>如果控件已获得授权，则为 true；否则为 false。</returns>
        /// <param name="actionContext">上下文。</param>
        protected override bool IsAuthorized(HttpActionContext actionContext) {
            if (Roles != null && Roles.Any()) {
                return Roles.Any(role => {
                    try {
                        return Authorized(role, actionContext);
                    }
                    catch (Exception) {
                        return false;
                    }

                });
            }

            return false;
        }

        private static bool Authorized(SDCAuthorizeRole role, HttpActionContext actionContext) {
            switch (role) {
                case SDCAuthorizeRole.BDCWeb:
                    return BDCAuthorizeAttribute.Authorized(actionContext);
                case SDCAuthorizeRole.xCloud:
                    return xCloudUserAuthorizeAttribute.Authorized(actionContext);
                case SDCAuthorizeRole.BDCWebAdmin:
                    return BDCAuthorizeAttribute.WebAdminAuthorized(actionContext);
                case SDCAuthorizeRole.AuthCode:
                    return AuthCodeAuthorizeAttribute.Authorized(actionContext);
                default:
                    return false;
            }
        }
    }

    public enum SDCAuthorizeRole {
        /// <summary>
        /// 邕e登一般用户
        /// </summary>
        BDCWeb,
        /// <summary>
        /// 自然资源政务云平台用户
        /// </summary>
        xCloud,
        /// <summary>
        /// 邕e登用户中具有WebAdmin角色的用户
        /// </summary>
        BDCWebAdmin,

        /// <summary>
        /// 授权码认证
        /// </summary>
        AuthCode
    }
}
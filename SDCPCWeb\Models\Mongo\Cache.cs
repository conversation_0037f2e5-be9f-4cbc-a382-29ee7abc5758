﻿using System;
using MongoDB.Bson.Serialization.Attributes;

namespace SDCPCWeb.Models.Mongo {
    /// <summary>
    /// 缓存对象
    /// </summary>
    public class Cache {
        /// <summary>
        /// 初始化缓存对象
        /// </summary>
        /// <param name="key"></param>
        /// <param name="val"></param>
        public Cache(string key, string val, TimeSpan lifeSpan) {
            Key = key;
            Value = val;
            ExpireTime = DateTime.Now.Add(lifeSpan).ToString("yyyy-MM-dd HH:mm:ss");
        }
        /// <summary>
        /// 键
        /// </summary>
        [BsonId]
        public string Key { get; set; }
        /// <summary>
        /// 值
        /// </summary>
        public string Value { get; set; }
        /// <summary>
        /// 失效时间
        /// </summary>
        public string ExpireTime { get; set; }
    }
}
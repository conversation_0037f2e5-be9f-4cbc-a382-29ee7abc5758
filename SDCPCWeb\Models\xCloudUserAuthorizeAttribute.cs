﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Web;
using System.Web.Configuration;
using System.Web.Http;
using System.Web.Http.Controllers;
using Newtonsoft.Json.Linq;

namespace SDCPCWeb.Models {
    /// <summary>
    /// 来自自然资源云平台的用户验证
    /// </summary>
    public class xCloudUserAuthorizeAttribute : AuthorizeAttribute {
        private static string _xCloudSsoUrl = WebConfigurationManager.AppSettings["xCloudApiUrl"];
        protected override bool IsAuthorized(HttpActionContext actionContext) {
            return Authorized(actionContext);
        }

        internal static bool Authorized(HttpActionContext actionContext) {
            //如果是调试模式或者令牌模式，则会根据token来处理接口的调用判断是否登录自然资源云平台
            //var testuser = new UserInfo() {
            //    From = "xCloud",
            //    UserId = null,
            //    PersonName = "管理员"
            //};
            //actionContext.Request.Properties.Add("SDC-UserInfo", testuser);
            //return true;

            var token = actionContext.Request.Headers.Authorization?.Parameter;
            if (!string.IsNullOrWhiteSpace(token)) {
                //调用接口传了token
                using (var web = new WebClient() { Proxy = null, Encoding = Encoding.UTF8 }) {
                    web.Headers.Set(HttpRequestHeader.Authorization, $@"bearer {token}");
                    web.Headers.Add("sessionid", "13fa1113557b8652b87d1389969dc0f5");
                    var res = web.DownloadString($"{_xCloudSsoUrl}/mso/GetUser");
                    if (res != "null") {
                        var userInfo = JObject.Parse(res);
                        actionContext.Request.Headers.Add("SDC-UserId", userInfo["Id"].Value<string>());
                        actionContext.Request.Headers.Add("SDC-PersonName", userInfo["Name"].Value<string>());
                        //actionContext.Request.Headers.Add("SDC-PersonNo", userInfo["PersonNo"].Value<string>());
                        actionContext.Request.Headers.Add("SDC-Phone", userInfo["Mobile"].Value<string>());
                        //actionContext.Request.Headers.Add("SDC-IsRealName", userInfo["IsRealName"].Value<bool>().ToString());

                        var user = new UserInfo() {
                            From = "xCloud",
                            UserId = userInfo["Id"].Value<string>(),
                            PersonName = userInfo["Name"].Value<string>(),
                            Phone = userInfo["Mobile"].Value<string>()
                        };
                        actionContext.Request.Properties.Add("SDC-UserInfo", user);
                        return true;
                    }
                }
            }

            return false;
        }
    }
}
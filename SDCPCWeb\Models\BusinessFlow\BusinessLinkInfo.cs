﻿using Newtonsoft.Json;
using SDCPCWeb.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Newtonsoft.Json.Linq;

namespace SDCPCWeb.Models.BusinessFlow {
    /// <summary>
    /// 业务流转信息
    /// </summary>
    public class BusinessLinkInfoModel {
        /// <summary>
        /// 环节ID
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 业务ID
        /// </summary>
        public string BusinessId { get; set; }
        /// <summary>
        /// 环节名称
        /// </summary>
        public string LinkName { get; set; }
        /// <summary>
        /// 环节状态，0待签收，1办理中，2已完成，3已退回，4已关闭
        /// </summary>
        public int StateCode { get; set; }
        /// <summary>
        /// 环节开始时间
        /// </summary>
        public DateTime StartTime { get; set; }
        /// <summary>
        /// 签收时间
        /// </summary>
        public DateTime? SignatureTime { get; set; }
        /// <summary>
        /// 环节完成时间
        /// </summary>
        public DateTime? EndTime { get; set; }
        /// <summary>
        /// 环节执行用户ID
        /// </summary>
        public string CurrentUserID { get; set; }
        /// <summary>
        /// 流程环节ID
        /// </summary>
        public int ActionId { get; set; }
        /// <summary>
        /// 流程用户权限
        /// </summary>
        public string ActionUserRole { get; set; }
        /// <summary>
        /// 流转意见、退回原因等
        /// </summary>
        public string FlowNotice { get; set; }

    }

    public class BusinessLinkInfo : BusinessLinkInfoModel, IOracleDataTable {
        /// <summary>
        /// 转换为基类模型对象
        /// </summary>
        /// <returns></returns>
        public BusinessLinkInfoModel ToModel() {
            return JsonConvert.DeserializeObject<BusinessLinkInfoModel>(JsonConvert.SerializeObject(this));
        }

        /// <summary>
        /// 从基类模型对象转换为可入库的数据对象
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public static BusinessLinkInfo FromModel(BusinessLinkInfoModel model) {
            return JsonConvert.DeserializeObject<BusinessLinkInfo>(JsonConvert.SerializeObject(model));
        }
    }

    public class BusinessLinkInfoModelEx : BusinessLinkInfoModel {
        public BusinessLinkInfoModelEx(BusinessLinkInfoModel model) {
            if (model != null) {
                base.ID = model.ID;
                base.BusinessId = model.ID;
                base.LinkName = model.LinkName;
                base.StateCode = model.StateCode;
                base.StartTime = model.StartTime;
                base.EndTime = model.EndTime;
                base.CurrentUserID = model.CurrentUserID;
                base.ActionId = model.ActionId;
                base.ActionUserRole = model.ActionUserRole;
                base.FlowNotice = model.FlowNotice;

            }
        }

        /// <summary>
        /// 扩展属性
        /// </summary>
        public JObject ExtendAttr { get; set; }
    }
}
﻿using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using System.Web.Http.Controllers;

namespace SDCPCWeb.Models {
    /// <summary>
    /// 授权码验证
    /// </summary>
    public class AuthCodeAuthorizeAttribute : AuthorizeAttribute {
        protected override bool IsAuthorized(HttpActionContext actionContext) {
            return Authorized(actionContext);
        }

        internal static bool Authorized(HttpActionContext actionContext) {
            if (actionContext.Request.Headers.TryGetValues("AuthCode", out IEnumerable<string> authCode)) {
                if (authCode.FirstOrDefault() == "SDCCommonAuthorize") {
                    return true;
                }
            }

            return false;
        }
    }
}
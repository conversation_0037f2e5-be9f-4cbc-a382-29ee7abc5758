﻿using System;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Newtonsoft.Json.Linq;

namespace SDCPCWebTests.Services {
    [TestClass]
    public class EPSServiceTest {
        [TestMethod]
        public async Task TestCanGroundCodeUse() {
            //校验宗地号是否可用
            var servicesUrl = $"http://172.16.3.184/EPSSDCServices/EPSSDCServices.asmx/IsZongDExits?strZDDM=450103001001GB00001";
            var httpClient = new HttpClient();
            var responseMessage = await httpClient.GetAsync(servicesUrl);
            if (responseMessage.IsSuccessStatusCode) {
                var responseText = await responseMessage.Content.ReadAsStringAsync();
                System.Xml.XmlDocument xml = new System.Xml.XmlDocument();
                xml.LoadXml(responseText);
                JObject result = JObject.Parse(xml.InnerText);
                if (result["status"]?.ToString() == "success" && result["isZongDExits"]?.ToString() == "True") {

                } else {
                    Console.WriteLine("宗地号不存在");
                }
            } else {
                Console.WriteLine("校验宗地号失败，请联系管理员"); 
            }
        }
    }
}

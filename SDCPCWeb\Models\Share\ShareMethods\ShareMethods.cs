﻿using SDCPCWeb.Models.Result;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Web;
using Newtonsoft.Json;
using SDCPCWeb.Services;

namespace SDCPCWeb.Models.Share {
    /// <summary>
    /// 共享方法集
    /// </summary>
    public partial class ShareMethods {
        /// <summary>
        /// Oracle数据库信息获取对象
        /// </summary>
        private readonly OracleDataService oracle = new OracleDataService();
        private List<ShareMethodHandler> Methods { get; set; }
        public ShareMethods() {
            //把类内部所有符合委托签名的方法都加入列表
            var members = GetType().GetMembers(BindingFlags.Instance | BindingFlags.NonPublic);
            Methods = new List<ShareMethodHandler>();
            //遍历所有成员
            foreach (var member in members) {
                //是方法
                if (member.MemberType == MemberTypes.Method) {
                    var method = GetType().GetMethod(member.Name, BindingFlags.Instance | BindingFlags.NonPublic);
                    if (method != null) {
                        ShareMethodHandler myDelegateMethod;
                        try {
                            myDelegateMethod = method.CreateDelegate(typeof(ShareMethodHandler), this) as ShareMethodHandler;
                        }
                        catch {
                            continue;
                        }
                        if (myDelegateMethod != null) {
                            //没有Obsolete特性
                            if (member.CustomAttributes.All(a => a.AttributeType != typeof(ObsoleteAttribute))) {
                                Methods.Add(myDelegateMethod);
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 执行方法
        /// </summary>
        /// <param name="methodName"></param>
        /// <param name="arguments"></param>
        /// <returns></returns>
        public ApiResult ExecuteMethod(string methodName, object arguments) {
            var method = Methods.FirstOrDefault(m => m.Method.Name == methodName);
            if (method != null) {
                return method.Invoke(arguments);
            }
            return new ApiResult { StateCode = 0, Message = "该方法不支持" };
        }

        /// <summary>
        /// 转换参数
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="arguments"></param>
        /// <returns></returns>
        private T ArgumentToObject<T>(object arguments) {
            return JsonConvert.DeserializeObject<T>(JsonConvert.SerializeObject(arguments));
        }
    }

    /// <summary>
    /// 受支持的共享方法统一委托。类内部所有参数和返回值匹配这个委托的自动支持
    /// </summary>
    /// <param name="arguments"></param>
    /// <returns></returns>
    internal delegate ApiResult ShareMethodHandler(object arguments);
}
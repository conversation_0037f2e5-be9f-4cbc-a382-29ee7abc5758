﻿using Newtonsoft.Json;
using SDCPCWeb.Services;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using SDCPCWeb.Models.System;

namespace SDCPCWeb.Models.Attachment {
    /// <summary>
    /// 附件信息表
    /// </summary>
    public class AttachmentInfoModel {
        /// <summary>
        /// 附件唯一标识码
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 附件关联类型
        /// </summary>
        public string BusinessType { get; set; }
        /// <summary>
        /// 附件关联ID
        /// </summary>
        public string BusinessID { get; set; }
        /// <summary>
        /// 附件名称
        /// </summary>
        public string AttachmentName { get; set; }
        /// <summary>
        /// 附件后缀名
        /// </summary>
        public string AttachmentExt { get; set; }
        /// <summary>
        /// 附件大小
        /// </summary>
        public int AttachmentLength { get; set; }
        /// <summary>
        /// 附件类型
        /// </summary>
        public string AttachmentType { get; set; }
        /// <summary>
        /// 附件类别
        /// </summary>
        public string AttachmentCategories { get; set; }
        /// <summary>
        /// 附件上传时间
        /// </summary>
        public DateTime? UploadTime { get; set; }
        /// <summary>
        /// 附件路径
        /// </summary>
        public string AttachmentPath { get; set; }
        /// <summary>
        /// 附件缩略图路径
        /// </summary>
        public string AttachmentThumbPath { get; set; }
        /// <summary>
        /// 附件状态（逻辑删除）0未删除，1已删除
        /// </summary>
        public int StateCode { get; set; }

        /// <summary>
        /// 附件提取码
        /// </summary>
        public string ValidateNumber { get; set; }
        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime? ExpirationTime { get; set; }

        /// <summary>
        /// 锁定解除时间
        /// </summary>
        public DateTime? LiftTime { get; set; }


    }

    public class AttachmentInfo : AttachmentInfoModel, IOracleDataTable {
        private static string RootPath = ExternalApiConfig.RootPath;

        public AttachmentInfoModel ToModel() {
            return JsonConvert.DeserializeObject<AttachmentInfoModel>(JsonConvert.SerializeObject(this));
        }

        public Stream ToStream() {
            return new FileStream(RootPath + "\\" + AttachmentPath, FileMode.Open);
        }
    }
}
﻿using System;
using System.CodeDom;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Reflection;
using System.Web;
using System.Web.Configuration;
using Newtonsoft.Json;
using Oracle.ManagedDataAccess.Client;
using SDCPCWeb.Models;
using SDCPCWeb.Models.Result;

namespace SDCPCWeb.Services {
    public class OracleDataService : IDisposable {
        /// <summary>
        /// 数据库链接
        /// </summary>
        private readonly OracleConnection connection = new OracleConnection(WebConfigurationManager.ConnectionStrings["SDCWorkDB"].ConnectionString);

        /// <summary>
        /// 查询语句直接返回DataTable
        /// </summary>
        /// <param name="sql"></param>
        /// <returns></returns>
        public DataTable ExecuteQuerySql(string sql, params OracleParameter[] parameters) {
            var da = new OracleDataAdapter(sql, connection);
            if (parameters?.Any() == true) {
                //有参数
                da.SelectCommand.Parameters.AddRange(parameters);
                da.SelectCommand.BindByName = true;
            }
            var dt = new DataTable();
            da.Fill(dt);
            return dt;
        }

        /// <summary>
        /// 执行SQL更新语句
        /// </summary>
        /// <param name="sql"></param>
        /// <param name="parameters"></param>
        public void ExecuteUpdateSql(string sql, params OracleParameter[] parameters) {
            var cmd = new OracleCommand(sql, connection);
            if (parameters?.Any() == true) {
                //有参数
                cmd.Parameters.AddRange(parameters);
                cmd.BindByName = true;
            }

            try {
                cmd.Connection.Open();
                cmd.ExecuteNonQuery();
                cmd.Connection.Close();
            }
            finally {
                if (connection.State == ConnectionState.Open) {
                    connection.Close();
                }
            }

        }

        /// <summary>
        /// 批量执行SQL更新语句
        /// </summary>
        /// <param name="sqlList"></param>
        public void BatchExecuteUpdateSql(List<string> sqlList) {
            if (connection.State != ConnectionState.Open) {
                connection.Open();
            }
            var tran = connection.BeginTransaction();  //事务
            try {
                //创建Command 并循环插入数据
                var command = connection.CreateCommand();
                foreach (var sql in sqlList) {
                    command.CommandText = sql;
                    command.ExecuteNonQuery();
                }
                tran.Commit();
            } catch (OracleException ex) {
                tran.Rollback();
                throw ex;
            } finally {
                if (connection.State == ConnectionState.Open) {
                    connection.Close();
                }
            }

        }

        /// <summary>
        /// 根据ID获取数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="id"></param>
        /// <returns></returns>
        public T GetById<T>(string id) where T : IOracleDataTable {
            var tableName = typeof(T).Name;
            var fieldProps = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance);
            var fieldNames = fieldProps.Select(p => p.Name);
            //组织字段
            var fields = string.Join(",", fieldNames.Select(f => $"{f} as \"{f}\""));

            //组织语句
            var getSql = $"SELECT {fields} FROM {tableName} WHERE ID=:id";
            var da = new OracleDataAdapter(getSql, connection);
            var para = da.SelectCommand.Parameters.Add(":id", OracleDbType.Varchar2);
            para.Value = id;
            var dt = new DataTable();
            foreach (var prop in fieldProps) {
                dt.Columns.Add(prop.Name,
                    prop.PropertyType.IsGenericType &&
                    prop.PropertyType.GetGenericTypeDefinition() == typeof(Nullable<>)
                        ? typeof(object)
                        : prop.PropertyType);
            }
            da.Fill(dt);

            if (dt.Rows.Count > 0) {
                //取出数据反序列化到对象模型
                var json = JsonConvert.SerializeObject(dt);
                var listResult = JsonConvert.DeserializeObject<List<T>>(json);
                return listResult.First();
            }

            return default(T);
        }

        public List<T> GetList<T>(string where, params OracleParameter[] parameters) where T : IOracleDataTable {
            var tableName = typeof(T).Name;
            var fieldProps = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance);
            var fieldNames = fieldProps.Select(p => p.Name);
            //组织字段
            var fields = string.Join(",", fieldNames.Select(f => $"{f} as \"{f}\""));

            //组织语句
            var getSql = $"SELECT {fields} FROM {tableName} WHERE {where}";
            var result = new List<T>();
            var da = new OracleDataAdapter(getSql, connection);
            if (parameters?.Any() == true) {
                //有参数
                da.SelectCommand.Parameters.AddRange(parameters);
                da.SelectCommand.BindByName = true;
            }
            var dt = new DataTable();
            foreach (var prop in fieldProps) {
                dt.Columns.Add(prop.Name,
                    prop.PropertyType.IsGenericType &&
                    prop.PropertyType.GetGenericTypeDefinition() == typeof(Nullable<>)
                        ? typeof(object)
                        : prop.PropertyType);
            }
            da.Fill(dt);
            if (dt.Rows.Count > 0) {
                //取出数据反序列化到对象模型
                var json = JsonConvert.SerializeObject(dt);
                result = JsonConvert.DeserializeObject<List<T>>(json);
                return result;
            }

            return result;
        }
        /// <summary>
        /// 根据查询条件获取分页数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="pageIndex">分页页码</param>
        /// <param name="pageSize">分页大小</param>
        /// <param name="where">查询条件</param>
        /// <param name="sort">排序字段</param>
        /// <returns></returns>
        public List<T> GetPagerList<T>(int pageIndex, int pageSize, string where, string sort, params OracleParameter[] parameters) where T : IOracleDataTable {
            var tableName = typeof(T).Name;
            var fieldProps = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance);
            var fieldNames = fieldProps.Select(p => p.Name);
            //组织字段
            var fields = string.Join(",", fieldNames.Select(f => $"A.{f} as \"{f}\""));
            //分页信息
            int maxNum = pageSize * pageIndex;
            int minNum = Convert.ToInt32(pageSize * (pageIndex - 1)) + 1;
            //组织语句
            var getSql = $"SELECT * FROM (SELECT {fields},ROWNUM RN FROM  (SELECT * FROM {tableName} where {where} order by {sort} DESC nulls last) A WHERE ROWNUM <= {maxNum}) WHERE RN >= {minNum} ";
            var result = new List<T>();
            var da = new OracleDataAdapter(getSql, connection);
            if (parameters?.Any() == true) {
                //有参数
                da.SelectCommand.Parameters.AddRange(parameters);
                da.SelectCommand.BindByName = true;
            }
            var dt = new DataTable();
            foreach (var prop in fieldProps) {
                dt.Columns.Add(prop.Name,
                    prop.PropertyType.IsGenericType &&
                    prop.PropertyType.GetGenericTypeDefinition() == typeof(Nullable<>)
                        ? typeof(object)
                        : prop.PropertyType);
            }
            dt.Columns.Add("RN", typeof(string));
            da.Fill(dt);
            if (dt.Rows.Count > 0) {
                //取出数据反序列化到对象模型
                var json = JsonConvert.SerializeObject(dt);
                result = JsonConvert.DeserializeObject<List<T>>(json);
                return result;
            }
            return result;
        }

        /// <summary>
        /// 根据SQL查询获取分页数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="pageIndex">分页页码</param>
        /// <param name="pageSize">分页大小</param>
        /// <param name="where">查询条件</param>
        /// <param name="sort">排序字段</param>
        /// <returns></returns>
        public PageResult GetPageResultWithSql(string mainSql, string sort, int pageIndex, int pageSize, params OracleParameter[] parameters) {
            //分页信息
            int maxNum = pageSize * pageIndex;
            int minNum = Convert.ToInt32(pageSize * (pageIndex - 1)) + 1;
            //组织语句
            var getSql = $"SELECT * FROM (SELECT A.*,ROWNUM RN FROM  ({mainSql} order by {sort} DESC nulls last) A WHERE ROWNUM <= {maxNum}) WHERE RN >= {minNum} ";
            var da = new OracleDataAdapter(getSql, connection);
            if (parameters?.Any() == true) {
                //有参数
                da.SelectCommand.Parameters.AddRange(parameters);
                da.SelectCommand.BindByName = true;
            }
            var dt = new DataTable();
            da.Fill(dt);
            var total = dt.Rows.Count;
            if (dt.Rows.Count == pageSize) {
                //返回的行数如果与页大小相等，则需要获取总数
                total = GetTotalCountWithSql(mainSql, parameters);
            }
            return new PageResult() { PageIndex = pageIndex, PageSize = pageSize, TotalCount = total, List = dt };
        }

        public PageResult<T> GetPageResultWithSql<T>(string mainSql, string sort, int pageIndex, int pageSize, params OracleParameter[] parameters) {
            //分页信息
            int maxNum = pageSize * pageIndex;
            int minNum = Convert.ToInt32(pageSize * (pageIndex - 1)) + 1;
            //组织语句
            var getSql = $"SELECT * FROM (SELECT A.*,ROWNUM RN FROM  ({mainSql} order by {sort} DESC nulls last) A WHERE ROWNUM <= {maxNum}) WHERE RN >= {minNum} ";
            var da = new OracleDataAdapter(getSql, connection);
            if (parameters?.Any() == true) {
                //有参数
                da.SelectCommand.Parameters.AddRange(parameters);
                da.SelectCommand.BindByName = true;
            }
            var fieldProps = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance);
            var dt = new DataTable();
            foreach (var prop in fieldProps) {
                dt.Columns.Add(prop.Name,
                    prop.PropertyType.IsGenericType &&
                    prop.PropertyType.GetGenericTypeDefinition() == typeof(Nullable<>)
                        ? typeof(object)
                        : prop.PropertyType);
            }
            dt.Columns.Add("RN", typeof(string));
            da.Fill(dt);
            var total = dt.Rows.Count;
            //分页总数错误问题 edit by wy
            //if (dt.Rows.Count == pageSize) {
            //返回的行数如果与页大小相等，则需要获取总数
            total = GetTotalCountWithSql(mainSql, parameters);
            //}

            return new PageResult<T> {
                Page = new Page() { PageIndex = pageIndex, PageSize = pageSize, Total = total },
                DataTable = JsonConvert.DeserializeObject<List<T>>(JsonConvert.SerializeObject(dt))

            };
        }
        public List<T> GetListWithSql<T>(string mainSql, string sort, params OracleParameter[] parameters) {
            //组织语句
            var getSql = $"{mainSql} order by {sort} DESC nulls last";
            //LogService.WriteLogs(null,"sql",getSql, null);
            var da = new OracleDataAdapter(getSql, connection);
            if (parameters?.Any() == true) {
                //有参数
                da.SelectCommand.Parameters.AddRange(parameters);
            }
            var fieldProps = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance);
            var dt = new DataTable();
            foreach (var prop in fieldProps) {
                dt.Columns.Add(prop.Name,
                    prop.PropertyType.IsGenericType &&
                    prop.PropertyType.GetGenericTypeDefinition() == typeof(Nullable<>)
                        ? typeof(object)
                        : prop.PropertyType);
            }
            da.Fill(dt);

            return JsonConvert.DeserializeObject<List<T>>(JsonConvert.SerializeObject(dt));
        }



        /// <summary>
        /// 根据SQL查询获取记录总数
        /// </summary>
        /// <param name="mainSql"></param>
        /// <returns></returns>
        internal int GetTotalCountWithSql(string mainSql, params OracleParameter[] parameters) {
            var getSql = $"SELECT COUNT(1) FROM ({mainSql})";
            var cmd = new OracleCommand(getSql, connection);
            if (parameters?.Any() == true) {
                //有参数
                cmd.Parameters.AddRange(parameters);
                cmd.BindByName = true;
            }
            cmd.Connection.Open();
            var total = Convert.ToInt32(cmd.ExecuteScalar());
            cmd.Connection.Close();
            return total;
        }

        /// <summary>
        /// 根据条件，获取匹配条件的记录数
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="where"></param>
        /// <param name="parameters"></param>
        /// <returns></returns>
        internal int GetCount<T>(string where, params OracleParameter[] parameters) where T : IOracleDataTable {
            var tableName = typeof(T).Name;
            //组织字段
            var fields = "COUNT(0) AS CNT";

            //组织语句
            var getSql = $"SELECT {fields} FROM {tableName} WHERE {where}";

            var da = new OracleDataAdapter(getSql, connection);
            if (parameters?.Any() == true) {
                //有参数
                da.SelectCommand.Parameters.AddRange(parameters);
                da.SelectCommand.BindByName = true;
            }
            var dt = new DataTable();
            da.Fill(dt);
            if (dt.Rows.Count > 0) {
                //取出数据反序列化到对象模型
                return Convert.ToInt32(dt.Rows[0][0]);
            }

            return 0;
        }

        /// <summary>
        /// 判断数据是否存在
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="where"></param>
        /// <param name="parameters"></param>
        /// <returns></returns>
        internal bool Exists<T>(string where, params OracleParameter[] parameters) where T : IOracleDataTable {
            var tableName = typeof(T).Name;

            //组织语句
            var getSql = $"SELECT ID FROM {tableName} WHERE {where} AND ROWNUM < 2";

            var da = new OracleDataAdapter(getSql, connection);
            if (parameters?.Any() == true) {
                //有参数
                da.SelectCommand.Parameters.AddRange(parameters);
                da.SelectCommand.BindByName = true;
            }
            var dt = new DataTable();
            da.Fill(dt);
            return dt.Rows.Count > 0;
        }

        public void InsertOne<T>(T entity) where T : IOracleDataTable {
            var tableName = typeof(T).Name;
            var props = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance);
            var fieldNames = props.Select(p => p.Name).ToArray();

            var fieldParamDic = new Dictionary<string, string>();

            for (int i = 0; i < fieldNames.Length; i++) {
                fieldParamDic.Add(fieldNames[i], $":{i}");
            }

            var insertSql =
                $"INSERT INTO {tableName}({string.Join(",", fieldNames)}) Values({string.Join(",", fieldParamDic.Values)})";
            var insertCmd = new OracleCommand(insertSql, connection);
            foreach (var fieldName in fieldParamDic.Keys) {
                var prop = props.First(p => p.Name == fieldName);
                if (prop.GetCustomAttribute(typeof(CLOBAttribute)) != null) {
                    insertCmd.Parameters.Add(fieldParamDic[fieldName], OracleDbType.Clob);
                }
                else if (prop.GetCustomAttribute(typeof(BLOBAttribute)) != null) {
                    insertCmd.Parameters.Add(fieldParamDic[fieldName], OracleDbType.Blob);
                }
                else {
                    insertCmd.Parameters.Add(fieldParamDic[fieldName], GetOracleDbType(prop.PropertyType));
                }
                insertCmd.Parameters[fieldParamDic[fieldName]].Value = prop.GetValue(entity);
            }

            try {
                if (insertCmd.Connection.State != ConnectionState.Open)
                    insertCmd.Connection.Open();
                insertCmd.ExecuteNonQuery();
            }
            catch {
                insertCmd.Transaction?.Rollback();
                throw;
            }
            finally {
                if (connection.State == ConnectionState.Open) {
                    connection.Close();
                }
            }
        }

        public void UpdateOne<T>(T entity) where T : IOracleDataTable {
            var tableName = typeof(T).Name;
            var props = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance);
            var updateList = GetUpdateList(entity);

            if (updateList.Any()) {
                var updateSql = $"UPDATE {tableName} SET {string.Join(",", updateList.Select(kv => $"{kv.Key}=:{kv.Key}"))} WHERE ID=:id";
                var updateCmd = new OracleCommand(updateSql, connection);
                updateCmd.Parameters.Add(":id", OracleDbType.Varchar2);
                updateCmd.Parameters[":id"].Value = entity.ID;

                foreach (var kv in updateList) {
                    var prop = props.First(p => p.Name == kv.Key);
                    var pname = $":{kv.Key}";
                    if (prop.GetCustomAttribute(typeof(CLOBAttribute)) != null) {
                        updateCmd.Parameters.Add(pname, OracleDbType.Clob);
                    }
                    else if (prop.GetCustomAttribute(typeof(BLOBAttribute)) != null) {
                        updateCmd.Parameters.Add(pname, OracleDbType.Blob);
                    }
                    else {
                        updateCmd.Parameters.Add(pname, GetOracleDbType(prop.PropertyType));
                    }
                    updateCmd.Parameters[pname].Value = prop.GetValue(entity);
                }

                try {
                    updateCmd.Connection.Open();
                    updateCmd.BindByName = true;
                    updateCmd.ExecuteNonQuery();
                }
                catch {
                    updateCmd.Transaction?.Rollback();
                    throw;
                }
                finally {
                    if (connection.State == ConnectionState.Open) {
                        connection.Close();
                    }
                }
            }
        }

        /// <summary>
        /// 获取实体对象相对于数据库的更新列表
        /// </summary>
        /// <typeparam name="T">类型</typeparam>
        /// <param name="entity">更新对象</param>
        /// <returns>返回的<see cref="Dictionary{TKey,TValue}"/>中，Key代表更新的字段，Value代表更新参数变量</returns>
        internal Dictionary<string, string> GetUpdateList<T>(T entity) where T : IOracleDataTable {
            IOracleDataTable row = entity;
            var record = GetById<T>(row.ID);

            //比较record和entity的变化
            var props = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance);
            var fieldNames = props.Select(p => p.Name).Where(p => p != "ID").ToArray();
            var updateList = new Dictionary<string, string>();
            foreach (var fieldName in fieldNames) {
                var prop = props.First(p => p.Name == fieldName);
                var old_val = prop.GetValue(record);
                var new_val = prop.GetValue(entity);

                if (old_val != null && new_val == null || old_val == null && new_val != null || old_val != null && !old_val.Equals(new_val)) {
                    updateList.Add(fieldName, new_val?.ToString());
                }
            }

            return updateList;
        }

        public void DeleteOne<T>(T entity) where T : IOracleDataTable {
            var tableName = typeof(T).Name;
            IOracleDataTable record = entity;

            var deleteSql = $"DELETE FROM {tableName} WHERE ID=:id";
            var deleteCmd = new OracleCommand(deleteSql, connection);
            deleteCmd.Parameters.Add(":id", OracleDbType.Varchar2);
            deleteCmd.Parameters[":id"].Value = record.ID;

            try {
                deleteCmd.Connection.Open();
                deleteCmd.BindByName = true;
                deleteCmd.ExecuteNonQuery();
            }
            catch {
                deleteCmd.Transaction?.Rollback();
                throw;
            }
            finally {
                if (connection.State == ConnectionState.Open) {
                    connection.Close();
                }
            }
        }

        public void DeleteMany<T>(string where, params OracleParameter[] parameters) where T : IOracleDataTable {
            var tableName = typeof(T).Name;

            var deleteSql = $"DELETE FROM {tableName} WHERE {where}";
            var deleteCmd = new OracleCommand(deleteSql, connection);

            if (parameters?.Any() == true) {
                //有参数
                deleteCmd.Parameters.AddRange(parameters);

            }

            try {
                deleteCmd.Connection.Open();
                deleteCmd.BindByName = true;
                deleteCmd.ExecuteNonQuery();
            }
            catch {
                deleteCmd.Transaction?.Rollback();
                throw;
            }
            finally {
                if (connection.State == ConnectionState.Open) {
                    connection.Close();
                }
            }
        }

        /// <summary>
        /// 获取业务流水号
        /// </summary>
        /// <returns></returns>
        public string GetNewBusinessNo() {
            var sql =
                "SELECT TO_CHAR(SYSDATE,'yyyyMMdd')||LPAD(BUSINESSNO_SEQ.NEXTVAL,5,'0') BUSINESSNO FROM DUAL";
            var cmd = new OracleCommand(sql, connection);
            try {
                cmd.Connection.Open();
                var busno = cmd.ExecuteScalar().ToString().Trim();
                return busno;
            }
            catch {
                cmd.Transaction?.Rollback();
                throw;
            }
            finally {
                if (connection.State == ConnectionState.Open) {
                    connection.Close();
                }
            }
        }

        /// <summary>
        /// 根据属性的类型返回Oracle类型
        /// </summary>
        /// <param name="propertyType"></param>
        /// <returns></returns>
        private OracleDbType GetOracleDbType(Type propertyType) {
            if (propertyType == typeof(int) || propertyType == typeof(int?)) {
                return OracleDbType.Int32;
            }

            if (propertyType == typeof(double) || propertyType == typeof(double?)) {
                return OracleDbType.Double;
            }

            if (propertyType == typeof(decimal) || propertyType == typeof(decimal?)) {
                return OracleDbType.Decimal;
            }

            if (propertyType == typeof(DateTime) || propertyType == typeof(DateTime?)) {
                return OracleDbType.Date;
            }

            if (propertyType.IsEnum) {
                return OracleDbType.Int32;
            }

            //todo 继续添加类型匹配

            return OracleDbType.Varchar2;
        }

        #region IDisposable

        /// <summary>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</summary>
        public void Dispose() {
            //检查连接对象是否已关闭，如果未关闭，则将其关闭，连接返回连接池，而不需要销毁
            if (connection?.State == ConnectionState.Open) {
                connection.Close();
            }
        }

        #endregion
    }

    /// <summary>
    /// 实现此接口的class，默认所有的属性都是字段名
    /// </summary>
    public interface IOracleDataTable {
        string ID { get; set; }
    }
}
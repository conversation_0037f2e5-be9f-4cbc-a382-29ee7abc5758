﻿using System.Text.RegularExpressions;

namespace SDCPCWeb.Extensions {
    /// <summary>
    /// Sql注入检查扩展类
    /// </summary>
    public static class SqlInjectExtension {
        /// <summary>
        /// 判断字符串是否包含Sql注入字符
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public static bool IsSqlInject(this string data) {
            if (string.IsNullOrWhiteSpace(data))
                return false;
            string patten = @".*(').*"; //是否包含单引号'
            Regex regex = new Regex(patten);
            return regex.IsMatch(data);
        }
    }
}

﻿using System;
using System.Security.Cryptography;
using System.Text;

namespace SDCPCWeb.Encrypts {
    /// <summary>
    /// AES加解密类
    /// </summary>
    public class AESEncrypt {
        /// <summary>
        ///  加密
        /// </summary>
        /// <param name="data">明文</param>
        /// <param name="key">密钥</param>
        /// <returns></returns>
        public static string Encrypt(string data, string key = "NNBdc.2020-10-15") {
            if (string.IsNullOrEmpty(data)) return null;
            Byte[] toEncryptArray = Encoding.UTF8.GetBytes(data);

            RijndaelManaged rm = new RijndaelManaged {
                Key = Encoding.UTF8.GetBytes(key),
                Mode = CipherMode.ECB,
                Padding = PaddingMode.PKCS7
            };

            ICryptoTransform cTransform = rm.CreateEncryptor();
            Byte[] resultArray = cTransform.TransformFinalBlock(toEncryptArray, 0, toEncryptArray.Length);
            return Convert.ToBase64String(resultArray);
        }

        /// <summary>
        ///  解密
        /// </summary>
        /// <param name="data">密文</param>
        /// <param name="key">密钥</param>
        /// <returns></returns>
        public static string Decrypt(string data, string key = "NNBdc.2020-10-15") {
            if (string.IsNullOrEmpty(data)) return null;
            Byte[] toEncryptArray = Convert.FromBase64String(data);

            RijndaelManaged rm = new RijndaelManaged {
                Key = Encoding.UTF8.GetBytes(key),
                Mode = CipherMode.ECB,
                Padding = PaddingMode.PKCS7
            };

            ICryptoTransform cTransform = rm.CreateDecryptor();
            Byte[] resultArray = cTransform.TransformFinalBlock(toEncryptArray, 0, toEncryptArray.Length);

            return Encoding.UTF8.GetString(resultArray);
        }
    }
}
﻿using SDCPCWeb.Models;
using SDCPCWeb.Models.Result;
using SDCPCWeb.Models.System;
using SDCPCWeb.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;

namespace SDCPCWeb.Controllers {
    [RoutePrefix("sdcapi/DataDirectory"), BDCAuthorize]
    public class DataDirectoryController : ApiController {
        private readonly OracleDataService service = new OracleDataService();
        /// <summary>
        /// 根据分类名称获取普通或树结构集合
        /// </summary>
        /// <param name="name">分类名称</param>
        /// <param name="type">返回类型</param>
        /// <returns></returns>
        [HttpGet, Route("GetDirectoryListOrTree")]
        public ApiResult GetDirectoryListOrTree(string name,string type) {
            if (name == null) {
                return new ApiResult { StateCode = 0, Message = "获取失败，请输入正确的信息" };
            }
            List<DataDirectoryInfo> dataList = service.GetList<DataDirectoryInfo>("Name='" + name + "'");
            if (dataList.Count == 0) {
                return new ApiResult { StateCode = 0, Message = "获取失败，查询不到该分类的任何信息" };
            }
            try {
                //需要返回集合是树结构时
                if (type == "tree") {
                    var tree = getTreeNodes(dataList[0].ID);
                    return new ApiResult { StateCode = 1, Data = tree, Message = "" };
                }
                //type不为tree时，默认按普通列表返回
                else {
                    List<DataDirectoryInfo> list = service.GetList<DataDirectoryInfo>("ParentID='" + dataList[0].ID + "' order by sort");
                    List<dynamic> ResultInfo = new List<dynamic>();
                    list.ForEach(data => {
                        var item = new {
                            id = data.ID,
                            name = data.Name
                        };
                        ResultInfo.Add(item);
                    });
                    return new ApiResult { StateCode = 1, Data = ResultInfo, Message = "" };
                }  
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 0, Message = "获取失败，错误信息：" + e.Message + "" };
            } 
        }
        /// <summary>
        /// 获取树节点信息
        /// </summary>
        /// <param name="pid"></param>
        /// <returns></returns>
        private List<DataDirectoryTree> getTreeNodes(string pid) {
            var list = new List<DataDirectoryTree>() { };
            List<DataDirectoryInfo> dataList = service.GetList<DataDirectoryInfo>("ParentID='" + pid + "' order by sort");
            if (dataList != null && dataList.Count >= 1) {
                dataList.ForEach(data => {
                    var item = new DataDirectoryTree {
                        value = data.ID,
                        label = data.Name,
                        children = null
                    };
                    if (this.getTreeNodes(item.value).Count > 0) {
                        //避免空集合Bug 在子集有数据时才初始化
                        item.children = new List<DataDirectoryTree> { };
                        item.children.AddRange(getTreeNodes(item.value));
                    }
                    list.Add(item);
                });
            }
            return list;
        }
    }
}

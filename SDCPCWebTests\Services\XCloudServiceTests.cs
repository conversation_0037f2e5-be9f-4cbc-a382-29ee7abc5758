﻿using System;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Newtonsoft.Json;
using SDCPCWeb.Models.BusinessFlow;
using SDCPCWeb.Services;

namespace SDCPCWebTests.Services {
    [TestClass]
    public class XCloudServiceTests {
        [TestMethod]
        public async Task TestGetSpyjByBusinessId() {
            var result = await XCloudService.GetSpyjByBusinessId("b6dbc51b48354638867b7c57e14cd099", nameof(RealEstateActualSurveyFlow));
            Console.WriteLine(result);
        }

        [TestMethod]
        public async Task TestGetFlowStepRecordsByBusinessId() {
            var result = await XCloudService.GetFlowStepRecordsByBusinessId("b6dbc51b48354638867b7c57e14cd099", "RealEstateActualSurveyFlow");
            Console.WriteLine(result);
        }

        [TestMethod]
        public async Task TestGetLandUseList() {
            MongoCache.Initialize("HangfireMongoDB");
            var result = await XCloudService.GetLandUseListCache();
            Console.WriteLine(JsonConvert.SerializeObject(result));
        }
    }
}

﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web.Configuration;
using SDCPCWeb.Controllers;
using SDCPCWeb.Models.System;
using SDCPCWeb.Services;

namespace SDCPCWeb.Models.EstateConstruction {
    /// <summary>
    /// 工程规划许可证信息
    /// </summary>
    public class ProjectPlanPermissionInfo {
        //工程规划许可证号
        public string Code { get; set; }
        //建设单位
        public string ConstructCompany { get; set; }
        //建设地址
        public string Address { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string ProjectName { get; set; }
        //附图编号
        public string AppendixImgNumber { get; set; }
        //不同建筑类别
        public List<Building> Buildings { get; set; }
        // 建筑性质
        public string BuildingNature { get; set; }
        // 建筑结构
        public string BuildingStructure { get; set; }
        // 建筑总面积
        public TotalAreaOrFloor TotalArea { get; set; }
        // 基底面积
        public decimal? BaseArea { get; set; }
        // 层数
        public TotalAreaOrFloor Floors { get; set; }
        // 建筑总高度
        public decimal? BuildingHeight { get; set; }
        // 栋数
        public int? BuildingBlockNumber { get; set; }
        // 投资（万元）
        public decimal? Invest { get; set; }
        // 套型
        public List<SetType> SetType { get; set; }
        //其它
        public SetType Others { get; set; }
        // 备注
        public string Remark { get; set; }
        /// <summary>
        /// 是否自行添加
        /// </summary>
        public bool? IsAddNew { get; set; }

        /// <summary>
        /// 报建编号
        /// </summary>
        public string CaseCode { get; set; }

        /// <summary>
        /// 审批意见
        /// </summary>
        public string SPNR { get; set; }

        /// <summary>
        /// 已缴纳城建配套费标识说明（值分别有：是、否、已减免、null）
        /// </summary>
        public string BalanceCJPTF { get; set; }

        /// <summary>
        /// 是否已缴纳城建配套费
        /// </summary>
        public bool? IsBalanceCJPTF {
            get {
                if (BalanceCJPTF == "是" || BalanceCJPTF == "已减免")
                    return true;
                return false;
            }
        }

        /// <summary>
        /// 工规证办结时间(yyyy-MM-dd HH:mm:ss)
        /// </summary>
        public string CertificateDate { get; set; }

        /// <summary>
        /// 是否需要人工审核（预核即时办结业务专用）
        /// </summary>
        public bool? NeedManualAudit {
            get {
                DateTime certificateDate;
                if (DateTime.TryParse(CertificateDate, out certificateDate)) {
                    // 从数据字典表中获取配置的开始时间和结束时间
                    var config = GetAuditDateConfig();

                    if (certificateDate.Date >= config.StartDate &&
                        certificateDate.Date < config.EndDate) {
                        if (BalanceCJPTF != "是" && BalanceCJPTF != "已减免") {

                            //判断是否有排除人工审核的工规证号
                            string rootPath = WebConfigurationManager.AppSettings["RootPath"];
                            string numbersFile = rootPath + "\\工规证\\Numbers.txt";
                            if (File.Exists(numbersFile)) {
                                var numbers = File.ReadLines(numbersFile);
                                if (numbers.Any(s => s == Code)) {
                                    return false;
                                }
                            }

                            return true;
                        }

                    }
                }

                return false;
            }
        }

        /// <summary>
        /// 从数据字典获取时间配置
        /// </summary>
        /// <returns>包含开始时间和结束时间的配置对象</returns>
        /// <exception cref="Exception">当无法获取配置或配置不存在时抛出异常</exception>
        private static AuditDateConfig GetAuditDateConfig() {
            var config = new AuditDateConfig();

            try {
                using (var service = new OracleDataService()) {
                    // 获取配置项，在数据字典中创建了名为"工规证时间识别范围"的分类
                    var configList = service.GetList<DataDirectoryInfo>("ParentID=(SELECT ID FROM DataDirectoryInfo WHERE Name ='工规证时间识别范围') AND STATECODE =1");


                    if (configList == null || configList.Count == 0) {
                        throw new Exception("工规证时间识别范围不存在，请联系管理员！");
                    }

                    var startDateConfig = configList.FirstOrDefault(c => c.Name == "开始时间");
                    var endDateConfig = configList.FirstOrDefault(c => c.Name == "结束时间");


                    if (startDateConfig == null || string.IsNullOrEmpty(startDateConfig.Remark)) {
                        throw new Exception("工规证时间识别范围开始时间不存在或为空");
                    }

                    if (endDateConfig == null || string.IsNullOrEmpty(endDateConfig.Remark)) {
                        throw new Exception("工规证时间识别范围结束时间不存在或为空");
                    }

                    DateTime startDate;
                    if (!DateTime.TryParse(startDateConfig.Remark, out startDate)) {
                        throw new Exception($"工规证时间识别范围开始时间格式错误: {startDateConfig.Remark}");
                    }

                    DateTime endDate;
                    if (!DateTime.TryParse(endDateConfig.Remark, out endDate)) {
                        throw new Exception($"工规证时间识别范围结束时间格式错误: {endDateConfig.Remark}");
                    }

                    config.StartDate = startDate;
                    config.EndDate = endDate;

                }
            } catch (Exception e) {
                throw;
            }

            return config;
        }

        /// <summary>
        /// 工规证办结时间判断范围
        /// </summary>
        private class AuditDateConfig {
            public DateTime StartDate { get; set; }
            public DateTime EndDate { get; set; }
        }
    }


    /// <summary>
    /// 工程规划许可证信息，建筑信息
    /// </summary>
    public class Building {
        // 建筑类别
        public string Type { get; set; }
        // 面积（平方米）
        public decimal? Area { get; set; }
    }

    /// <summary>
    /// 工程规划许可证信息，层数分类
    /// </summary>
    public class TotalAreaOrFloor {
        //地上
        public decimal? Aboveground { get; set; }
        // 地下
        public decimal? Underground { get; set; }
    }

    /// <summary>
    /// 工程规划许可证信息，套型信息
    /// </summary>
    public class SetType {
        // 套型
        public string Name { get; set; }
        // 套数
        public int? Number { get; set; }
        // 面积
        public decimal? Area { get; set; }
    }

}
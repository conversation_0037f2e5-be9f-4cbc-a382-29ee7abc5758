﻿using System;
using System.Linq;
using System.Threading.Tasks;
using System.Web.Configuration;
using Hangfire;
using Hangfire.Console;
using Hangfire.Dashboard;
using Hangfire.Mongo;
using Microsoft.AspNet.Identity;
using Microsoft.AspNet.Identity.Owin;
using Microsoft.Owin;
using Microsoft.Owin.Security.Cookies;
using Owin;
using SDCPCWeb.Filters;
using SDCPCWeb.Jobs;
using SDCPCWeb.Models.Mongo;
using SDCPCWeb.Models.System;
using SDCPCWeb.Services;

[assembly: OwinStartup(typeof(SDCPCWeb.Startup))]

namespace SDCPCWeb {
    public class Startup {
        public void Configuration(IAppBuilder app) {
            app.UseCors(Microsoft.Owin.Cors.CorsOptions.AllowAll);
            app.UseExternalSignInCookie(DefaultAuthenticationTypes.ExternalCookie);
            GlobalConfiguration.Configuration.UseMongoStorage(
                WebConfigurationManager.ConnectionStrings["HangfireMongoDB"].ConnectionString,
                new MongoStorageOptions() { MigrationOptions = new MongoMigrationOptions(MongoMigrationStrategy.Migrate) });
            GlobalConfiguration.Configuration.UseConsole()
                .UseDashboardMetric(DashboardMetrics.FailedCount).UseDashboardMetric(DashboardMetrics.ProcessingCount);
            app.UseHangfireServer(new BackgroundJobServerOptions() { WorkerCount = 200 });
            app.UseHangfireDashboard("/sdcapi/hangfire",
                new DashboardOptions {
                    Authorization = new IDashboardAuthorizationFilter[] {
                        new SDCHangfireAuthorizationFilter()
                    }
                });
            GlobalJobFilters.Filters.Add(new AutomaticRetryAttribute() { Attempts = 3 });
            //初始化工作流模型定义
            BusinessFlowConfig.Initialize();
            //初始化MongoDB魂村
            MongoCache.Initialize("HangfireMongoDB");
            //初始化Hangfire管理员信息
            InitializeHangfireAdmin();
            //初始化周期性计划任务
            InitializeHangFireJobs();
        }

        /// <summary>
        /// 初始化Hangfire管理员
        /// </summary>
        private void InitializeHangfireAdmin() {
            var mongo = new MongoService<HangfireAdmin>();
            if (!mongo.Get(admin => admin.Id != null).Any()) {
                mongo.Insert(new HangfireAdmin() {
                    Id = Guid.NewGuid().ToString("N"),
                    AdminPersonName = "吕俊宏",
                    AdminPersonNo = "450821198707135810"
                });
            }
        }

        /// <summary>
        /// 初始化系统周期性作业
        /// </summary>
        private void InitializeHangFireJobs() {
            //更新测绘单位排序
            RecurringJob.AddOrUpdate(() => SurveyCompanyJob.RefreshSurveyCompanyOrder(null), Cron.Daily(16, 0));
            //按计划清理缓存
            RecurringJob.AddOrUpdate(() => MongoCache.ClearExpiredCaches(), Cron.Minutely);
            //检测注册测绘师授权过期
            RecurringJob.AddOrUpdate(() => SurveyCompanyJob.CheckSurveyMasterAuthExpired(null), Cron.Daily(16, 5));

            //每天0点同步单位信息到综合服务平台
            RecurringJob.AddOrUpdate(() => CompanyService.SyncAllCompanyInfo(null), Cron.Daily, TimeZoneInfo.Local);
            //每隔3分钟同步单位信息到综合服务平台
            RecurringJob.AddOrUpdate(() => CompanyService.SyncCompanyInfo(null), Cron.MinuteInterval(3), TimeZoneInfo.Local);

            //检查是否有注册测绘师即将到期并提醒单位管理员
            RecurringJob.AddOrUpdate(() => CompanyService.CheckZCCKSExpire(null), Cron.Daily(9, 0), TimeZoneInfo.Local);

            //更新土地用途列表缓存
            RecurringJob.AddOrUpdate(() => XCloudService.UpdateLandUseCache(null), Cron.MinuteInterval(20), TimeZoneInfo.Local);
        }
    }
}

﻿using Newtonsoft.Json;
using SDCPCWeb.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace SDCPCWeb.Models.EstateConstruction {
    /// <summary>
    /// 房建项目管理--自然幢信息
    /// </summary>
    public class NatureBuildInfoModel {
        /// <summary>
        /// ID
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 关联宗地ID
        /// </summary>
        public string RelationId { get; set; }
        /// <summary>
        /// 工规证号
        /// </summary>
        public string WorkRegulationNo { get; set; }
        /// <summary>
        /// 审核状态 0 未审核 1 审核中 2 已审核
        /// </summary>
        public string StateCode { get; set; }
    }
    public class NatureBuildInfo : NatureBuildInfoModel, IOracleDataTable {
        public NatureBuildInfoModel ToModel() {
            return JsonConvert.DeserializeObject<NatureBuildInfoModel>(JsonConvert.SerializeObject(this));
        }
    }
}
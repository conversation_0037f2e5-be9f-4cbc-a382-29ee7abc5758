﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Linq;
using System.Web;
using Hangfire.Console;
using Hangfire.Server;
using Newtonsoft.Json.Linq;
using Oracle.ManagedDataAccess.Client;
using SDCPCWeb.Models.BusinessFlow;
using SDCPCWeb.Models.EstateConstruction;

namespace SDCPCWeb.Services {
    /// <summary>
    /// 更新数据服务类
    /// </summary>
    public class UpdateDataService {
        /// <summary>
        /// 更新工规证号与实核业务一对多功能后处理旧数据
        /// </summary>
        /// <param name="console"></param>
        [DisplayName("更新工规证号与实核业务一对多功能后处理旧数据")]
        public static void UpdateEstateProjectInfo(PerformContext console) {
            using (var oracle = new OracleDataService()) {
                string sql = "select BUSINESSCLASS from BUSINESSBASEINFO group by BUSINESSCLASS";
                var dt = oracle.ExecuteQuerySql(sql);
                foreach (DataRow dataRow in dt.Rows) {
                    string businessClass = dataRow[0].ToString();
                    
                    switch (businessClass) {
                        case nameof(RealEstateActualSurveyFlow):
                        case nameof(RealEstateActualResultChangeFlow): {
                                sql =
                                    "select A.ID, A.STATECODE, A.BUSINESSCLASS, B.ProjectPlanPermission from BUSINESSBASEINFO A";
                                sql += " inner join EstateActualSurveyContentInfo B on A.ID = B.ID";
                                sql += $" where A.BUSINESSCLASS = '{businessClass}' and A.StateCode in (1, 2, 3)";
                                sql += " order by A.CreateTime";
                                dt = oracle.ExecuteQuerySql(sql);
                                console?.WriteLine($"businessClass：{businessClass}；数据量：{dt.Rows.Count}");
                                int n = 0;
                                foreach (DataRow dtRow in dt.Rows) {
                                    n++;
                                    string id = dtRow["ID"].ToString();
                                    int stateCode = Convert.ToInt32(dtRow["STATECODE"].ToString());
                                    var projectPlanPermissionStr = dtRow["ProjectPlanPermission"] == DBNull.Value ? "[]" : dtRow["ProjectPlanPermission"].ToString();
                                    bool needCheckEstateProjectInfo = false; //是否需要检查工规证号绑定的业务信息是否存在

                                    if ((stateCode == 1 && projectPlanPermissionStr != "[]") || stateCode == 3) {
                                        //查是否提交到过云平台
                                        int actionId = 5;
                                        if (businessClass == nameof(RealEstateActualResultChangeFlow))
                                            actionId = 4;

                                        //如果有提交到云平台就要检查
                                        if (oracle.Exists<BusinessLinkInfo>(
                                            $"BUSINESSID='{id}' and ACTIONID={actionId}")) {
                                            needCheckEstateProjectInfo = true;
                                        }
                                    }

                                    //如果业务已完成就要检查
                                    if (stateCode == 2) {
                                        needCheckEstateProjectInfo = true;
                                    }

                                    if (needCheckEstateProjectInfo == true) {
                                        //获取业务里的工规证号
                                        JArray projectPlanPermissions = JArray.Parse(projectPlanPermissionStr);
                                        string[] codes = projectPlanPermissions.Select(s => s["Code"].ToString())
                                            .ToArray();

                                        console?.WriteLine($"{n}. id：{id}；codes：{string.Join(",", codes)}");

                                        //判断工规证号是跟该业务绑定
                                        foreach (var code in codes) {
                                            //如果没有绑定
                                            if (oracle.Exists<EstateProjectInfo>(
                                                $"PlanCerificateCode=:code and RealSurveyID=:id",
                                                new OracleParameter(":code", OracleDbType.Varchar2) { Value = code },
                                                new OracleParameter(":id", OracleDbType.Varchar2) { Value = id }) == false) {

                                                var surveyState = SurveyState.Invalid;
                                                if (stateCode == 1)
                                                    surveyState = SurveyState.CompleteSurvey;
                                                if (stateCode == 2)
                                                    surveyState = SurveyState.CompleteAudit;

                                                EstateProjectInfo model = new EstateProjectInfo() {
                                                    ID = EstateProjectInfo.CreateEstateProjectInfoId(code),
                                                    PlanCerificateCode = code,
                                                    PutLineSurveyID = null,
                                                    PutLineSurveyState = SurveyState.NotSurvey,
                                                    PreSurveyID = null,
                                                    PreSurveyState = SurveyState.NotSurvey,
                                                    CheckConditionSurveyID = null,
                                                    CheckConditionSurveyState = SurveyState.NotSurvey,
                                                    RealSurveyID = id,
                                                    RealSurveyState = surveyState
                                                };

                                                oracle.InsertOne(model);

                                                model = new EstateProjectInfo() {
                                                    ID = EstateProjectInfo.CreateEstateProjectInfoId(code),
                                                    PlanCerificateCode = code,
                                                    PutLineSurveyID = null,
                                                    PutLineSurveyState = SurveyState.NotSurvey,
                                                    PreSurveyID = null,
                                                    PreSurveyState = SurveyState.NotSurvey,
                                                    CheckConditionSurveyID = id,
                                                    CheckConditionSurveyState = surveyState,
                                                    RealSurveyID = null,
                                                    RealSurveyState = SurveyState.NotSurvey
                                                };

                                                oracle.InsertOne(model);
                                            }
                                        }
                                        
                                    }
                                }
                                break;
                            }
                    }
                }
            }
        }
    }
}
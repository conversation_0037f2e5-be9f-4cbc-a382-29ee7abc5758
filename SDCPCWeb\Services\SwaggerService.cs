﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace SDCPCWeb.Services {
    public class SwaggerService {
        public static async Task<byte[]> CreateMarkdownFile(string swaggerJsonUrl) {
            var httpClient = HttpClientFactory.Create();
            var responseMessage = await httpClient.GetAsync(swaggerJsonUrl);
            var responseTxt = await responseMessage.Content.ReadAsStringAsync();
            var jObj = JObject.Parse(responseTxt);
            var paths = jObj["paths"];
            StringBuilder sb = new StringBuilder();
            int i = 0;
            foreach (var jPath in paths) {
                JProperty property = jPath.ToObject<JProperty>();

                //接口url地址
                string url = property.Name;

                //接口method，get或者post
                string method = property.Value["get"] != null ? "get" : "post";

                var methodObj = property.Value[method];
                if (methodObj == null) continue;
                
                //接口标题
                string title = methodObj["summary"]?.ToString();

                i++;
                sb.AppendLine("");
                sb.AppendLine($"# 接口{i}、{title}");
                sb.AppendLine("");
                sb.AppendLine($"## 接口信息");
                sb.AppendLine("");

                var tags = methodObj["tags"].ToObject<List<string>>();
                var param = JArray.FromObject(methodObj["parameters"]);

                sb.AppendLine($"- **所属控制器：** {string.Join("、", tags)}");
                sb.AppendLine("");
                sb.AppendLine($"- **接口地址：** {url}");
                sb.AppendLine("");
                sb.AppendLine($"- **请求方式：**{method.ToUpper()}  ");
                sb.AppendLine("");
                if (method == "get") {
                    sb.AppendLine($"- **Content-Type：**application/x-www-form-urlencoded");
                }
                else {
                    //参数位置
                    bool isQuery = false;
                    foreach (var item in param) {
                        if (item["in"].ToString() == "header") continue;
                        if (item["in"].ToString() == "query") {
                            isQuery = true;
                            break;
                        }
                    }

                    if (isQuery) {
                        sb.AppendLine($"- **Content-Type：**application/x-www-form-urlencoded");
                    }
                    else {
                        sb.AppendLine($"- **Content-Type：**application/json");
                    }
                }
                sb.AppendLine("");
                sb.AppendLine($"- **请求参数**：");
                sb.AppendLine("");

                
                int n = 0;
                if (param.Where(s => s["in"]?.ToString() != "header").Any()) {
                    sb.AppendLine("|参数名|数据类型|说明|是否必填|");
                    sb.AppendLine("|-|-|-|-|");
                }
                foreach (var item in param) {
                    if (item["in"].ToString() == "header") continue;
                    n++;

                    var refClass = (item["schema"]?["$ref"] ?? item["type"])?.ToString() ?? "";
                    if (refClass.StartsWith("#/definitions/"))
                        refClass = refClass.Replace("#/definitions/", "") + "类";
                    sb.AppendLine($"|{item["name"]}|{refClass}|{item["description"]}|{item["required"]}|");
                }

                sb.AppendLine("");
                sb.AppendLine($"- **响应类型**：");
                sb.AppendLine($"");
                var responseType = (methodObj["responses"]?["200"]?["schema"]?["$ref"] ??
                                      methodObj["responses"]?["200"]?["schema"]?["type"])?.ToString();
                if (responseType == "#/definitions/ApiResult") {
                    sb.AppendLine($"  JSON");
                    sb.AppendLine($"");
                    sb.AppendLine($"返回结构：");
                    sb.AppendLine("");
                    sb.AppendLine("```json");
                    sb.AppendLine("{");
                    sb.AppendLine($"  //接口状态值 1成功 0失败");
                    sb.AppendLine($"  \"StateCode\": 1,");
                    sb.AppendLine($"  //接口返回信息");
                    sb.AppendLine($"  \"Message\": \"\",");
                    sb.AppendLine($"  //接口返回数据");
                    sb.AppendLine("   \"Data\": {}");
                    sb.AppendLine("}");
                    sb.AppendLine("```");
                }
                else {
                    sb.AppendLine($"  {responseType}");
                }
            }

            sb.AppendLine("");
            sb.AppendLine("");
            sb.AppendLine("");
            sb.AppendLine($"# 类定义 ");

            //获取类的定义
            var definitions = jObj["definitions"];
            foreach (var definition in definitions) {
                JProperty property = definition.ToObject<JProperty>();

                //类的名称
                string className = property.Name;

                //类的描述
                string classDescription = property.Value["description"]?.ToString();
                classDescription = !string.IsNullOrWhiteSpace(classDescription) ? $"({classDescription})" : "";

                sb.AppendLine("");
                sb.AppendLine($"## {className}{classDescription}");
                var classProperties = property.Value["properties"];
                if (classProperties.Any()) {
                    sb.AppendLine("|属性名|数据类型|说明|");
                    sb.AppendLine("|-|-|-|");
                    foreach (var prop in classProperties) {
                        var propObj = prop.ToObject<JProperty>();
                        sb.AppendLine($"|{propObj.Name}|{propObj.Value["type"]}|{propObj.Value["description"]}|");
                    }
                }
            }

            return Encoding.UTF8.GetBytes(sb.ToString());
        }
    }
}
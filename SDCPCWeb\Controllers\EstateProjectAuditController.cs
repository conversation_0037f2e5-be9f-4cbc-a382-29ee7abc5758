﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using System.Web.Configuration;
using System.Web.Http;
using Hangfire;
using Hangfire.Console;
using Hangfire.Server;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Oracle.ManagedDataAccess.Client;
using SDCPCWeb.Extensions;
using SDCPCWeb.Jobs;
using SDCPCWeb.Models;
using SDCPCWeb.Models.Attachment;
using SDCPCWeb.Models.BusinessContent.GuiHuaHeShi;
using SDCPCWeb.Models.BusinessContent.YanXian;
using SDCPCWeb.Models.BusinessFlow;
using SDCPCWeb.Models.CompanyRegister;
using SDCPCWeb.Models.EstateConstruction;
using SDCPCWeb.Models.Result;
using SDCPCWeb.Models.System;
using SDCPCWeb.Services;

namespace SDCPCWeb.Controllers {
    /// <summary>
    /// 不动产业务相关审核回调接口
    /// </summary>
    [RoutePrefix("internal/EstateProjectAudit")]
    public class EstateProjectAuditController : ApiController {
        private OracleDataService oracle = new OracleDataService();

        /// <summary>
        /// 业务审核通过
        /// </summary>
        /// <param name="argument"></param>
        /// <returns></returns>
        [HttpPost, Route("CompleteAuditByAuthCode"), SDCAuthorize(SDCAuthorizeRole.AuthCode)]
        public ApiResult CompleteAuditByAuthCode(object argument) {
            return CompleteAudit(argument);
        }

        /// <summary>
        /// 业务审核通过
        /// 包括：规划验线、不动产预测绘成果备案、规划核实、不动产实测绘成果备案
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("CompleteAudit"),SDCAuthorize(SDCAuthorizeRole.xCloud)]
        public ApiResult CompleteAudit(object argument) {
            //使用hangfire执行
            Hangfire.BackgroundJob.Enqueue(() => HangfireCompleteAudit(argument, null));

            return new ApiResult() {
                StateCode = 1
            };
        }

        /// <summary>
        /// 办结业务
        /// </summary>
        /// <param name="argument"></param>
        /// <param name="console"></param>
        /// <returns></returns>
        [DisplayName("办结业务"), AutomaticRetry(Attempts = 0)]
        public ApiResult HangfireCompleteAudit(object argument, PerformContext console) {
            var arg = JObject.FromObject(argument);
            try {
                var surveyId = arg["SurveyId"]?.Value<string>();
                string remark = arg["remark"]?.Value<string>(); //备注

                if (string.IsNullOrWhiteSpace(surveyId)) {
                    throw new Exception("参数错误");
                }

                var business = oracle.GetById<BusinessBaseInfo>(surveyId);
                if (business?.BusinessClass == null) {
                    throw new Exception("无效的参数或者不支持的测绘业务");
                }

                Request.WriteSDCLog("业务审核", $"审核完成 >> 业务ID：{surveyId} >> 业务类型：{business.BusinessType} >> remark：{remark} ");

                switch (business.BusinessClass) {
                    case "MeasurePutLineFlow": {
                            //放线测量
                            return CompletePutlineAudit(surveyId);
                        }
                    case "MeasurePutLinePreCheckFlow": {
                            //放线测量与规划验线（非房开企业）
                            return CompletePutLinePreCheckAudit(surveyId);
                    }
                    case "CouncilMeasurePutLinePreCheckFlow": {
                        //放线测量与规划验线（市政工程）
                        return CompleteCouncilPutLinePreCheckAudit(surveyId);
                    }
                    case "RealEstatePreSurveyFlow": {   
                            //不动产预测绘
                            return CompletePreSurveyAudit(surveyId);
                        }
                    case "RealEstatePreSurveyBuildingTableChangeFlow": {
                            //不动产预测绘楼盘表变更流程
                            return CompleteRealEstatePreSurveyBuildingTableChangeAudit(surveyId);
                    }
                    case "RealEstatePreSurveyResultChangeFlow": {
                            //不动产预测绘成果变更流程
                            return CompletePreSurveyResultChangeAudit(surveyId);
                    }
                    case "RealEstatePreCheckSurveyFlow": {
                            //不动产预核测绘
                            return CompletePreCheckSurveyAudit(surveyId);
                        }
                    case "RealEstateActualSurveyFlow": {
                            //不动产实核测绘
                            return CompleteRealActualSurveyAudit(surveyId, remark);
                        }
                    case "RealEstateActualBuildingTableChangeFlow": {
                            //不动产实核业务楼盘表变更流程
                            return CompleteRealEstateActualBuildingTableChangeAudit(surveyId);
                        }
                    case "RealEstateActualResultChangeFlow": {
                            //不动产实核业务成果变更流程
                            return CompleteRealEstateActualResultChangeAudit(surveyId, remark);
                        }
                    case "RealEstatePreCheckSurveyAutoFlow": {
                        //不动产预核测绘即时办理
                        return CompletePreCheckSurveyAutoAudit(surveyId);
                    }
                    case "CouncilPlanCheckFlow": {
                        //市政工程建设竣工规划核实业务
                        return CompleteCouncilPlanCheckAudit(surveyId);
                    }
                    case "RealEstateOverallActualSurveyFlow": {
                        //不动产全面核实业务
                        return CompleteRealOverallActualSurveyAudit(surveyId, remark);
                    }
                    default: throw new Exception("不支持的业务类型");

                }
            } catch (Exception e) {
                Request.WriteSDCLog("业务审核异常", $"审核完成 >> 参数【{JsonConvert.SerializeObject(arg)}】。异常信息：{e.GetStackTraces()} ");
                throw;
            }
        }

        /// <summary>
        /// 业务审核不通过
        /// 包括：规划验线、不动产预测绘成果备案、规划核实、不动产实测绘成果备案
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("RejectAudit"), SDCAuthorize(SDCAuthorizeRole.xCloud)]
        public async Task<ApiResult> RejectAudit(object argument) {
            //使用hangfire执行
            Hangfire.BackgroundJob.Enqueue(() => HangfireRejectAudit(argument, null));

            return new ApiResult() {
                StateCode = 1
            };
        }

        /// <summary>
        /// 退回业务
        /// </summary>
        /// <param name="argument"></param>
        /// <param name="console"></param>
        /// <returns></returns>
        [DisplayName("退回业务"), AutomaticRetry(Attempts = 0)]
        public async Task<ApiResult> HangfireRejectAudit(object argument, PerformContext console) {
            var arg = JObject.FromObject(argument);
            try {
                var surveyId = arg["SurveyId"]?.Value<string>();
                var message = arg["Message"]?.Value<string>();

                if (string.IsNullOrWhiteSpace(surveyId)) {
                    throw new Exception("参数错误");
                }

                var business = oracle.GetById<BusinessBaseInfo>(surveyId);
                if (business?.BusinessClass == null) {
                    throw new Exception("无效的参数或者不支持的测绘业务");
                }

                #region 判断是否能退回
                if (business.StateCode == 0 || business.StateCode == 2 || business.StateCode == 3 ||
                    business.StateCode == 4) {
                    return new ApiResult() {
                        StateCode = 0,
                        Message = $"当前业务状态不支持退回，当前业务状态为：{business.StateCode}"
                    };
                }

                int lastActionId = -1;
                var flow = BusinessFlowConfig.AllFlows.FirstOrDefault(s => s.GetType().Name == business.BusinessClass);
                if (flow != null) {
                    lastActionId = flow.FlowActionInfo.Actions.OrderByDescending(s => s.Index).FirstOrDefault().ID;
                }

                var lastBusinessLinkInfo = oracle.GetList<BusinessLinkInfo>($"BusinessId='{business.ID}'")
                    .OrderByDescending(s => s.StartTime)
                    .FirstOrDefault();
                if (lastBusinessLinkInfo.ActionId != lastActionId) {
                    return new ApiResult() {
                        StateCode = 0,
                        Message = $"当前业务环节不支持退回，当前业务环节为：{lastActionId}"
                    };
                }
                #endregion

                Request.WriteSDCLog("业务审核", $"审核不通过 >> 业务ID：{surveyId} >> 业务类型：{business.BusinessType} >> 参数：{JsonConvert.SerializeObject(argument)} ");
                switch (business.BusinessClass) {
                    case "MeasurePutLineFlow": {
                            //放线测量
                            return RejectPutlineAudit(surveyId, message);
                        }
                    case "MeasurePutLinePreCheckFlow": {
                            //单独规划验线
                            return RejectPutlinePreCheckAudit(surveyId, message);
                        }
                    case "CouncilMeasurePutLinePreCheckFlow": {
                        //市政工程单独规划验线
                        return RejectCouncilPutlinePreCheckAudit(surveyId, message);
                    }
                    case "RealEstatePreSurveyFlow": {
                            //不动产预测绘
                            return RejectPreSurveyAudit(surveyId, message);
                        }
                    case "RealEstatePreSurveyBuildingTableChangeFlow": {
                            //不动产预测绘楼盘表变更流程
                            return RejectPreSurveyBuildingTableChangeAudit(surveyId, message);
                        }
                    case "RealEstatePreSurveyResultChangeFlow": {
                            //不动产预测绘成果变更流程
                            return RejectPreSurveyResultChangeAudit(surveyId, message);
                        }
                    case "RealEstatePreCheckSurveyFlow": {
                            //不动产预核测绘
                            return RejectPreCheckSurveyAudit(surveyId, message);
                        }
                    case "RealEstateActualSurveyFlow": {
                            //不动产实核测绘
                            return await RejectRealActualSurveyAudit(surveyId, message);
                        }
                    case "RealEstateActualBuildingTableChangeFlow": {
                            //不动产实核业务楼盘表变更流程
                            return RejectRealEstateActualBuildingTableChangeAudit(surveyId, message);
                        }
                    case "RealEstateActualResultChangeFlow": {
                            //不动产实核业务成果变更
                            return RejectRealEstateActualResultChangeAudit(surveyId, message);
                        }
                    case "RealEstatePreCheckSurveyAutoFlow": {
                        //不动产预核测绘即时办理
                        return RejectPreCheckSurveyAutoAudit(surveyId, message);
                    }
                    case "CouncilPlanCheckFlow": {
                        //市政工程建设竣工规划核实业务
                        return await RejectCouncilPlanCheckAudit(surveyId, message);
                    }
                    case "RealEstateOverallActualSurveyFlow": {
                        //不动产全面核实业务
                        return await RejectRealOverallActualSurveyAudit(surveyId, message);
                    }
                    default: throw new Exception("不支持的业务类型");

                }
            } catch (Exception e) {
                Request.WriteSDCLog("业务审核异常", $"审核不通过 >> 参数【{JsonConvert.SerializeObject(arg)}】。异常信息：{e.GetStackTraces()} ");
                throw;
            }
        }

        /// <summary>
        /// 放线报告完成审核，修改放线报告状态
        /// </summary>
        /// <param name="surveyId"></param>
        /// <returns></returns>
        private ApiResult CompletePutlineAudit(string surveyId) {
            if (string.IsNullOrWhiteSpace(surveyId)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }
            var putline_content = oracle.GetById<PutLine_ContentInfo>(surveyId);
            if (putline_content == null) {
                return new ApiResult() { StateCode = 0, Message = "无效的放线测量ID" };
            }
            putline_content.DataCheckState = -2;
            oracle.UpdateOne(putline_content);
            var plans = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(putline_content.ProjectPlanPermission);
            BackgroundJob.Enqueue(() => EstateProjectInfo.SaveEstateProjectPutlineSurveyStates(plans, surveyId, SurveyState.CompleteAudit, null));

            return new ApiResult() { StateCode = 1, Message = "操作完成" };
        }

        /// <summary>
        /// 单独规划验线审核完成
        /// </summary>
        /// <param name="surveyId"></param>
        /// <returns></returns>
        private ApiResult CompletePutLinePreCheckAudit(string surveyId) {
            if (string.IsNullOrWhiteSpace(surveyId)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }
            var putline_content = oracle.GetById<PutLine_ContentInfo>(surveyId);
            if (putline_content == null) {
                return new ApiResult() { StateCode = 0, Message = "无效的放线测量ID" };
            }

            putline_content.DataCheckState = -2;
            oracle.UpdateOne(putline_content);

            //业务往前走一步
            var flow = new FlowService<MeasurePutLinePreCheckFlow>(oracle);
            flow.FlowPostToNext(surveyId);

            var plans = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(putline_content.ProjectPlanPermission);
            BackgroundJob.Enqueue(() => EstateProjectInfo.SaveEstateProjectPutlineSurveyStates(plans, surveyId, SurveyState.CompleteAudit, null));

            BackgroundJob.Enqueue(() => SurveyResultJob.SavePreCheckSurveyAuditResult(surveyId, null));

            return new ApiResult() { StateCode = 1, Message = "操作完成" };
        }

        /// <summary>
        /// 市政工程单独规划验线审核完成
        /// </summary>
        /// <param name="surveyId"></param>
        /// <returns></returns>
        private ApiResult CompleteCouncilPutLinePreCheckAudit(string surveyId) {
            if (string.IsNullOrWhiteSpace(surveyId)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }
            var putline_content = oracle.GetById<PutLine_ContentInfo>(surveyId);
            if (putline_content == null) {
                return new ApiResult() { StateCode = 0, Message = "无效的放线测量ID" };
            }

            putline_content.DataCheckState = -2;
            oracle.UpdateOne(putline_content);

            //业务往前走一步
            var flow = new FlowService<CouncilMeasurePutLinePreCheckFlow>(oracle);
            flow.FlowPostToNext(surveyId);

            var plans = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(putline_content.ProjectPlanPermission);
            BackgroundJob.Enqueue(() => EstateProjectInfo.SaveEstateProjectPutlineSurveyStates(plans, surveyId, SurveyState.CompleteAudit, null));

            BackgroundJob.Enqueue(() => SurveyResultJob.SavePreCheckSurveyAuditResult(surveyId, null));

            return new ApiResult() { StateCode = 1, Message = "操作完成" };
        }

        /// <summary>
        /// 不动产预测绘成果通过备案，修改测绘成果状态
        /// </summary>
        /// <param name="surveyId"></param>
        /// <returns></returns>
        private ApiResult CompletePreSurveyAudit(string surveyId) {
            if (string.IsNullOrWhiteSpace(surveyId)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }
            var preSurvey_content = oracle.GetById<PreSurvey_ContentInfo>(surveyId);
            if (preSurvey_content == null) {
                return new ApiResult() { StateCode = 0, Message = "无效的不动产预测绘ID" };
            }

            preSurvey_content.DataCheckState = -2;
            oracle.UpdateOne(preSurvey_content);
            var plans = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(preSurvey_content.ProjectPlanPermission);
            BackgroundJob.Enqueue(() => EstateProjectInfo.SaveEstateProjectPreSurveyStates(plans, surveyId, SurveyState.CompleteAudit, null));

            return new ApiResult() { StateCode = 1, Message = "操作完成" };
        }

        /// <summary>
        /// 不动产预测绘楼盘表变更流程通过备案
        /// </summary>
        /// <param name="surveyId"></param>
        /// <returns></returns>
        private ApiResult CompleteRealEstatePreSurveyBuildingTableChangeAudit(string surveyId) {
            if (string.IsNullOrWhiteSpace(surveyId)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }
            var preSurvey_content = oracle.GetById<PreSurvey_ContentInfo>(surveyId);
            if (preSurvey_content == null) {
                return new ApiResult() { StateCode = 0, Message = "无效的不动产预测绘ID" };
            }

            preSurvey_content.DataCheckState = -2;
            oracle.UpdateOne(preSurvey_content);

            //业务往前走一步
            var flow = new FlowService<RealEstatePreSurveyBuildingTableChangeFlow>(oracle);
            flow.FlowPostToNext(surveyId);

            return new ApiResult() { StateCode = 1, Message = "操作完成" };
        }

        /// <summary>
        /// 不动产预测绘成果变更流程通过备案
        /// </summary>
        /// <param name="surveyId"></param>
        /// <returns></returns>
        private ApiResult CompletePreSurveyResultChangeAudit(string surveyId) {
            if (string.IsNullOrWhiteSpace(surveyId)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }
            var preSurvey_content = oracle.GetById<PreSurvey_ContentInfo>(surveyId);
            if (preSurvey_content == null) {
                return new ApiResult() { StateCode = 0, Message = "无效的不动产预测绘ID" };
            }

            preSurvey_content.DataCheckState = -2;
            oracle.UpdateOne(preSurvey_content);

            //业务往前走一步
            var flow = new FlowService<RealEstatePreSurveyResultChangeFlow>(oracle);
            flow.FlowPostToNext(surveyId);

            //更新项目表状态
            var plans = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(preSurvey_content.ProjectPlanPermission);
            BackgroundJob.Enqueue(() => EstateProjectInfo.SaveEstateProjectPreSurveyStates(plans, surveyId, SurveyState.CompleteAudit, null));

            BackgroundJob.Enqueue(() => SurveyResultJob.SavePreCheckSurveyAuditResult(surveyId, null));

            return new ApiResult() { StateCode = 1, Message = "操作完成" };
        }

        /// <summary>
        /// 不动产预核测绘成果通过备案，修改测绘成果状态
        /// </summary>
        /// <param name="surveyId"></param>
        /// <returns></returns>
        private ApiResult CompletePreCheckSurveyAudit(string surveyId) {
            if (string.IsNullOrWhiteSpace(surveyId)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }
            var preSurvey_content = oracle.GetById<PreSurvey_ContentInfo>(surveyId);
            if (preSurvey_content == null) {
                return new ApiResult() { StateCode = 0, Message = "无效的不动产预核测绘ID" };
            }

            preSurvey_content.DataCheckState = -2;
            oracle.UpdateOne(preSurvey_content);

            var putline_content = oracle.GetById<PutLine_ContentInfo>(surveyId);
            if (putline_content != null) {
                putline_content.DataCheckState = -2;
                oracle.UpdateOne(putline_content);
            }
            //业务往前走一步
            var flow = new FlowService<RealEstatePreCheckSurveyFlow>(oracle);
            flow.FlowPostToNext(surveyId);

            //更新项目表状态
            var plans = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(preSurvey_content.ProjectPlanPermission);
            BackgroundJob.Enqueue(() => EstateProjectInfo.SaveEstateProjectPreSurveyStates(plans, surveyId, SurveyState.CompleteAudit, null));
            if (putline_content != null) {
                var plan_putline = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(putline_content.ProjectPlanPermission);
                BackgroundJob.Enqueue(() => EstateProjectInfo.SaveEstateProjectPutlineSurveyStates(plan_putline, surveyId, SurveyState.CompleteAudit, null));
            }
            BackgroundJob.Enqueue(() => SurveyResultJob.SavePreCheckSurveyAuditResult(surveyId, null));


            return new ApiResult() { StateCode = 1, Message = "操作完成" };
        }

        /// <summary>
        /// 不动产预核测绘即时办理成果通过备案，修改测绘成果状态
        /// </summary>
        /// <param name="surveyId"></param>
        /// <returns></returns>
        private ApiResult CompletePreCheckSurveyAutoAudit(string surveyId) {
            if (string.IsNullOrWhiteSpace(surveyId)) {
                return new ApiResult() { StateCode = 0, Message = "surveyId参数错误" };
            }

            var preSurvey_content = oracle.GetById<PreSurvey_ContentInfo>(surveyId);
            if (preSurvey_content == null) {
                return new ApiResult() { StateCode = 0, Message = "无效的不动产预核测绘ID" };
            }

            bool needManualAudit = false;

            //判断是否人工审核
            List<ProjectPlanPermissionInfo> projectPlanPermissionInfos =
                JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(preSurvey_content.ProjectPlanPermission);

            needManualAudit = projectPlanPermissionInfos.Any(s => s.NeedManualAudit == true);

            //key:AUDITSTATUS
            //value:0（无需人工审核），1（情景1），2（情景2），1和2就要走人工审核
            if (needManualAudit == false && preSurvey_content?.BuildingTableInfo?.Contains("AUDITSTATUS") == true) {
                var buildingTableInfo = JsonConvert.DeserializeObject<JArray>(preSurvey_content.BuildingTableInfo);
                if (buildingTableInfo.Any(s => 
                    s["AUDITSTATUS"]?.ToObject<int?>() == 1 || s["AUDITSTATUS"]?.ToObject<int?>() == 2)) {
                    needManualAudit = true;
                }
            }

            //如果是人工审核
            if (needManualAudit == true) {
                //办结业务
                Hangfire.BackgroundJob.Enqueue(() => BackgroudCompletePreCheckSurveyAutoAudit(surveyId, true, null));

                return new ApiResult() { StateCode = 1, Message = "操作完成" };
            }

            //通知EPS创建业务，等EPS创建业务并且入库完成后再办结业务方法BackgroudCompletePreCheckSurveyAutoAudit
            BackgroundJob.Enqueue(() => EPSService.ToEPSAutoCreateBusiness(surveyId, nameof(RealEstatePreCheckSurveyAutoFlow), null));

            return new ApiResult() { StateCode = 1, Message = "操作完成" };
        }

        /// <summary>
        /// 不动产预核测绘即时办理成果通过备案，修改测绘成果状态
        /// </summary>
        /// <param name="surveyId"></param>
        /// <param name="needManualAudit">是否人工审核</param>
        /// <param name="console"></param>
        [DisplayName("后台自动办结业务")]
        public static async Task BackgroudCompletePreCheckSurveyAutoAudit(string surveyId, bool needManualAudit, PerformContext console) {

            OracleDataService oracle = new OracleDataService();
            var preSurvey_content = oracle.GetById<PreSurvey_ContentInfo>(surveyId);
            if (preSurvey_content == null) {
                throw new Exception("无效的不动产预核测绘ID");
            }

            //提前取出备案信息，避免前端没能及时显示附件
            await SurveyResultJob.SavePreCheckSurveyAuditResult(surveyId, console);

            preSurvey_content.DataCheckState = -2;
            oracle.UpdateOne(preSurvey_content);

            var putline_content = oracle.GetById<PutLine_ContentInfo>(surveyId);
            if (putline_content != null) {
                putline_content.DataCheckState = -2;
                oracle.UpdateOne(putline_content);
            }

            //业务往前走一步
            var flow = new FlowService<RealEstatePreCheckSurveyAutoFlow>(oracle);
            flow.FlowPostToNext(surveyId);

            //更新项目表状态
            List<Tuple<SurveyClass, List<ProjectPlanPermissionInfo>>> plansList =
                new List<Tuple<SurveyClass, List<ProjectPlanPermissionInfo>>>();

            var plans = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(preSurvey_content.ProjectPlanPermission);
            plansList.Add(new Tuple<SurveyClass, List<ProjectPlanPermissionInfo>>(SurveyClass.PreSurvey, plans));
            if (putline_content != null) {
                var plan_putline = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(putline_content.ProjectPlanPermission);
                plansList.Add(new Tuple<SurveyClass, List<ProjectPlanPermissionInfo>>(SurveyClass.Putline, plan_putline));
            }

            BackgroundJob.Enqueue(() => EstateProjectInfo.BatchSaveEstateProjectSurveyStates(surveyId, plansList, SurveyState.CompleteAudit, null));


            //如果不是人工审核，通知云平台办结业务
            if (needManualAudit == false)
                BackgroundJob.Enqueue(() => XCloudService.ToXCloudCompletedBusiness(surveyId, nameof(RealEstatePreCheckSurveyAutoFlow), null));
        }

        /// <summary>
        /// 不动产实核测绘通过备案，修改测绘成果状态
        /// </summary>
        /// <param name="surveyId"></param>
        /// <returns></returns>
        private ApiResult CompleteRealActualSurveyAudit(string surveyId, string remark = null) {
            if (string.IsNullOrWhiteSpace(surveyId)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }
            var realEstateActualSurvey = RealEstateActualSurveyProject.GetByBusinessID(surveyId);
            if (realEstateActualSurvey?.ContentInfo == null) {
                return new ApiResult() { StateCode = 0, Message = "无效的不动产实核测绘业务ID" };
            }

            realEstateActualSurvey.ContentInfo.DataCheckState = -2;
            oracle.UpdateOne(realEstateActualSurvey.ContentInfo);
            //业务往前走一步
            var flow = new FlowService<RealEstateActualSurveyFlow>(oracle);
            flow.FlowPostToNext(surveyId);
            var plans = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(realEstateActualSurvey.ContentInfo.ProjectPlanPermission);
            BackgroundJob.Enqueue(() => EstateProjectInfo.SaveEstateProjectRealSurveyStates(plans, surveyId, SurveyState.CompleteAudit, null));
            BackgroundJob.Enqueue(() => SurveyResultJob.SaveRealSurveyAuditResult(surveyId, null));

            //提取已通过的实核测绘备案编号
            //BackgroundJob.Enqueue(() => SurveyResultJob.SaveRealSurveyAuditResultNo(surveyId, null));

            //不管云平台有无修改备注，都需要重新获取竣工规划条件核实信息表
            BackgroundJob.Enqueue(() => SurveyResultJob.UpdateJGKHHSBRemark(remark, surveyId, null));

            return new ApiResult() { StateCode = 1, Message = "操作完成" };
        }


        /// <summary>
        /// 不动产实核测绘楼盘表变更通过备案
        /// </summary>
        /// <param name="surveyId"></param>
        /// <returns></returns>
        private ApiResult CompleteRealEstateActualBuildingTableChangeAudit(string surveyId) {
            if (string.IsNullOrWhiteSpace(surveyId)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }
            var realEstateActualSurvey = RealEstateActualBuildingTableChangeProject.GetByBusinessID(surveyId);
            if (realEstateActualSurvey?.ContentInfo == null) {
                return new ApiResult() { StateCode = 0, Message = "无效的不动产实核测绘业务ID" };
            }

            realEstateActualSurvey.ContentInfo.DataCheckState = -2;
            oracle.UpdateOne(realEstateActualSurvey.ContentInfo);
            //业务往前走一步
            var flow = new FlowService<RealEstateActualBuildingTableChangeFlow>(oracle);
            flow.FlowPostToNext(surveyId);

            return new ApiResult() { StateCode = 1, Message = "操作完成" };
        }

        /// <summary>
        /// 不动产实核测绘成果变更通过备案
        /// </summary>
        /// <param name="surveyId"></param>
        /// <returns></returns>
        private ApiResult CompleteRealEstateActualResultChangeAudit(string surveyId, string remark) {
            if (string.IsNullOrWhiteSpace(surveyId)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }
            var realEstateActualSurvey = RealEstateActualResultChangeProject.GetByBusinessID(surveyId);
            if (realEstateActualSurvey?.ContentInfo == null) {
                return new ApiResult() { StateCode = 0, Message = "无效的不动产实核测绘业务ID" };
            }

            realEstateActualSurvey.ContentInfo.DataCheckState = -2;
            oracle.UpdateOne(realEstateActualSurvey.ContentInfo);
            //业务往前走一步
            var flow = new FlowService<RealEstateActualResultChangeFlow>(oracle);
            flow.FlowPostToNext(surveyId);

            BackgroundJob.Enqueue(() => SurveyResultJob.SaveRealSurveyAuditResult(surveyId, null));

            var plans = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(realEstateActualSurvey.ContentInfo.ProjectPlanPermission);
            BackgroundJob.Enqueue(() => EstateProjectInfo.SaveEstateProjectRealSurveyStates(plans, surveyId, SurveyState.CompleteAudit, null));

            //提取已通过的实核测绘备案编号
            //BackgroundJob.Enqueue(() => SurveyResultJob.SaveRealSurveyAuditResultNo(surveyId, null));

            //不管云平台有无修改备注，都需要重新获取竣工规划条件核实信息表
            BackgroundJob.Enqueue(() => SurveyResultJob.UpdateJGKHHSBRemark(remark, surveyId, null));

            return new ApiResult() { StateCode = 1, Message = "操作完成" };
        }

        private ApiResult CompleteCouncilPlanCheckAudit(string surveyId) {
            if (string.IsNullOrWhiteSpace(surveyId)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }
            var councilPlanCheckProject = CouncilPlanCheckProject.GetByBusinessID(surveyId);
            if (councilPlanCheckProject?.ContentInfo == null) {
                return new ApiResult() { StateCode = 0, Message = "无效的市政工程建设竣工规划核实业务ID" };
            }

            councilPlanCheckProject.ContentInfo.DataCheckState = -2;
            oracle.UpdateOne(councilPlanCheckProject.ContentInfo);
            //业务往前走一步
            var flow = new FlowService<CouncilPlanCheckFlow>(oracle);
            flow.FlowPostToNext(surveyId);
            var plans = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(councilPlanCheckProject.ContentInfo.ProjectPlanPermission);
            BackgroundJob.Enqueue(() => EstateProjectInfo.SaveEstateProjectRealSurveyStates(plans, surveyId, SurveyState.CompleteAudit, null));
            BackgroundJob.Enqueue(() => SurveyResultJob.SaveCouncilPlanCheckAuditResult(surveyId, null));
            BackgroundJob.Enqueue(() => SurveyResultJob.UpdateJGKHHSBRemark(null, surveyId, null));

            return new ApiResult() { StateCode = 1, Message = "操作完成" };
        }

        /// <summary>
        /// 不动产全面核实测绘通过备案，修改测绘成果状态
        /// </summary>
        /// <param name="surveyId"></param>
        /// <returns></returns>
        private ApiResult CompleteRealOverallActualSurveyAudit(string surveyId, string remark = null) {
            if (string.IsNullOrWhiteSpace(surveyId)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }
            var realEstateOverallActualSurvey = RealEstateOverallActualSurveyProject.GetByBusinessID(surveyId);
            if (realEstateOverallActualSurvey?.ContentInfo == null) {
                return new ApiResult() { StateCode = 0, Message = "无效的不动产全面核实业务ID" };
            }

            realEstateOverallActualSurvey.ContentInfo.DataCheckState = -2;
            oracle.UpdateOne(realEstateOverallActualSurvey.ContentInfo);
            //业务往前走一步
            var flow = new FlowService<RealEstateOverallActualSurveyFlow>(oracle);
            flow.FlowPostToNext(surveyId);
            var plans = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(realEstateOverallActualSurvey.ContentInfo.ProjectPlanPermission);
            BackgroundJob.Enqueue(() => EstateProjectInfo.SaveEstateProjectRealSurveyStates(plans, surveyId, SurveyState.CompleteAudit, null));
            BackgroundJob.Enqueue(() => SurveyResultJob.SaveRealSurveyAuditResult(surveyId, null));

            //不管云平台有无修改备注，都需要重新获取竣工规划条件核实信息表
            BackgroundJob.Enqueue(() => SurveyResultJob.UpdateJGKHHSBRemark(remark, surveyId, null));

            return new ApiResult() { StateCode = 1, Message = "操作完成" };
        }

        /// <summary>
        /// 放线测量验线不通过，修改其状态
        /// </summary>
        /// <param name="surveyId"></param>
        /// <returns></returns>
        private ApiResult RejectPutlineAudit(string surveyId, string msg) {
            if (string.IsNullOrWhiteSpace(surveyId)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }
            var putline_content = oracle.GetById<PutLine_ContentInfo>(surveyId);
            if (putline_content == null) {
                return new ApiResult() { StateCode = 0, Message = "无效的放线测量ID" };
            }

            //todo 建议启动异步作业确保操作完成
            var planInfos = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(putline_content.ProjectPlanPermission);
            foreach (var planInfo in planInfos) {
                var code = planInfo.Code;
                var estateProject = EstateProjectInfo.GetEstateProjectInfo(surveyId, code, SurveyClass.Putline); //oracle.GetById<EstateProjectInfo>(code);
                estateProject.PutLineSurveyState = SurveyState.Invalid;
                oracle.UpdateOne(estateProject);
            }

            msg += "（如对退件意见有疑义，可咨询南宁市不动产登记中心测绘管理科，联系电话：5609650，地点：南宁市民中心三楼C36号窗）";

            putline_content.DataCheckState = -1;
            putline_content.RejectMessage = msg;
            oracle.UpdateOne(putline_content);

            return new ApiResult() { StateCode = 1, Message = "操作完成" };
        }

        /// <summary>
        /// 单独规划验线不通过，修改其状态
        /// </summary>
        /// <param name="surveyId"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        private ApiResult RejectPutlinePreCheckAudit(string surveyId, string msg) {
            if (string.IsNullOrWhiteSpace(surveyId)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }
            var putline_content = oracle.GetById<PutLine_ContentInfo>(surveyId);
            if (putline_content == null) {
                return new ApiResult() { StateCode = 0, Message = "无效的放线测量ID" };
            }

            if (string.IsNullOrWhiteSpace(msg)) {
                return new ApiResult() { StateCode = 0, Message = "请填写回退意见" };
            }

            var project = MeasurePutLinePreCheckProject.GetByBusinessID(surveyId);

            //todo 建议启动异步作业确保操作完成
            var planInfos = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(putline_content.ProjectPlanPermission);
            foreach (var planInfo in planInfos) {
                var code = planInfo.Code;
                var estateProject = EstateProjectInfo.GetEstateProjectInfo(surveyId, code, SurveyClass.Putline); //oracle.GetById<EstateProjectInfo>(code);
                if (estateProject != null) {
                    estateProject.PutLineSurveyState = SurveyState.Invalid;
                    oracle.UpdateOne(estateProject);
                }
                
            }

            msg += "（如对退件意见有疑义，可咨询南宁市不动产登记中心测绘管理科，联系电话：5609650，地点：南宁市民中心三楼C36号窗）";

            putline_content.DataCheckState = -1;
            putline_content.RejectMessage = msg;
            oracle.UpdateOne(putline_content);

            //业务退回上一步
            var flow = MeasurePutLinePreCheckProject.FlowService;
            flow.FlowPostToLast(project.BaseInfo.ID, msg);

            return new ApiResult() { StateCode = 1, Message = "操作完成" };
        }

        /// <summary>
        /// 市政工程单独规划验线不通过，修改其状态
        /// </summary>
        /// <param name="surveyId"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        private ApiResult RejectCouncilPutlinePreCheckAudit(string surveyId, string msg) {
            if (string.IsNullOrWhiteSpace(surveyId)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }
            var putline_content = oracle.GetById<PutLine_ContentInfo>(surveyId);
            if (putline_content == null) {
                return new ApiResult() { StateCode = 0, Message = "无效的放线测量ID" };
            }

            if (string.IsNullOrWhiteSpace(msg)) {
                return new ApiResult() { StateCode = 0, Message = "请填写回退意见" };
            }

            var project = CouncilMeasurePutLinePreCheckProject.GetByBusinessID(surveyId);

            //todo 建议启动异步作业确保操作完成
            var planInfos = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(putline_content.ProjectPlanPermission);
            foreach (var planInfo in planInfos) {
                var code = planInfo.Code;
                var estateProject = oracle.GetById<EstateProjectInfo>(code);
                estateProject.PutLineSurveyState = SurveyState.Invalid;
                oracle.UpdateOne(estateProject);
            }

            putline_content.DataCheckState = -1;
            putline_content.RejectMessage = msg;
            oracle.UpdateOne(putline_content);

            //业务退回上一步
            var flow = CouncilMeasurePutLinePreCheckProject.FlowService;
            flow.FlowPostToLast(project.BaseInfo.ID, msg);

            return new ApiResult() { StateCode = 1, Message = "操作完成" };
        }

        /// <summary>
        /// 不动产预测绘备案不通过，修改其状态
        /// </summary>
        /// <param name="surveyId"></param>
        /// <returns></returns>
        private ApiResult RejectPreSurveyAudit(string surveyId, string msg) {
            if (string.IsNullOrWhiteSpace(surveyId)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }
            var preSurvey_content = oracle.GetById<PreSurvey_ContentInfo>(surveyId);
            if (preSurvey_content == null) {
                return new ApiResult() { StateCode = 0, Message = "无效的不动产预测绘ID" };
            }

            //todo 建议启动异步作业确保操作完成
            var planInfos = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(preSurvey_content.ProjectPlanPermission);
            foreach (var planInfo in planInfos) {
                var code = planInfo.Code;
                var estateProject = EstateProjectInfo.GetEstateProjectInfo(surveyId, code, SurveyClass.PreSurvey); //oracle.GetById<EstateProjectInfo>(code);
                estateProject.PreSurveyState = SurveyState.Invalid;
                oracle.UpdateOne(estateProject);
            }
            preSurvey_content.DataCheckState = -1;
            preSurvey_content.RejectMessage = msg;
            oracle.UpdateOne(preSurvey_content);

            return new ApiResult() { StateCode = 1, Message = "操作完成" };
        }

        /// <summary>
        /// 不动产预测绘楼盘表变更流程备案不通过
        /// </summary>
        /// <param name="surveyId"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        private ApiResult RejectPreSurveyBuildingTableChangeAudit(string surveyId, string msg) {
            if (string.IsNullOrWhiteSpace(surveyId)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }
            var preSurvey_content = oracle.GetById<PreSurvey_ContentInfo>(surveyId);
            if (preSurvey_content == null) {
                return new ApiResult() { StateCode = 0, Message = "无效的不动产预测绘ID" };
            }

            if (string.IsNullOrWhiteSpace(msg)) {
                return new ApiResult() { StateCode = 0, Message = "请填写回退意见" };
            }

            msg += "（如对退件意见有疑义，可咨询南宁市不动产登记中心测绘管理科，联系电话：5609650，地点：南宁市民中心三楼C36号窗）";

            preSurvey_content.DataCheckState = -1;
            preSurvey_content.RejectMessage = msg;
            oracle.UpdateOne(preSurvey_content);

            //业务退回上一步
            var project = RealEstatePreSurveyBuildingTableChangeProject.GetByBusinessID(surveyId);
            var flow = RealEstatePreSurveyBuildingTableChangeProject.FlowService;
            flow.FlowPostToLast(project.BaseInfo.ID, msg);

            return new ApiResult() { StateCode = 1, Message = "操作完成" };
        }

        /// <summary>
        /// 不动产预测绘成果变更流程备案不通过 
        /// </summary>
        /// <param name="surveyId"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        private ApiResult RejectPreSurveyResultChangeAudit(string surveyId, string msg) {
            if (string.IsNullOrWhiteSpace(surveyId)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }
            var preSurvey_content = oracle.GetById<PreSurvey_ContentInfo>(surveyId);
            if (preSurvey_content == null) {
                return new ApiResult() { StateCode = 0, Message = "无效的不动产预测绘ID" };
            }

            if (string.IsNullOrWhiteSpace(msg)) {
                return new ApiResult() { StateCode = 0, Message = "请填写回退意见" };
            }

            //重置项目状态
            Hangfire.BackgroundJob.Enqueue(() =>
                EstateProjectInfo.ResetEstateProjectStates(surveyId, preSurvey_content.ProjectPlanPermission,
                    SurveyClass.PreSurvey, null));

            msg += "（如对退件意见有疑义，可咨询南宁市不动产登记中心测绘管理科，联系电话：5609650，地点：南宁市民中心三楼C36号窗）";

            preSurvey_content.DataCheckState = -1;
            preSurvey_content.RejectMessage = msg;
            oracle.UpdateOne(preSurvey_content);

            //业务退回上一步
            var project = RealEstatePreSurveyResultChangeProject.GetByBusinessID(surveyId);
            var flow = RealEstatePreSurveyResultChangeProject.FlowService;
            flow.FlowPostToLast(project.BaseInfo.ID, msg);

            return new ApiResult() { StateCode = 1, Message = "操作完成" };
        }

        /// <summary>
        /// 不动产预核测绘备案不通过，修改其状态
        /// </summary>
        /// <param name="surveyId"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        private ApiResult RejectPreCheckSurveyAudit(string surveyId, string msg) {
            //预核测绘如果备案不通过，会存在两种情况：
            //1. 只有预测绘成果备案未通过
            //2. 只有放线测量成果不通过
            //3. 放线测量成果和预测绘成果均不通过

            //不论哪种不通过，业务将退回上一步，再次提交备案时，由内网云平台来控制创建什么业务

            if (string.IsNullOrWhiteSpace(surveyId)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }
            if (string.IsNullOrWhiteSpace(msg)) {
                return new ApiResult() { StateCode = 0, Message = "请填写回退意见" };
            }
            var project = RealEstatePreCheckSurveyProject.GetByBusinessID(surveyId);
            if (project?.PreSurveyContentInfo == null || project?.BaseInfo?.BusinessClass != BusinessFlowConfig.RealEstatePreCheckSurveyFlow.GetType().Name) {
                return new ApiResult() { StateCode = 0, Message = "无效的不动产预核测绘ID" };
            }

            //退回业务时，不需要改变测绘成果的状态，只需要恢复签收前状态并告知驳回的情况和消息即可
            
            project.PreSurveyContentInfo.RejectMessage = msg;
            oracle.UpdateOne(project.PreSurveyContentInfo);
            if (project.PutLineContentInfo != null) {
                project.PutLineContentInfo.RejectMessage = msg;
                oracle.UpdateOne(project.PutLineContentInfo);
            }
            //业务退回上一步
            var flow = RealEstatePreCheckSurveyProject.FlowService;
            flow.FlowPostToLast(project.BaseInfo.ID, msg);

            return new ApiResult() { StateCode = 1, Message = "操作完成" };
        }

        private ApiResult RejectPreCheckSurveyAutoAudit(string surveyId, string msg) {
            //预核测绘如果备案不通过，会存在两种情况：
            //1. 只有预测绘成果备案未通过
            //2. 只有放线测量成果不通过
            //3. 放线测量成果和预测绘成果均不通过

            //不论哪种不通过，业务将退回上一步，再次提交备案时，由内网云平台来控制创建什么业务

            if (string.IsNullOrWhiteSpace(surveyId)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }
            if (string.IsNullOrWhiteSpace(msg)) {
                return new ApiResult() { StateCode = 0, Message = "请填写回退意见" };
            }
            var project = RealEstatePreCheckSurveyAutoProject.GetByBusinessID(surveyId);
            if (project?.PreSurveyContentInfo == null || project?.BaseInfo?.BusinessClass != nameof(RealEstatePreCheckSurveyAutoFlow)) {
                return new ApiResult() { StateCode = 0, Message = "无效的不动产预核测绘ID" };
            }

            //退回业务时，不需要改变测绘成果的状态，只需要恢复签收前状态并告知驳回的情况和消息即可

            msg += "（如对退件意见有疑义，可咨询南宁市不动产登记中心测绘管理科，联系电话：5609650，地点：南宁市民中心三楼C36号窗）";

            project.PreSurveyContentInfo.RejectMessage = msg;
            oracle.UpdateOne(project.PreSurveyContentInfo);
            if (project.PutLineContentInfo != null) {
                project.PutLineContentInfo.RejectMessage = msg;
                oracle.UpdateOne(project.PutLineContentInfo);
            }
            //业务退回上一步
            var flow = RealEstatePreCheckSurveyAutoProject.FlowService;
            flow.FlowPostToLast(project.BaseInfo.ID, msg);

            return new ApiResult() { StateCode = 1, Message = "操作完成" };
        }

        /// <summary>
        /// 不动产实核测绘备案不通过，修改其状态
        /// </summary>
        /// <param name="surveyId"></param>
        /// <returns></returns>
        private async Task<ApiResult> RejectRealActualSurveyAudit(string surveyId, string msg) {
            if (string.IsNullOrWhiteSpace(surveyId)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }
            if (string.IsNullOrWhiteSpace(msg)) {
                return new ApiResult() { StateCode = 0, Message = "请填写回退意见" };
            }
            var realEstateActualSurvey = RealEstateActualSurveyProject.GetByBusinessID(surveyId);
            if (realEstateActualSurvey?.ContentInfo == null) {
                return new ApiResult() { StateCode = 0, Message = "无效的不动产实核测绘业务ID" };
            }

            //重置项目状态
            Hangfire.BackgroundJob.Enqueue(() =>
                EstateProjectInfo.ResetEstateProjectStates(surveyId, realEstateActualSurvey.ContentInfo.ProjectPlanPermission,
                    SurveyClass.CheckConditionSurvey, null));
            Hangfire.BackgroundJob.Enqueue(() =>
                EstateProjectInfo.ResetEstateProjectStates(surveyId, realEstateActualSurvey.ContentInfo.ProjectPlanPermission,
                    SurveyClass.RealSurvey, null));

            //判断是否退回MDB
            var isUploadMDBOnly = await XCloudService.CheckIsReplaceMDB(surveyId, nameof(RealEstateActualSurveyFlow));
            if (isUploadMDBOnly != true) {
                msg += "（如对退件意见有疑义，可咨询南宁市不动产登记中心测绘管理科，联系电话：5609650，地点：南宁市民中心三楼C36号窗）";
            }

            realEstateActualSurvey.ContentInfo.RejectMessage = msg;
            oracle.UpdateOne(realEstateActualSurvey.ContentInfo);

            var flow = RealEstateActualSurveyProject.FlowService;
            flow.FlowPostToLast(realEstateActualSurvey.BaseInfo.ID, msg, isUploadMDBOnly);

            return new ApiResult() { StateCode = 1, Message = "操作完成" };
        }

        /// <summary>
        /// 不动产实核业务楼盘表变更流程备案不通过
        /// </summary>
        /// <param name="surveyId"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        private ApiResult RejectRealEstateActualBuildingTableChangeAudit(string surveyId, string msg) {
            if (string.IsNullOrWhiteSpace(surveyId)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }
            if (string.IsNullOrWhiteSpace(msg)) {
                return new ApiResult() { StateCode = 0, Message = "请填写回退意见" };
            }
            var realEstateActualSurvey = RealEstateActualBuildingTableChangeProject.GetByBusinessID(surveyId);
            if (realEstateActualSurvey?.ContentInfo == null) {
                return new ApiResult() { StateCode = 0, Message = "无效的不动产实核测绘业务ID" };
            }

            realEstateActualSurvey.ContentInfo.RejectMessage = msg;
            oracle.UpdateOne(realEstateActualSurvey.ContentInfo);
            var flow = RealEstateActualBuildingTableChangeProject.FlowService;
            flow.FlowPostToLast(realEstateActualSurvey.BaseInfo.ID, msg);

            return new ApiResult() { StateCode = 1, Message = "操作完成" };
        }

        /// <summary>
        /// 不动产实核业务成果变更流程不通过
        /// </summary>
        /// <param name="surveyId"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        private ApiResult RejectRealEstateActualResultChangeAudit(string surveyId, string msg) {
            if (string.IsNullOrWhiteSpace(surveyId)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }
            if (string.IsNullOrWhiteSpace(msg)) {
                return new ApiResult() { StateCode = 0, Message = "请填写回退意见" };
            }
            var realEstateActualSurvey = RealEstateActualResultChangeProject.GetByBusinessID(surveyId);
            if (realEstateActualSurvey?.ContentInfo == null) {
                return new ApiResult() { StateCode = 0, Message = "无效的不动产实核测绘业务ID" };
            }

            //重置项目状态
            Hangfire.BackgroundJob.Enqueue(() =>
                EstateProjectInfo.ResetEstateProjectStates(surveyId, realEstateActualSurvey.ContentInfo.ProjectPlanPermission,
                    SurveyClass.CheckConditionSurvey, null));
            Hangfire.BackgroundJob.Enqueue(() =>
                EstateProjectInfo.ResetEstateProjectStates(surveyId, realEstateActualSurvey.ContentInfo.ProjectPlanPermission,
                    SurveyClass.RealSurvey, null));

            msg += "（如对退件意见有疑义，可咨询南宁市不动产登记中心测绘管理科，联系电话：5609650，地点：南宁市民中心三楼C36号窗）";

            realEstateActualSurvey.ContentInfo.RejectMessage = msg;
            oracle.UpdateOne(realEstateActualSurvey.ContentInfo);
            var flow = RealEstateActualResultChangeProject.FlowService;
            flow.FlowPostToLast(realEstateActualSurvey.BaseInfo.ID, msg);

            return new ApiResult() { StateCode = 1, Message = "操作完成" };
        }

        /// <summary>
        /// 不动产实核测绘备案不通过，修改其状态
        /// </summary>
        /// <param name="surveyId"></param>
        /// <returns></returns>
        private async Task<ApiResult> RejectCouncilPlanCheckAudit(string surveyId, string msg) {
            if (string.IsNullOrWhiteSpace(surveyId)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }
            if (string.IsNullOrWhiteSpace(msg)) {
                return new ApiResult() { StateCode = 0, Message = "请填写回退意见" };
            }
            var councilPlanCheckProject = CouncilPlanCheckProject.GetByBusinessID(surveyId);
            if (councilPlanCheckProject?.ContentInfo == null) {
                return new ApiResult() { StateCode = 0, Message = "无效的市政工程建设竣工规划核实业务ID" };
            }

            //重置项目状态
            Hangfire.BackgroundJob.Enqueue(() =>
                EstateProjectInfo.ResetEstateProjectStates(surveyId, councilPlanCheckProject.ContentInfo.ProjectPlanPermission,
                    SurveyClass.CheckConditionSurvey, null));
            Hangfire.BackgroundJob.Enqueue(() =>
                EstateProjectInfo.ResetEstateProjectStates(surveyId, councilPlanCheckProject.ContentInfo.ProjectPlanPermission,
                    SurveyClass.RealSurvey, null));

            //判断是否退回MDB
            var isUploadMDBOnly = await XCloudService.CheckIsReplaceMDB(surveyId, nameof(CouncilPlanCheckFlow));
            if (isUploadMDBOnly != true) {
                msg += "（如对退件意见有疑义，可咨询南宁市不动产登记中心测绘管理科，联系电话：5609650，地点：南宁市民中心三楼C36号窗）";
            }

            councilPlanCheckProject.ContentInfo.RejectMessage = msg;
            oracle.UpdateOne(councilPlanCheckProject.ContentInfo);

            var flow = CouncilPlanCheckProject.FlowService;
            flow.FlowPostToLast(councilPlanCheckProject.BaseInfo.ID, msg, isUploadMDBOnly);

            return new ApiResult() { StateCode = 1, Message = "操作完成" };
        }

        /// <summary>
        /// 不动产全面核实业务备案不通过，修改其状态
        /// </summary>
        /// <param name="surveyId"></param>
        /// <returns></returns>
        private async Task<ApiResult> RejectRealOverallActualSurveyAudit(string surveyId, string msg) {
            if (string.IsNullOrWhiteSpace(surveyId)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }
            if (string.IsNullOrWhiteSpace(msg)) {
                return new ApiResult() { StateCode = 0, Message = "请填写回退意见" };
            }
            var realEstateOverallActualSurvey = RealEstateOverallActualSurveyProject.GetByBusinessID(surveyId);
            if (realEstateOverallActualSurvey?.ContentInfo == null) {
                return new ApiResult() { StateCode = 0, Message = "无效的不动产全面核实业务ID" };
            }

            //重置项目状态
            Hangfire.BackgroundJob.Enqueue(() =>
                EstateProjectInfo.ResetEstateProjectStates(surveyId, realEstateOverallActualSurvey.ContentInfo.ProjectPlanPermission,
                    SurveyClass.CheckConditionSurvey, null));
            Hangfire.BackgroundJob.Enqueue(() =>
                EstateProjectInfo.ResetEstateProjectStates(surveyId, realEstateOverallActualSurvey.ContentInfo.ProjectPlanPermission,
                    SurveyClass.RealSurvey, null));

            //判断是否退回MDB
            var isUploadMDBOnly = await XCloudService.CheckIsReplaceMDB(surveyId, nameof(RealEstateOverallActualSurveyFlow));
            if (isUploadMDBOnly != true) {
                msg += "（如对退件意见有疑义，可咨询南宁市不动产登记中心测绘管理科，联系电话：5609650，地点：南宁市民中心三楼C36号窗）";
            }

            realEstateOverallActualSurvey.ContentInfo.RejectMessage = msg;
            oracle.UpdateOne(realEstateOverallActualSurvey.ContentInfo);


            var flow = RealEstateOverallActualSurveyProject.FlowService;
            flow.FlowPostToLast(realEstateOverallActualSurvey.BaseInfo.ID, msg, isUploadMDBOnly);

            return new ApiResult() { StateCode = 1, Message = "操作完成" };
        }

        /// <summary>
        /// 根据业务ID获取业务信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("GetBusinessByID")]
        public ApiResult GetBusinessByID(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult { StateCode = 0, Message = "请输入正确的信息" };
            }
            var record = oracle.GetById<BusinessBaseInfo>(id);
            if (record == null) {
                return new ApiResult { StateCode = 0, Message = "无法找到业务信息" };
            }
            try {
                if (record.BusinessClass == "BaseSurveyDataDownloadFlow") {
                    BaseSurveyDataDownloadProject project = BaseSurveyDataDownloadProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project };
                }
                if (record.BusinessClass == "MeasurePutLineFlow") {
                    MeasurePutLineProject project = MeasurePutLineProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project };
                }
                if (record.BusinessClass == "RealEstatePreSurveyFlow") {
                    RealEstatePreSurveyProject project = RealEstatePreSurveyProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project };
                }
                if (record.BusinessClass == "BlueLineSurveyFlow") {
                    BlueLineSurveyProject project = BlueLineSurveyProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project };
                }
                if (record.BusinessClass == "MarkPointSurveyFlow") {
                    MarkPointSurveyProject project = MarkPointSurveyProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project };
                }
                if (record.BusinessClass == "RealEstateActualSurveyFlow") {
                    RealEstateActualSurveyProject project = RealEstateActualSurveyProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "" };
                }
                if (record.BusinessClass == "RealEstatePreCheckSurveyFlow") {
                    RealEstatePreCheckSurveyProject project = RealEstatePreCheckSurveyProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project };
                }
                if (record.BusinessClass == "RealEstatePreSurveyBuildingTableChangeFlow") {
                    RealEstatePreSurveyBuildingTableChangeProject project = RealEstatePreSurveyBuildingTableChangeProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "" };
                }
                if (record.BusinessClass == "RealEstatePreSurveyResultChangeFlow") {
                    RealEstatePreSurveyResultChangeProject project = RealEstatePreSurveyResultChangeProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "" };
                }
                if (record.BusinessClass == "RealEstateActualBuildingTableChangeFlow") {
                    RealEstateActualBuildingTableChangeProject project = RealEstateActualBuildingTableChangeProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "" };
                }
                if (record.BusinessClass == "RealEstateActualResultChangeFlow") {
                    RealEstateActualResultChangeProject project = RealEstateActualResultChangeProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "" };
                }
                if (record.BusinessClass == "MeasurePutLinePreCheckFlow") {
                    MeasurePutLinePreCheckProject project = MeasurePutLinePreCheckProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "" };
                }
                if (record.BusinessClass == "RealEstatePreCheckSurveyAutoFlow") {
                    RealEstatePreCheckSurveyAutoProject project = RealEstatePreCheckSurveyAutoProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project };
                }
                if (record.BusinessClass == "CouncilPlanCheckFlow") {
                    CouncilPlanCheckProject project = CouncilPlanCheckProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "" };
                }
                if (record.BusinessClass == "CouncilMeasurePutLinePreCheckFlow") {
                    CouncilMeasurePutLinePreCheckProject project = CouncilMeasurePutLinePreCheckProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "" };
                }
                if (record.BusinessClass == "RealEstateOverallActualSurveyFlow") {
                    RealEstateOverallActualSurveyProject project = RealEstateOverallActualSurveyProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "" };
                }
                return new ApiResult { StateCode = 0, Data = "", Message = "...待完善" };
            }
            catch (Exception e) {
                Request.WriteSDCLog("内网获取业务详情", $"业务ID：{id} >> 错误信息：{e.GetStackTraces()} ");
                return new ApiResult { StateCode = 0, Message = "获取失败，错误信息：" + e.Message + "" };
            }
        }

        /// <summary>
        /// 获取业务附件列表
        /// </summary>
        /// <param name="id">业务id</param>
        /// <param name="attachmentCategories">附件类别</param>
        /// <param name="stateCode">附件状态：0正常，1已删除。传该参数但是不传值则是获取所有附件</param>
        /// <returns></returns>
        [HttpGet, Route("GetBusinessAttachmentInfos")]
        public ApiResult GetBusinessAttachmentInfos(string id, string attachmentCategories = null, int? stateCode = 0) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "id参数无效"
                };
            }

            string whereSqlText = $"BUSINESSID=:id";

            var oracleParameters = new List<OracleParameter>();
            oracleParameters.Add(new OracleParameter(":id", OracleDbType.Varchar2) { Value = id });

            if (stateCode.HasValue) {
                if (stateCode != 0 && stateCode != 1) {
                    stateCode = 0;
                }
                whereSqlText += " and STATECODE=:stateCode";
                oracleParameters.Add(new OracleParameter(":stateCode", OracleDbType.Int32) { Value = stateCode.Value });
            }

            if (!string.IsNullOrWhiteSpace(attachmentCategories)) {
                whereSqlText += " and AttachmentCategories=:attachmentCategories";
                oracleParameters.Add(new OracleParameter(":attachmentCategories", OracleDbType.Varchar2) { Value = attachmentCategories });
            }

            var list = oracle.GetList<AttachmentInfo>(whereSqlText, oracleParameters.ToArray());
            return new ApiResult() {
                StateCode = 1,
                Data = list
            };
        }

        /// <summary>
        /// 附件下载
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("AttachmentDownloadRequest")]
        public HttpResponseMessage AttachmentDownloadRequest(string id, bool? canDownloadDeleted = null) {
            if (id == null) {
                return new HttpResponseMessage(HttpStatusCode.BadRequest);
            }
            //ID必须存在对应的记录
            var record = oracle.GetById<AttachmentInfo>(id);
            if (record == null) {
                return new HttpResponseMessage(HttpStatusCode.NotFound);
            }
            if (canDownloadDeleted != true) {
                //新增：当项目附件被逻辑删除时 按404处理
                if (record.StateCode == 1) {
                    return Request.CreateErrorResponse(HttpStatusCode.NotFound, "该数据不存在，无法提供下载");
                }
            }
            //基础数据业务完成超过两周后，不可再下载
            if (record.AttachmentType == "基础数据") {
                BusinessBaseInfo bInfo = oracle.GetById<BusinessBaseInfo>(record.BusinessID);
                if (bInfo.FinishTime != null) {
                    DateTime now = DateTime.Now;
                    int weekNum = Convert.ToInt32(now.Subtract((DateTime)bInfo.FinishTime).Days) / 7;
                    if (weekNum >= 2) {
                        return new HttpResponseMessage(HttpStatusCode.NotFound);
                    }
                }
            }
            try {
                string RootPath = WebConfigurationManager.AppSettings["RootPath"];
                using (var stream = new FileStream(RootPath + "\\" + record.AttachmentPath, FileMode.Open, FileAccess.Read, FileShare.Read)) {

                    byte[] bytes = new byte[stream.Length];
                    stream.Read(bytes, 0, bytes.Length);
                    MemoryStream memoryStream = new MemoryStream(bytes);
                    memoryStream.Position = 0;
                    HttpResponseMessage response = new HttpResponseMessage(HttpStatusCode.OK);
                    response.Content = new StreamContent(memoryStream);
                    response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/octet-stream");
                    //获取原始文件名称
                    var FileName = record.AttachmentName + record.AttachmentExt;
                    response.Content.Headers.ContentDisposition = new ContentDispositionHeaderValue("attachment") {
                        FileName = FileName
                    };
                    return response;
                }
            }
            catch (Exception e) {
                return Request.CreateErrorResponse(HttpStatusCode.InternalServerError, e.Message);
            }
        }

        /// <summary>
        /// 反馈不动产预测绘成果备案信息MDB
        /// </summary>
        /// <param name="id">多测合一业务ID</param>
        /// <returns></returns>
        [HttpPost, Route("RejectPreSurveyMDBUpload")]
        public ApiResult RejectPreSurveyMDBUpload(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult { StateCode = 0, Message = "上传失败，请输入正确的信息" };
            }
            //ID必须存在对应的记录
            var project = RealEstatePreSurveyProject.GetByBusinessID(id);
            if (project?.ContentInfo == null) {
                return new ApiResult { StateCode = 0, Message = "上传失败，找不到不动产预测绘多测合一业务" };
            }
            HttpFileCollection files = System.Web.HttpContext.Current.Request.Files;
            //判断是否有文件上传
            if (files.Count == 0) {
                return new ApiResult { StateCode = 0, Message = "请提交要上传的文件" };
            }
            try {
                string attachmentId = Guid.NewGuid().ToString("N");
                //附件存储根路径
                string RootPath = WebConfigurationManager.AppSettings["RootPath"];
                string Year = DateTime.Now.Year.ToString();
                string Month = DateTime.Now.Month.ToString();
                string Day = DateTime.Now.Day.ToString();
                //文件存储路径
                string SavePath = Year + "\\" + Month + "\\" + Day + "\\" + project.BaseInfo.ID;
                //路径不存在时，自动创建
                if (!Directory.Exists(RootPath + "\\" + SavePath)) {
                    Directory.CreateDirectory(RootPath + "\\" + SavePath);
                }
                SavePath = SavePath + "\\" + attachmentId;
                //保存文件
                files[0].SaveAs(RootPath + "\\" + SavePath);
                //修改文件后缀名，防止部分文件被直接执行 暂时禁用 
                // Path.ChangeExtension(YSSavePath, ".bak");
                //更新附件信息
                var attachment = new AttachmentInfo {
                    ID = attachmentId,
                    AttachmentName = "不动产预测绘成果审核反馈",
                    AttachmentExt = ".mdb",
                    UploadTime = DateTime.Now,
                    AttachmentPath = SavePath,
                    AttachmentLength = files[0].ContentLength,
                    AttachmentType = "项目成果附件",
                    AttachmentCategories = "不动产预测绘成果审核反馈",
                    BusinessID = project.ContentInfo.ID,
                    BusinessType = project.FlowInfo.FlowName
                };
                //先删除原来的附件，应逻辑删除
                var rejectAttachments = oracle.GetList<AttachmentInfo>("BusinessID=:id AND AttachmentName='不动产预测绘成果审核反馈'", new OracleParameter(":id", OracleDbType.Varchar2) { Value = id });
                foreach (var attachmentInfo in rejectAttachments) {
                    attachmentInfo.StateCode = 1;
                    oracle.UpdateOne(attachmentInfo);
                }
                oracle.InsertOne(attachment);
                dynamic ResultInfo = new {
                    attachment.ID,
                    attachment.AttachmentName,
                    attachment.AttachmentExt,
                    attachment.AttachmentLength,
                    attachment.AttachmentType,
                    attachment.AttachmentCategories
                };
                Request.WriteSDCLog("业务审核", $"上传反馈MDB_预核 >> 业务ID：{project.BaseInfo.ID} >> 业务类型：{project.BaseInfo.BusinessType} ");
                return new ApiResult { StateCode = 1, Message = "上传成功", Data = ResultInfo };
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 0, Message = "上传失败，错误信息：" + e.Message + "" };
            }
        }

        /// <summary>
        /// 反馈不动产实核测绘成果备案信息MDB
        /// </summary>
        /// <param name="id">多测合一业务ID</param>
        /// <returns></returns>
        [HttpPost, Route("RejectRealActualSurveyMDBUpload")]
        public ApiResult RejectRealActualSurveyMDBUpload(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult { StateCode = 0, Message = "上传失败，请输入正确的信息" };
            }
            //ID必须存在对应的记录
            var project = RealEstateActualSurveyProject.GetByBusinessID(id);
            if (project?.ContentInfo == null) {
                return new ApiResult { StateCode = 0, Message = "上传失败，找不到不动产实核测绘多测合一业务" };
            }
            HttpFileCollection files = System.Web.HttpContext.Current.Request.Files;
            //判断是否有文件上传
            if (files.Count == 0) {
                return new ApiResult { StateCode = 0, Message = "请提交要上传的文件" };
            }
            try {
                string attachmentId = Guid.NewGuid().ToString("N");
                //附件存储根路径
                string RootPath = WebConfigurationManager.AppSettings["RootPath"];
                string Year = DateTime.Now.Year.ToString();
                string Month = DateTime.Now.Month.ToString();
                string Day = DateTime.Now.Day.ToString();
                //文件存储路径
                string SavePath = Year + "\\" + Month + "\\" + Day + "\\" + project.BaseInfo.ID;
                //路径不存在时，自动创建
                if (!Directory.Exists(RootPath + "\\" + SavePath)) {
                    Directory.CreateDirectory(RootPath + "\\" + SavePath);
                }
                SavePath = SavePath + "\\" + attachmentId;
                //保存文件
                files[0].SaveAs(RootPath + "\\" + SavePath);
                //修改文件后缀名，防止部分文件被直接执行 暂时禁用 
                // Path.ChangeExtension(YSSavePath, ".bak");
                //更新附件信息
                var attachment = new AttachmentInfo {
                    ID = attachmentId,
                    AttachmentName = "不动产实核测绘成果审核反馈",
                    AttachmentExt = ".mdb",
                    UploadTime = DateTime.Now,
                    AttachmentPath = SavePath,
                    AttachmentLength = files[0].ContentLength,
                    AttachmentType = "项目成果附件",
                    AttachmentCategories = "不动产实核测绘成果审核反馈",
                    BusinessID = project.ContentInfo.ID,
                    BusinessType = project.FlowInfo.FlowName
                };
                //先删除原来的附件，应逻辑删除
                var rejectAttachments = oracle.GetList<AttachmentInfo>("BusinessID=:id AND AttachmentName='不动产实核测绘成果审核反馈'", new OracleParameter(":id", OracleDbType.Varchar2) { Value = id });
                foreach (var attachmentInfo in rejectAttachments) {
                    attachmentInfo.StateCode = 1;
                    oracle.UpdateOne(attachmentInfo);
                }
                oracle.InsertOne(attachment);
                dynamic ResultInfo = new {
                    attachment.ID,
                    attachment.AttachmentName,
                    attachment.AttachmentExt,
                    attachment.AttachmentLength,
                    attachment.AttachmentType,
                    attachment.AttachmentCategories
                };
                Request.WriteSDCLog("业务审核", $"上传反馈MDB_实核 >> 业务ID：{project.BaseInfo.ID} >> 业务类型：{project.BaseInfo.BusinessType} ");
                return new ApiResult { StateCode = 1, Message = "上传成功", Data = ResultInfo };
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 0, Message = "上传失败，错误信息：" + e.Message + "" };
            }
        }

        /// <summary>
        /// 获取测绘成果审核业务列表
        /// </summary>
        /// <param name="classType">业务类型，当前仅支持RealEstateActualSurveyFlow不动产实核业务和RealEstatePreCheckSurveyFlow不动产预核业务</param>
        /// <param name="includeNotSign">包含未签收，如果includeSigned为false，此参数强制true</param>
        /// <param name="includeSigned">包含已签收</param>
        /// <param name="isWuXiang">是否读取五象业务并且未做过“规划核实业务”的数据</param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpGet, Route("GetProjectAuditList"), SDCAuthorize(SDCAuthorizeRole.xCloud)]
        public ApiResult GetProjectAuditList(string classType, bool includeNotSign = true, bool includeSigned = false, bool? isWuXiang = null, int pageIndex = 1, int pageSize = 20) {
            if (string.IsNullOrWhiteSpace(classType)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }

            var config = AuditParameters.Config;

            if (!config.ContainsKey(classType)) {
                return new ApiResult() { StateCode = 0, Message = "不支持的类型" };
            }

            var audit = config[classType];
            string where =
                $"BUSINESSCLASS=:classType AND EXISTS(SELECT 1 FROM BUSINESSLINKINFO WHERE BUSINESSID=BUSINESSBASEINFO.ID AND ACTIONID={audit.AuditActionId} AND STATECODE IN(0,1))";

            var signStates = new List<int>();
            if (includeNotSign) {
                signStates.Add(audit.BeforeSignValue);
            }
            if (includeSigned) {
                signStates.Add(audit.SignedValue);
            }

            if (signStates.Any()) {
                where +=
                    $" AND EXISTS(SELECT 1 FROM {audit.ContentInfoName} WHERE ID=BUSINESSBASEINFO.ID AND DataCheckState IN({string.Join(",", signStates)}))";
            }
            else {
                where +=
                    $" AND EXISTS(SELECT 1 FROM {audit.ContentInfoName} WHERE ID=BUSINESSBASEINFO.ID AND DataCheckState={audit.BeforeSignValue})";
            }

            //属于五象“工程规划许可证号”并且未做过“规划核实业务”的数据只能让五象的人员签收
            if (isWuXiang == true) {
                where += " AND ISWUXIANG = '1' AND (IsCompletedGHHS IS NULL OR IsCompletedGHHS != '1') ";
            }
            else { //否则让登记中心的人签收
                where += " AND ID NOT IN (SELECT ID FROM BUSINESSBASEINFO WHERE (ISWUXIANG = '1' AND (IsCompletedGHHS IS NULL OR IsCompletedGHHS != '1'))) ";
            }
            

            var result = oracle.GetPagerList<BusinessBaseInfo>(pageIndex, pageSize, where, "BusinessNumber", new OracleParameter(":classType", OracleDbType.Varchar2) { Value = classType });
            var total = oracle.GetTotalCountWithSql($"SELECT 1 FROM BusinessBaseInfo WHERE {where}", new OracleParameter(":classType", OracleDbType.Varchar2) { Value = classType });

            var resultEx = result
                .Select(s => JsonConvert.DeserializeObject<BusinessBaseInfoEx>(JsonConvert.SerializeObject(s)))
                .ToList();
            if (result.Any()) {
                //查当前操作步骤ID
                string sqlText = $"SELECT ID, BUSINESSID FROM BUSINESSLINKINFO WHERE BUSINESSID IN ('{string.Join("','", result.Select(s => s.ID))}') AND ENDTIME IS NULL";
                var linkInfoDataTable = oracle.ExecuteQuerySql(sqlText);

                foreach (var businessBaseInfo in resultEx) {
                    businessBaseInfo.CurrentLinkInfoId = linkInfoDataTable.Select($"BUSINESSID='{businessBaseInfo.ID}'")
                        .FirstOrDefault()?["ID"]?.ToString();
                }
            }

            return new ApiResult() {
                StateCode = 1,
                Data = new PageResult<BusinessBaseInfoEx>() {
                    DataTable = resultEx,
                    Page = new Page() {
                        PageIndex = pageIndex,
                        PageSize = pageSize,
                        Total = total
                    }
                }
            };
        }

        /// <summary>
        /// 签收审核业务
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost, Route("SignProjectAudit"), SDCAuthorize(SDCAuthorizeRole.xCloud)]
        public ApiResult SignProjectAudit(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult { StateCode = 0, Message = "请输入正确的信息" };
            }
            var record = oracle.GetById<BusinessBaseInfo>(id);
            if (record == null) {
                return new ApiResult { StateCode = 0, Message = "无法找到业务信息" };
            }

            Request.WriteSDCLog("业务审核", $"签收业务 >> 业务ID：{record.ID} >> 业务类型：{record.BusinessType} ");

            switch (record.BusinessClass) {
                case nameof(RealEstateActualSurveyFlow):
                case nameof(RealEstateActualBuildingTableChangeFlow):
                case nameof(RealEstateActualResultChangeFlow):
                case nameof(RealEstateOverallActualSurveyFlow): {
                    var content = oracle.GetById<EstateActualSurveyContentInfo>(record.ID);
                    if (content != null) {
                        content.DataCheckState = 3;
                        oracle.UpdateOne(content);
                        return new ApiResult() { StateCode = 1, Message = "签收成功" };
                    }
                    break;
                }
                case nameof(RealEstatePreCheckSurveyFlow):
                case nameof(RealEstatePreCheckSurveyAutoFlow): {
                    var content = oracle.GetById<PreSurvey_ContentInfo>(record.ID);
                    if (content != null) {
                        content.DataCheckState = 3;
                        oracle.UpdateOne(content);
                        var putline_content = oracle.GetById<PutLine_ContentInfo>(record.ID);
                        if (putline_content != null) {
                            putline_content.DataCheckState = 3;
                            oracle.UpdateOne(putline_content);
                        }
                        return new ApiResult() { StateCode = 1, Message = "签收成功" };
                    }
                    break;
                }
                case nameof(RealEstatePreSurveyBuildingTableChangeFlow):
                case nameof(RealEstatePreSurveyResultChangeFlow): {
                    var content = oracle.GetById<PreSurvey_ContentInfo>(record.ID);
                    if (content != null) {
                        content.DataCheckState = 3;
                        oracle.UpdateOne(content);
                        return new ApiResult() { StateCode = 1, Message = "签收成功" };
                    }
                    break;
                }
                case nameof(MeasurePutLinePreCheckFlow):
                case nameof(CouncilMeasurePutLinePreCheckFlow): {
                    var putline_content = oracle.GetById<PutLine_ContentInfo>(record.ID);
                    if (putline_content != null) {
                        putline_content.DataCheckState = 3;
                        oracle.UpdateOne(putline_content);
                        return new ApiResult() { StateCode = 1, Message = "签收成功" };
                    }
                    break;
                }
                case nameof(CouncilPlanCheckFlow): {
                    var content = oracle.GetById<CouncilPlanCheckContentInfo>(record.ID);
                    if (content != null) {
                        content.DataCheckState = 3;
                        oracle.UpdateOne(content);
                        return new ApiResult() { StateCode = 1, Message = "签收成功" };
                    }
                    break;
                }
            }

            return new ApiResult() { StateCode = 0, Message = "操作失败" };
        }

        /// <summary>
        /// 反签收审核业务
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost, Route("UnSignProjectAudit"), SDCAuthorize(SDCAuthorizeRole.xCloud)]
        public async Task<ApiResult> UnSignProjectAudit(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult { StateCode = 0, Message = "请输入正确的信息" };
            }
            var record = oracle.GetById<BusinessBaseInfo>(id);
            if (record == null) {
                return new ApiResult { StateCode = 0, Message = "无法找到业务信息" };
            }

            Request.WriteSDCLog("业务审核", $"反签收业务 >> 业务ID：{record.ID} >> 业务类型：{record.BusinessType} ");

            switch (record.BusinessClass) {
                case nameof(RealEstateActualSurveyFlow): {
                        var content = oracle.GetById<EstateActualSurveyContentInfo>(record.ID);
                        if (content != null) {
                            
                            //获取五象工规证号
                            List<string> wuxiangCodes = new List<string>();

                            if (!string.IsNullOrWhiteSpace(content.ProjectPlanPermission)) {
                                var plans =
                                    JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(
                                        content.ProjectPlanPermission);
                                foreach (var projectPlanPermissionInfo in plans) {
                                    if (!string.IsNullOrWhiteSpace(projectPlanPermissionInfo.CaseCode)) {
                                        if (projectPlanPermissionInfo.CaseCode.StartsWith("五象")) {
                                            wuxiangCodes.Add(projectPlanPermissionInfo.Code);
                                        }
                                    } else {
                                        if (projectPlanPermissionInfo.Code.Length > 11 && projectPlanPermissionInfo.Code.Substring(10, 1) == "5") {
                                            wuxiangCodes.Add(projectPlanPermissionInfo.Code);
                                        }
                                    }
                                }
                            }


                            //判断是否已办理规划核实业务
                            if (wuxiangCodes.Any()) {
                                var checkResult = await XCloudService.CheckIsCompletedGHHS(wuxiangCodes.ToArray());
                                if (!string.IsNullOrWhiteSpace(checkResult.Item1)) {
                                    return new ApiResult() { StateCode = 0, Message = checkResult.Item1 };
                                }
                                else {
                                    record.IsCompletedGHHS = checkResult.Item2 == true ? "1" : null;
                                }
                            }
                            oracle.UpdateOne(record);


                            content.DataCheckState = 1;
                            oracle.UpdateOne(content);

                            //获取规划条件核实证明文件
                            Hangfire.BackgroundJob.Enqueue(() => SurveyResultJob.GetGHHSZMFile(id, null));

                            return new ApiResult() { StateCode = 1, Message = "反签收成功" };
                        }
                        break;
                    }
                default: return new ApiResult() { StateCode = 0, Message = "不支持的业务类型" };
            }

            return new ApiResult() { StateCode = 0, Message = "操作失败" };
        }

        /// <summary>
        /// 根据业务id查询校验失败的楼栋的信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost, Route("GetZRZZTByBusinessId"), SDCAuthorize(SDCAuthorizeRole.xCloud)]
        public async Task<ApiResult> GetZRZZTByBusinessId(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult { StateCode = 0, Message = "id参数无效" };
            }
            var record = oracle.GetById<BusinessBaseInfo>(id);
            if (record == null) {
                return new ApiResult { StateCode = 0, Message = "无法找到业务信息" };
            }

            if (record.BusinessClass != nameof(RealEstatePreSurveyBuildingTableChangeFlow)
                && record.BusinessClass != nameof(RealEstatePreSurveyResultChangeFlow)
                && record.BusinessClass != nameof(RealEstateActualBuildingTableChangeFlow)
                && record.BusinessClass != nameof(RealEstateActualResultChangeFlow)) {
                return new ApiResult { StateCode = 0, Message = "不支持的业务类型" };
            }

            if (record.StateCode == 4) {
                return new ApiResult { StateCode = 0, Message = "该业务已关闭" };
            }

            var zrzArr = JArray.Parse(record.ExtendInfo);
            var zrzInfo = zrzArr.FirstOrDefault();
            string zrzguid = zrzInfo["ZRZGUID"].ToString();
            var result = await BusinessFlowManageController.GetZRZZT(zrzguid);
            if (!string.IsNullOrWhiteSpace(result.Item1)) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = result.Item1
                };
            } else {
                List<JObject> fails = new List<JObject>();
                foreach (var item in result.Item2) {
                    if (item["SUCESS"].ToObject<bool>() == false) {
                        fails.Add(JObject.FromObject(new {
                            @zrzguid = item["ZRZGUID"].ToString(),
                            @zl = zrzArr.FirstOrDefault(s => s["ZRZGUID"].ToString() == item["ZRZGUID"].ToString())["ZL"].ToString(),
                            @msg = BusinessFlowManageController.GetZRZZTDescriptionForXCloud(item["CODE"].ToObject<int>())
                        }));
                    }
                }
                return new ApiResult() {
                    StateCode = 1,
                    Data = fails
                };
            }
        }

        /// <summary>
        /// 提取已通过的备案凭证PDF到业务附件和更新项目表状态（临时用于修复数据）
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost, Route("GetSurveyAuditResult"), AllowAnonymous]
        public async Task<ApiResult> GetSurveyAuditResult(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult { StateCode = 0, Message = "id参数无效" };
            }
            var record = oracle.GetById<BusinessBaseInfo>(id);
            if (record == null) {
                return new ApiResult { StateCode = 0, Message = "无法找到业务信息" };
            }

            if (record.StateCode != 2) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "业务未办结"
                };
            }

            Request.WriteSDCLog("业务审核", $"修复数据 >> 业务ID：{record.ID} >> 业务类型：{record.BusinessType} ");
            if (record.BusinessClass == nameof(RealEstatePreCheckSurveyAutoFlow)) {
                BackgroundJob.Enqueue(() => SurveyResultJob.SavePreCheckSurveyAuditResult(id, null));
            }
            else if (record.BusinessClass == nameof(RealEstatePreCheckSurveyFlow)) {
                BackgroundJob.Enqueue(() => SurveyResultJob.SavePreCheckSurveyAuditResult(id, null));
            }
            else if (record.BusinessClass == nameof(RealEstatePreSurveyResultChangeFlow)) {
                BackgroundJob.Enqueue(() => SurveyResultJob.SavePreCheckSurveyAuditResult(id, null));

                //更新项目表状态
                var preSurvey_content = oracle.GetById<PreSurvey_ContentInfo>(id);
                var plans = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(preSurvey_content.ProjectPlanPermission);
                BackgroundJob.Enqueue(() => EstateProjectInfo.SaveEstateProjectPreSurveyStates(plans, id, SurveyState.CompleteAudit, null));
            }
            else if (record.BusinessClass == nameof(RealEstateActualResultChangeFlow)) {
                BackgroundJob.Enqueue(() => SurveyResultJob.SaveRealSurveyAuditResult(id, null));

                //更新项目表状态
                var realEstateActualSurvey = RealEstateActualResultChangeProject.GetByBusinessID(id);
                var plans = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(realEstateActualSurvey.ContentInfo.ProjectPlanPermission);
                BackgroundJob.Enqueue(() => EstateProjectInfo.SaveEstateProjectRealSurveyStates(plans, id, SurveyState.CompleteAudit, null));
            }
            else if (record.BusinessClass == nameof(RealEstateActualSurveyFlow)) {
                BackgroundJob.Enqueue(() => SurveyResultJob.SaveRealSurveyAuditResult(id, null));

                //更新项目表状态
                var realEstateActualSurvey = RealEstateActualSurveyProject.GetByBusinessID(id);
                var plans = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(realEstateActualSurvey.ContentInfo.ProjectPlanPermission);
                BackgroundJob.Enqueue(() => EstateProjectInfo.SaveEstateProjectRealSurveyStates(plans, id, SurveyState.CompleteAudit, null));
            } else if (record.BusinessClass == nameof(RealEstateOverallActualSurveyFlow)) {
                BackgroundJob.Enqueue(() => SurveyResultJob.SaveRealSurveyAuditResult(id, null));

                //更新项目表状态
                var realEstateOverallActualSurvey = RealEstateOverallActualSurveyProject.GetByBusinessID(id);
                var plans = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(realEstateOverallActualSurvey.ContentInfo.ProjectPlanPermission);
                BackgroundJob.Enqueue(() => EstateProjectInfo.SaveEstateProjectRealSurveyStates(plans, id, SurveyState.CompleteAudit, null));
            } else {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "不支持的业务类型"
                };
            }

            return new ApiResult() {
                StateCode = 1
            };
        }

        /// <summary>
        /// 更新即核即办楼栋列表
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost, Route("AddJHJB"), SDCAuthorize(SDCAuthorizeRole.AuthCode)]
        public async Task<ApiResult> AddJHJB(dynamic data) {
            string sdcno = data?.sdcno?.ToString();
            string projectName = data?.projectName?.ToString();
            string developerName = data?.developerName?.ToString();
            string developerNo = data?.developerNo?.ToString();

            if (string.IsNullOrWhiteSpace(sdcno)) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "sdcno参数无效"
                };
            }

            if (string.IsNullOrWhiteSpace(projectName)) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "projectName参数无效"
                };
            }

            if (string.IsNullOrWhiteSpace(developerName)) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "developerName参数无效"
                };
            }

            if (string.IsNullOrWhiteSpace(developerNo)) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "developerNo参数无效"
                };
            }

            Request.WriteSDCLog("即核即办", $"添加楼栋数据 >> 参数【{JsonConvert.SerializeObject(data)} ");

            string whereSql = $"SDCNO=:sdcno";
            var list = oracle.GetList<ActualSurveyResultInfo>(whereSql,
                new OracleParameter(":sdcno", OracleDbType.Varchar2) {Value = sdcno});
            if (list.Any()) {
                var model = list.FirstOrDefault();

                if (model.IsInvalid == "1") {
                    return new ApiResult() {
                        StateCode = 0,
                        Message = $"实核备案编号{sdcno}已无效，不能修改信息"
                    };
                }

                if (model.IsSDCompleted == "1") {
                    return new ApiResult() {
                        StateCode = 0,
                        Message = $"实核备案编号{sdcno}的首次登记业务已办理，不能修改信息"
                    };
                }

                //更新信息
                model.ProjectName = projectName;
                model.DeveloperName = developerName;
                model.DeveloperNo = developerNo;

                oracle.UpdateOne(model);
            }
            else {
                ActualSurveyResultInfo model = new ActualSurveyResultInfo() {
                    ID = Guid.NewGuid().ToString("N"),
                    CreateTime = DateTime.Now,
                    SDCNo = sdcno.Trim(),
                    ProjectName = projectName.Trim(),
                    DeveloperName = developerName.Trim(),
                    DeveloperNo = developerNo.Trim(),
                    UpdateTime = DateTime.Now,
                    IsOpen = 0,
                    IsSDCompleted = "0",
                    IsInvalid = "0",
                };

                oracle.InsertOne(model);
            }

            return new ApiResult() {
                StateCode = 1
            };
        }

        /// <summary>
        /// 设置即核即办楼栋无效
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost, Route("InvalidJHJB"), SDCAuthorize(SDCAuthorizeRole.AuthCode)]
        public async Task<ApiResult> InvalidJHJB(dynamic data) {
            string sdcno = data?.sdcno?.ToString();

            if (string.IsNullOrWhiteSpace(sdcno)) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "sdcno参数无效"
                };
            }

            Request.WriteSDCLog("即核即办", $"作废楼栋数据 >> 参数【{JsonConvert.SerializeObject(data)} ");

            var updateSql = $"UPDATE ACTUALSURVEYRESULTINFO SET IsInvalid='1' WHERE SDCNO=:sdcno";
            oracle.ExecuteUpdateSql(updateSql, new OracleParameter(":sdcno", OracleDbType.Varchar2) {Value = sdcno});

            return new ApiResult() {
                StateCode = 1
            };
        }

        /// <summary>
        /// 更新工规证号与实核业务一对多功能后处理旧数据
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("UpdateEstateProjectInfo"), SDCAuthorize(SDCAuthorizeRole.AuthCode)]
        public ApiResult UpdateEstateProjectInfo() {
            Request.WriteSDCLog("更新数据", $"更新工规证号与实核业务绑定数据");

            Hangfire.BackgroundJob.Enqueue(() => UpdateDataService.UpdateEstateProjectInfo(null));

            return new ApiResult() {
                StateCode = 1
            };
        }

        /// <summary>
        /// 批量重新获取竣工规划条件核实信息表PDF附件
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost, Route("BatchGetJGKHHSB"), SDCAuthorize(SDCAuthorizeRole.AuthCode)]
        public ApiResult BatchGetJGKHHSB(dynamic data) {
            JArray list = JsonConvert.DeserializeObject<JArray>(JsonConvert.SerializeObject(data));

            Hangfire.BackgroundJob.Enqueue(() => SurveyResultJob.BatchGetJGKHHSB(list, null));

            return new ApiResult() {
                StateCode = 1,
                Message = JsonConvert.SerializeObject(data)
            };
        }

        /// <summary>
        /// 根据业务id数组重新获取竣工规划条件核实信息表PDF附件（用于修复数据）
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost, Route("BatchGetJGKHHSBByBusinessIds"), SDCAuthorize(SDCAuthorizeRole.AuthCode)]
        public ApiResult BatchGetJGKHHSBByBusinessIds(dynamic data) {
            List<string> businessIds = JsonConvert.DeserializeObject<List<string>>(JsonConvert.SerializeObject(data.businessIds));
            string sContainCompleted = data?.containCompleted;
            bool containCompleted = false;
            Boolean.TryParse(sContainCompleted, out containCompleted);

            Hangfire.BackgroundJob.Enqueue(() => SurveyResultJob.BatchGetJGKHHSB(businessIds, containCompleted, null));

            return new ApiResult() {
                StateCode = 1,
                Message = JsonConvert.SerializeObject(data)
            };
        }

        /// <summary>
        /// 修复预核即时办结业务的工规证号数据
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("FixProjectCodeInfo"), SDCAuthorize(SDCAuthorizeRole.AuthCode)]
        public async Task<ApiResult> FixProjectCodeInfo() {
            Hangfire.BackgroundJob.Enqueue(() => FixProjectCodeInfo(null));
            return new ApiResult() {
                StateCode = 1
            };
        }

        /// <summary>
        /// 修复预核即时办结业务的工规证号数据
        /// </summary>
        /// <param name="console"></param>
        /// <returns></returns>
        [DisplayName("修复预核即时办结业务的工规证号数据")]
        public static async Task FixProjectCodeInfo(PerformContext console) {
            console?.WriteLine("开始修复lb、tx字段没有值、未关闭业务的数据");
            BusinessFlowManageController businessFlowManageController =
                new BusinessFlowManageController();
            int updateCount = 0;
            using (OracleDataService oracle = new OracleDataService()) {
                string sql =
                    $"SELECT B.ID, B.PROJECTPLANPERMISSION FROM BUSINESSBASEINFO A INNER JOIN PRESURVEY_CONTENTINFO B on A.ID = B.ID where A.BUSINESSCLASS = 'RealEstatePreCheckSurveyAutoFlow' and A.STATECODE != 4 order by A.CREATETIME";
                var dt = oracle.ExecuteQuerySql(sql);
                console?.WriteLine($"总查询到：{dt.Rows.Count}条数据");
                foreach (DataRow dataRow in dt.Rows) {
                    var id = dataRow["ID"].ToString();
                    var PROJECTPLANPERMISSION = dataRow["PROJECTPLANPERMISSION"] != DBNull.Value
                        ? dataRow["PROJECTPLANPERMISSION"].ToString()
                        : null;
                    if (string.IsNullOrWhiteSpace(PROJECTPLANPERMISSION) || PROJECTPLANPERMISSION == "[]") {
                        console?.WriteLine($"ID：{id}，工规证号信息为空，跳过");
                    }
                    else {
                        var list = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(PROJECTPLANPERMISSION);
                        console?.WriteLine($"ID：{id}，工规证号数量：{list.Count}个");

                        bool hasUpdate = false; //是否有更新工规证信息

                        foreach (var projectPlanPermissionInfo in list) {
                            if (projectPlanPermissionInfo.IsAddNew == true) {
                                console?.WriteLine($"工规证号【{projectPlanPermissionInfo.Code}】是手工添加的，跳过处理");
                            } else if (projectPlanPermissionInfo.Buildings.Any() || projectPlanPermissionInfo.SetType.Any()) {
                                console?.WriteLine($"工规证号【{projectPlanPermissionInfo.Code}】这两个字段已有值，跳过处理");
                            }
                            else {
                                console?.WriteLine($"从接口获取工规证号【{projectPlanPermissionInfo.Code}】数据");
                                
                                var result = businessFlowManageController.GetProjectPlanPermissionInfo("",
                                    "RealEstatePreCheckSurveyAutoFlow", projectPlanPermissionInfo.Code);
                                if (result.StateCode == 1) {
                                    console?.WriteLine(JsonConvert.SerializeObject(result));
                                    var data = JsonConvert.DeserializeObject<ProjectPlanPermissionInfo>(JsonConvert.SerializeObject(result.Data));

                                    if (data.Buildings.Any()) {
                                        projectPlanPermissionInfo.Buildings = data.Buildings;
                                        hasUpdate = true;
                                    }

                                    if (data.SetType.Any()) {
                                        projectPlanPermissionInfo.SetType = data.SetType;
                                        hasUpdate = true;
                                    }
                                }
                                else {
                                    console?.WriteLine(result.Message);
                                }
                            }
                        }

                        if (hasUpdate) {
                            console?.WriteLine($"更新业务【{id}】的工规证数据");
                            string updateSql = $"update PRESURVEY_CONTENTINFO set PROJECTPLANPERMISSION=:data where ID='{id}'";
                            oracle.ExecuteUpdateSql(updateSql, new OracleParameter(":data", OracleDbType.Clob){ Value = JsonConvert.SerializeObject(list)});
                            console?.WriteLine("更新成功");

                            updateCount++;
                        }
                    }
                }

                console?.WriteLine($"总共更新成功：{updateCount}条");
            }
        }

        /// <summary>
        /// 根据统一信用代码获取企业信息
        /// </summary>
        /// <param name="creditCode"></param>
        /// <returns></returns>
        [HttpPost, Route("GetCompanyInfoByCreditCode"), SDCAuthorize(SDCAuthorizeRole.xCloud)]
        public ApiResult GetCompanyInfoByCreditCode(string creditCode) {
            var list = oracle.GetList<CompanyBaseInfo>("CreditCode=:creditCode",
                new OracleParameter(":creditCode", OracleDbType.Varchar2){ Value = creditCode});
            if (list.Any()) {
                string companyId = list.FirstOrDefault().ID;
                return new CompanyRegisterController().GetCompanyDetailsInfo(companyId);
            }
            else {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "系统中找不到该单位信息"
                };
            }
        }

        /// <summary>
        /// 重新获取竣工规划条件核实信息表(用于修复数据)
        /// </summary>
        /// <param name="remark">备注</param>
        /// <param name="surveyId">业务号</param>
        /// <returns></returns>
        [HttpPost, Route("UpdateJGKHHSBRemark"), SDCAuthorize(SDCAuthorizeRole.AuthCode)]
        public async Task<ApiResult> UpdateJGKHHSBRemark(string remark, string surveyId) {
            var business = oracle.GetById<BusinessBaseInfo>(surveyId);
            if (business?.StateCode != 2) {
                return new ApiResult() { StateCode = 0, Message = "该业务未办结" };
            }

            BackgroundJob.Enqueue(() => SurveyResultJob.UpdateJGKHHSBRemark(remark, surveyId, null));
            return new ApiResult() {
                StateCode = 1
            };
        }

        /// <summary>
        /// 判断业务是否需要人工审核（预核即时办结业务专用）
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("CheckBusinessNeedManualAudit")]
        public ApiResult CheckBusinessNeedManualAudit(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult() { StateCode = 0, Message = "id参数错误" };
            }

            var business = oracle.GetById<BusinessBaseInfo>(id);
            if (business?.BusinessClass != nameof(RealEstatePreCheckSurveyAutoFlow)) {
                return new ApiResult() { StateCode = 0, Message = "不支持的业务类型" };
            }

            var preSurvey_content = oracle.GetById<PreSurvey_ContentInfo>(id);
            if (preSurvey_content == null) {
                return new ApiResult() { StateCode = 0, Message = "无效的不动产预核测绘ID" };
            }

            bool needManualAudit = false;

            //判断是否人工审核
            List<ProjectPlanPermissionInfo> projectPlanPermissionInfos =
                JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(preSurvey_content.ProjectPlanPermission);

            //是否是人工审核
            needManualAudit = projectPlanPermissionInfos.Any(s => s.NeedManualAudit == true);

            //key:AUDITSTATUS
            //value:0（无需人工审核），1（情景1），2（情景2），1和2就要走人工审核
            if (needManualAudit == false && preSurvey_content?.BuildingTableInfo?.Contains("AUDITSTATUS") == true) {
                var buildingTableInfo = JsonConvert.DeserializeObject<JArray>(preSurvey_content.BuildingTableInfo);
                if (buildingTableInfo.Any(s => 
                    s["AUDITSTATUS"]?.ToObject<int?>() == 1 || s["AUDITSTATUS"]?.ToObject<int?>() == 2)) {
                    needManualAudit = true;
                }
            }

            return new ApiResult() { StateCode = 1, Data = needManualAudit };
        }

        /// <summary>
        /// 自检保存业务接口
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost, Route("SelfCheckSaveBusiness"), SDCAuthorize(SDCAuthorizeRole.AuthCode)]
        public ApiResult SelfCheckSaveBusiness() {
            string id = "d57ea28228934f5eb56a639524e73f73";
#if !DEBUG
            id = "8f35db3173304f68ab027910975e8d49";
#endif
            var business = oracle.GetById<BusinessBaseInfo>(id);
            if (business?.BusinessClass != nameof(BaseSurveyDataDownloadFlow)) {
                return new ApiResult() { StateCode = 0, Message = "不支持的业务类型" };
            }

            BaseSurveyDataDownloadProject project = BaseSurveyDataDownloadProject.GetByBusinessID(id);
            var result = BaseSurveyDataDownloadProject.Save(project);
            if (!string.IsNullOrWhiteSpace(result)) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = result
                };
            }
            else {
                return new ApiResult() {
                    StateCode = 1
                };
            }
        }

        /// <summary>
        /// 自检上传附件接口
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost, Route("SelfCheckAttachmentUploadRequest"), SDCAuthorize(SDCAuthorizeRole.AuthCode)]
        public ApiResult SelfCheckAttachmentUploadRequest() {
            string id = "5cd0c502f4e14e3681e3c2683b941ab3";
#if !DEBUG
            id = "809b2d1bfc1b4594bc9d6955c62fc9b9";
#endif


            //ID必须存在对应的记录
            var record = oracle.GetById<AttachmentInfo>(id);
            if (record == null) {
                return new ApiResult { StateCode = 0, Message = "上传失败，无法找到附件信息" };
            }
            HttpFileCollection files = System.Web.HttpContext.Current.Request.Files;
            //判断是否有文件上传
            if (files.Count == 0) {
                return new ApiResult { StateCode = 0, Message = "上传失败，请选择要上传的文件" };
            }
            try {
                //附件存储根路径
                string RootPath = WebConfigurationManager.AppSettings["RootPath"];
                string Year = DateTime.Now.Year.ToString();
                string Month = DateTime.Now.Month.ToString();
                string Day = DateTime.Now.Day.ToString();
                //文件存储路径
                string SavePath = Year + "\\" + Month + "\\" + Day + "\\" + record.BusinessID;
                //路径不存在时，自动创建
                if (!Directory.Exists(RootPath + "\\" + SavePath)) {
                    Directory.CreateDirectory(RootPath + "\\" + SavePath);
                }
                SavePath = SavePath + "\\" + id;
                //文件原始后缀名
                string OldFileEextension = Path.GetExtension(files[0].FileName);
                //文件名称
                string Fname = Path.GetFileNameWithoutExtension(files[0].FileName);
                //保存文件
                files[0].SaveAs(RootPath + "\\" + SavePath);
                //判断上传内容是否为图片
                string[] ImageTypes = new string[] { ".JPG", ".JPEG", ".PNG", ".GIF", ".BMP" };
                string SLSavePath = "";
                if (Array.IndexOf(ImageTypes, OldFileEextension.ToUpper()) != -1) {
                    SLSavePath = SavePath + ".thumb";
                    long quality = 75;   //图片质量,优选75质量，存储比较小
                    AttachmentManageController.SendSmallImage(RootPath + "\\" + SavePath, RootPath + "\\" + SLSavePath, 300, 300, quality, "CUT");
                }
                //修改文件后缀名，防止部分文件被直接执行 暂时禁用 
                // Path.ChangeExtension(YSSavePath, ".bak");
                //更新附件信息
                record.AttachmentName = Fname;
                record.AttachmentExt = OldFileEextension;
                record.UploadTime = DateTime.Now;
                record.AttachmentPath = SavePath;
                record.AttachmentThumbPath = SLSavePath;
                record.AttachmentLength = files[0].ContentLength;
                oracle.UpdateOne(record);
                LogService.WriteLogs(Request, "附件管理", $"上传附件 >> 附件ID：{record.ID} >> 附件名称：{record.AttachmentName}", new HttpRequestInfoHelper(((HttpContextBase)Request.Properties["MS_HttpContext"]).Request));
                dynamic ResultInfo = new {
                    ID = id,
                    AttachmentName = Fname,
                    AttachmentExt = OldFileEextension,
                    AttachmentLength = files[0].ContentLength,
                    AttachmentType = record.AttachmentType,
                    AttachmentCategories = record.AttachmentCategories
                };
                return new ApiResult { StateCode = 1, Message = "上传成功", Data = ResultInfo };
            }
            catch (Exception e) {
                LogService.WriteLogs(Request, "附件管理", $"上传附件异常 >> {e.GetStackTraces()}", new HttpRequestInfoHelper(((HttpContextBase)Request.Properties["MS_HttpContext"]).Request));
                return new ApiResult { StateCode = 0, Message = "上传失败，错误信息：" + e.Message + "" };
            }
        }
    }

    /// <summary>
    /// 业务审核参数
    /// </summary>
    public class AuditParameters {
        /// <summary>
        /// 审核环节，表示在哪个环节审核
        /// </summary>
        public int AuditActionId { get; set; }
        /// <summary>
        /// 业务信息表名称
        /// </summary>
        public string ContentInfoName { get; set; }
        /// <summary>
        /// 表示ContentInfoName对应的哪个字段表示签收
        /// </summary>
        public string SignFieldName { get; set; }
        /// <summary>
        /// 签收前的字段的值
        /// </summary>
        public int BeforeSignValue { get; set; }
        /// <summary>
        /// 签收后字段的值
        /// </summary>
        public int SignedValue { get; set; }
        /// <summary>
        /// 业务审核默认参数
        /// </summary>
        public static Dictionary<string, AuditParameters> Config => new Dictionary<string, AuditParameters>() {
            //不动产实测测绘业务设置
            {
                "RealEstateActualSurveyFlow",
                new AuditParameters() {
                    AuditActionId = 5,
                    ContentInfoName = "EstateActualSurveyContentInfo",
                    SignFieldName = "DataCheckState",
                    BeforeSignValue = 1,
                    SignedValue = 3
                }
            },
            {
                "RealEstatePreCheckSurveyFlow",
                new AuditParameters() {
                    AuditActionId = 5,
                    ContentInfoName = "PreSurvey_ContentInfo",
                    SignFieldName = "DataCheckState",
                    BeforeSignValue = 1,
                    SignedValue = 3
                }
            },
            {
                "RealEstatePreSurveyBuildingTableChangeFlow",
                new AuditParameters() {
                    AuditActionId = 1,
                    ContentInfoName = "PreSurvey_ContentInfo",
                    SignFieldName = "DataCheckState",
                    BeforeSignValue = 0,
                    SignedValue = 3
                }
            },
            {
                "RealEstatePreSurveyResultChangeFlow",
                new AuditParameters() {
                    AuditActionId = 4,
                    ContentInfoName = "PreSurvey_ContentInfo",
                    SignFieldName = "DataCheckState",
                    BeforeSignValue = 1,
                    SignedValue = 3
                }
            },
            {
                "RealEstateActualBuildingTableChangeFlow",
                new AuditParameters() {
                    AuditActionId = 1,
                    ContentInfoName = "EstateActualSurveyContentInfo",
                    SignFieldName = "DataCheckState",
                    BeforeSignValue = 0,
                    SignedValue = 3
                }
            },
            {
                "RealEstateActualResultChangeFlow",
                new AuditParameters() {
                    AuditActionId = 4,
                    ContentInfoName = "EstateActualSurveyContentInfo",
                    SignFieldName = "DataCheckState",
                    BeforeSignValue = 1,
                    SignedValue = 3
                }
            },
            {
                "MeasurePutLinePreCheckFlow",
                new AuditParameters() {
                    AuditActionId = 4,
                    ContentInfoName = "PutLine_ContentInfo",
                    SignFieldName = "DataCheckState",
                    BeforeSignValue = 1,
                    SignedValue = 3
                }
            },
            {
                "RealEstatePreCheckSurveyAutoFlow",
                new AuditParameters() {
                    AuditActionId = 5,
                    ContentInfoName = "PreSurvey_ContentInfo",
                    SignFieldName = "DataCheckState",
                    BeforeSignValue = 1,
                    SignedValue = 3
                }
            },
            {
                "CouncilPlanCheckFlow",
                new AuditParameters() {
                    AuditActionId = 5,
                    ContentInfoName = "CouncilPlanCheckContentInfo",
                    SignFieldName = "DataCheckState",
                    BeforeSignValue = 1,
                    SignedValue = 3
                }
            },
            {
                "CouncilMeasurePutLinePreCheckFlow",
                new AuditParameters() {
                    AuditActionId = 4,
                    ContentInfoName = "PutLine_ContentInfo",
                    SignFieldName = "DataCheckState",
                    BeforeSignValue = 1,
                    SignedValue = 3
                }
            },
            {
                "RealEstateOverallActualSurveyFlow",
                new AuditParameters() {
                    AuditActionId = 5,
                    ContentInfoName = "EstateActualSurveyContentInfo",
                    SignFieldName = "DataCheckState",
                    BeforeSignValue = 1,
                    SignedValue = 3
                }
            },

            //todo 新的业务设置持续添加
        };
    }
}

﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using Newtonsoft.Json;
using SDCPCWeb.Extensions;
using SDCPCWeb.Models;
using SDCPCWeb.Models.CompanyRegister;
using SDCPCWeb.Models.Result;
using SDCPCWeb.Services;
using Oracle.ManagedDataAccess.Client;
using SDCPCWeb.Models.BusinessFlow;
using SDCPCWeb.Models.System;

namespace SDCPCWeb.Controllers {
    /// <summary>
    /// 多测合一单位信息管理控制器
    /// </summary>
    [RoutePrefix("interal/CompanyManage")]
    [xCloudUserAuthorize]
    public class CompanyManageController : ApiController {
        private readonly OracleDataService service = new OracleDataService();
        private UserInfo UserInfo => (UserInfo)Request.Properties["SDC-UserInfo"];

        /// <summary>
        /// 获取单位列表
        /// </summary>
        /// <param name="type">1,测绘单位；2,开发商；3,机关单位；4,一般企业</param>
        /// <param name="companyName"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns>CompanyType为1时与GetCompanyList相同</returns>
        [HttpGet]
        [Route("GetCompanyList")]
        public ApiResult GetCompanyList(CompanyType type, string companyName = "", int pageIndex = 1, int pageSize = 20, bool? isSJZT = false) {
            if (companyName?.Contains("'") == true) {
                return new ApiResult { StateCode = 0, Message = "参数错误" };
            }

            if (!Enum.IsDefined(typeof(CompanyType), type)) {
                return new ApiResult { StateCode = 0, Message = "参数错误，单位类型不正确" };
            }
            var companyType = type.ToName();

            var where = "CompanyType=:type";
            var sort = "CompanyName";
            var parameters = new List<OracleParameter>() {
                new OracleParameter(":type", OracleDbType.Varchar2) { Value = companyType }
            };
            if (!string.IsNullOrWhiteSpace(companyName)) {
                //防注入
                where += " AND (CompanyName Like :name Or CreditCode=:code)";
                parameters.Add(new OracleParameter(":name", OracleDbType.Varchar2) { Value = $"%{companyName}%" });
                parameters.Add(new OracleParameter(":code", OracleDbType.Varchar2) { Value = companyName });
            }
            var list = service.GetPagerList<CompanyBaseInfo>(pageIndex, pageSize, where, sort, parameters.ToArray());
            var total = service.GetCount<CompanyBaseInfo>(where, parameters.ToArray());
            if (isSJZT == true) {
                return new ApiResult {
                    StateCode = 1,
                    Data = new {
                        DataTable = list.Select(c => new {
                            c.CompanyName,
                            c.CreditCode
                        }),
                        Page = new {
                            PageIndex = pageIndex,
                            PageSize = pageSize,
                            TotalCount = total,
                        }
                    }
                };
            }
            return new ApiResult {
                StateCode = 1,
                Data = new {
                    DataTable = list.Select(c => new {
                        c.ID,
                        c.CompanyName,
                        c.CreditCode,
                        c.CompanyType,
                        c.Contacter,
                        c.ContacterPhone
                    }),
                    Page = new {
                        PageIndex = pageIndex,
                        PageSize = pageSize,
                        TotalCount = total,
                    }
                }
            };
        }

        /// <summary>
        /// 获取单位人员列表
        /// </summary>
        /// <param name="companyId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetCompanyMemberList")]
        public ApiResult GetCompanyMemberList(string companyId, int pageIndex = 1, int pageSize = 20) {
            if (string.IsNullOrWhiteSpace(companyId)) {
                return new ApiResult() { StateCode = 0, Message = "companyId不能为空" };
            }
            var company = service.GetById<CompanyBaseInfo>(companyId);

            var typeName = company.CompanyType;
            var type = typeName.CompanyTypeFromName();
            var where = "RelationRequestId=:id";
            var sort = "PersonName";
            var parameters = new[] { new OracleParameter(":id", OracleDbType.Varchar2) { Value = company.ID } };
            //结果
            IEnumerable<object> data;
            int total;
            if (type == CompanyType.SurveyCompany) {
                //获取分页结果
                data = service.GetPagerList<CompanyEmployees>(pageIndex, pageSize, where, sort, parameters);
                //获取分页前总数
                total = service.GetCount<CompanyEmployees>(where, parameters);
            }
            else {
                //获取分页结果
                data = service.GetPagerList<DeveloperEmployee>(pageIndex, pageSize, where, sort, parameters);
                //获取分页前总数
                total = service.GetCount<DeveloperEmployee>(where, parameters);

            }
            return new ApiResult {
                StateCode = 1,
                Data = new {
                    DataTable = data,
                    Page = new {
                        PageIndex = pageIndex,
                        PageSize = pageSize,
                        Total = total
                    }
                }
            };

        }

        /// <summary>
        /// 新建或者保存单位信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("CreateOrSaveCompanyInfo")]
        public ApiResult CreateOrSaveCompanyInfo(CompanyBaseInfoModel model) {
            if (model == null) {
                return new ApiResult() { StateCode = 0, Message = "请输入正确的信息" };
            }
            //空值检测
            if (string.IsNullOrWhiteSpace(model.CompanyName)) {
                return new ApiResult() { StateCode = 0, Message = "单位名称不能为空" };
            }
            if (string.IsNullOrWhiteSpace(model.CreditCode)) {
                return new ApiResult() { StateCode = 0, Message = "统一社会信用代码不能为空" };
            }
            if (string.IsNullOrWhiteSpace(model.CompanyAddress)) {
                return new ApiResult() { StateCode = 0, Message = "单位地址不能为空" };
            }
            if (string.IsNullOrWhiteSpace(model.Contacter)) {
                return new ApiResult() { StateCode = 0, Message = "联系人不能为空" };
            }
            if (string.IsNullOrWhiteSpace(model.ContacterPhone)) {
                return new ApiResult() { StateCode = 0, Message = "联系电话不能为空" };
            }

            //目前仅能够手动添加和编辑【机关单位】和【一般企业】
            var type = model.CompanyType.CompanyTypeFromName();
            if (type != CompanyType.Institute && type != CompanyType.NormalCompany) {
                return new ApiResult() { StateCode = 0, Message = "单位类型不正确，本操作只能是“机关单位”或者“一般企业”" };
            }

            if (!string.IsNullOrWhiteSpace(model.ID)) {
                //涉及修改
                var record = service.GetById<CompanyBaseInfo>(model.ID);
                if (record != null) {
                    //是更新没错了
                    //更新的时候，如果单位类型不正确，不能进行更新
                    if (record.CompanyType.CompanyTypeFromName() != type) {
                        return new ApiResult() { StateCode = 0, Message = $"更新失败，不能把单位从【{record.CompanyType}】更新为【{model.CompanyType}】" };
                    }

                    //设定更新信息，并非所有信息都能更新
                    record.CompanyName = model.CompanyName;
                    record.CreditCode = model.CreditCode;
                    record.CompanyAddress = model.CompanyAddress;
                    record.LegalPersonName = model.LegalPersonName;
                    record.Contacter = model.Contacter;
                    record.ContacterPhone = model.ContacterPhone;

                    //更新内容用于写到日志
                    var updateData = service.GetUpdateList(record);
                    service.UpdateOne(record);
                    Request.WriteSDCLog("更新单位信息", $"单位信息更新 >> 单位ID：{record.ID} >> 单位名称：{record.CompanyName} >> 更新内容：{JsonConvert.SerializeObject(updateData, SystemConfig.JsonDateTimeConverter)}");
                    return new ApiResult() { StateCode = 1, Message = "更新成功", Data = record };
                }
            }
            //是新建
            if (service.GetCount<CompanyBaseInfo>("CompanyName=:cname Or CreditCode=:code"
                , new OracleParameter(":cname", OracleDbType.Varchar2) { Value = model.CompanyName }
                , new OracleParameter(":code", OracleDbType.Varchar2) { Value = model.CreditCode }
                ) > 0) {
                //重复了
                return new ApiResult() { StateCode = 0, Message = $"添加失败，已存在名称为【{model.CompanyName }】或者证件号为【{model.CreditCode}】的单位" };
            }
            var newId = Guid.NewGuid().ToString("N");
            var company = new CompanyBaseInfo() {
                ID = newId,
                RelationRequestId = newId,
                CompanyName = model.CompanyName,
                CompanyType = model.CompanyType,
                CreditCode = model.CreditCode,
                CompanyAddress = model.CompanyAddress,
                LegalPersonName = model.LegalPersonName,
                LegalPersonNumber = model.LegalPersonNumber,
                Contacter = model.Contacter,
                ContacterPhone = model.ContacterPhone,
            };
            service.InsertOne(company);
            var newLog = JsonConvert.SerializeObject(new {
                ID = newId,
                RelationRequestId = newId,
                CompanyName = model.CompanyName,
                CompanyType = model.CompanyType,
                CreditCode = model.CreditCode,
                CompanyAddress = model.CompanyAddress,
                LegalPersonName = model.LegalPersonName,
                LegalPersonNumber = model.LegalPersonNumber,
                Contacter = model.Contacter,
                ContacterPhone = model.ContacterPhone,
            });
            Request.WriteSDCLog("新建单位信息", $"新建单位信息 >> 单位ID：{company.ID} >> 单位名称：{company.CompanyName} >> 单位信息：{JsonConvert.SerializeObject(newLog, SystemConfig.JsonDateTimeConverter)}");
            return new ApiResult() { StateCode = 1, Message = "添加成功", Data = company };

        }

        /// <summary>
        /// 删除单位信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <remarks>所删除的记录回先写入到删除记录表，所删除单位信息，会将单位对应的人员信息一并删除</remarks>
        [HttpPost]
        [Route("DeleteCompanyInfo")]
        public ApiResult DeleteCompanyInfo(DeleteCompanyInfoInput input) {
            if (input == null) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }

            if (string.IsNullOrWhiteSpace(input.CompanyId)) {
                return new ApiResult() { StateCode = 0, Message = "请指定要删除的单位ID" };
            }
            if (string.IsNullOrWhiteSpace(input.DeleteReason)) {
                return new ApiResult() { StateCode = 0, Message = "请填写删除原因" };
            }

            var record = service.GetById<CompanyBaseInfo>(input.CompanyId);
            if (record == null) {
                return new ApiResult() { StateCode = 0, Message = "在系统中未找到要删除的单位" };
            }

            //找到了要删除的单位

            //目前仅能够手动删除【机关单位】和【一般企业】
            var type = record.CompanyType.CompanyTypeFromName();
            if (type != CompanyType.Institute && type != CompanyType.NormalCompany) {
                return new ApiResult() { StateCode = 0, Message = "单位类型不正确，本操作只能是“机关单位”或者“一般企业”" };
            }

            //如果名下已有办理过业务，则此单位不能删除
            var cnt = service.GetCount<BusinessBaseInfo>("DeveloperNo=:cno", new OracleParameter(":cno", OracleDbType.Varchar2) { Value = record.CreditCode });
            if (cnt > 0) {
                return new ApiResult() { StateCode = 0, Message = "该单位已办理过业务，不能删除" };
            }
            //todo 若还有其它限制删除的逻辑，继续补充

            //可以删除了，按照以下步骤进行删除

            //删除基本信息
            service.DeleteOne(record);
            LogService.WriteDeleteLog(record, UserInfo.PersonName);
            //删除人员信息
            //因为目前仅能够手动删除【机关单位】和【一般企业】，因此是固定的表
            var members = service.GetList<DeveloperEmployee>("RelationRequestId=:id", new OracleParameter(":id", OracleDbType.Varchar2) { Value = record.ID });
            service.DeleteMany<DeveloperEmployee>("RelationRequestId=:id", new OracleParameter(":id", OracleDbType.Varchar2) { Value = record.ID });

            //记录日志
            foreach (var member in members) {
                LogService.WriteDeleteLog(member, UserInfo.PersonName);
            }
            Request.WriteSDCLog("删除单位信息", $"删除单位信息 >> 单位ID：{record.ID} >> 单位名称：{record.CompanyName} >> 单位代码：{record.CreditCode} >> 原因：{input.DeleteReason}");

            return new ApiResult() { StateCode = 1, Message = "单位已删除" };
        }

        /// <summary>
        /// 添加单位人员信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("CreateOrSaveCompanyMember")]
        public ApiResult CreateOrSaveCompanyMember(DeveloperEmployeeModel model) {
            if (model == null) {
                return new ApiResult() { StateCode = 0, Message = "请输入正确的信息" };
            }
            //空值检测
            if (string.IsNullOrWhiteSpace(model.RelationRequestId)) {
                return new ApiResult { StateCode = 0, Message = "单位ID不能为空，请传入正确的参数" };
            }
            if (string.IsNullOrWhiteSpace(model.PersonRole)) {
                return new ApiResult { StateCode = 0, Message = "人员角色不能为空，请传入正确的参数" };
            }
            if (string.IsNullOrWhiteSpace(model.PersonNumber)) {
                return new ApiResult { StateCode = 0, Message = "人员身份证号不能为空，请传入正确的参数" };
            }
            if (string.IsNullOrWhiteSpace(model.PersonName)) {
                return new ApiResult { StateCode = 0, Message = "人员姓名不能为空，请传入正确的参数" };
            }

            //逻辑检测
            var company = service.GetById<CompanyBaseInfo>(model.RelationRequestId);
            if (company == null) {
                return new ApiResult() { StateCode = 0, Message = "单位信息不存在，不能添加人员" };
            }

            if (!new[] { "单位管理员", "报建员" }.Contains(model.PersonRole)) {
                return new ApiResult { StateCode = 0, Message = "人员角色只能是“单位管理员”或者“报建员”，请传入正确的参数" };
            }

            //目前仅能够手动添加和编辑【机关单位】和【一般企业】
            var type = company.CompanyType.CompanyTypeFromName();
            if (type != CompanyType.Institute && type != CompanyType.NormalCompany) {
                return new ApiResult() { StateCode = 0, Message = "单位类型不正确，本操作只能是“机关单位”或者“一般企业”" };
            }

            //添加或者编辑
            if (!string.IsNullOrWhiteSpace(model.ID)) {
                //涉及修改
                var record = service.GetById<DeveloperEmployee>(model.ID);
                if (record != null) {
                    //是更新没错了

                    //如果修改的是身份证号，则还需要检查是否符合逻辑
                    if (record.PersonNumber != model.PersonNumber) {
                        //1、新身份证号不能是测绘单位的人员
                        var check1 = service.GetCount<CompanyEmployees>("PersonNumber=:pno", new OracleParameter(":pno", OracleDbType.Varchar2) { Value = model.PersonNumber });
                        if (check1 > 0) {
                            return new ApiResult() { StateCode = 0, Message = "该身份证号是测绘单位的人员" };
                        }

                        //2、新身份证号不能是同一单位的其它人员
                        var check2 = service.GetCount<DeveloperEmployee>(
                            "PersonNumber=:pno AND RelationRequestId=:cid AND ID!=:mid"
                            , new OracleParameter(":pno", OracleDbType.Varchar2) { Value = model.PersonNumber }
                            , new OracleParameter(":cid", OracleDbType.Varchar2) { Value = record.RelationRequestId }
                            , new OracleParameter(":mid", OracleDbType.Varchar2) { Value = record.ID }
                            );
                        if (check2 > 0) {
                            return new ApiResult() { StateCode = 0, Message = "该身份证号已对应本单位的其它人员" };
                        }
                    }

                    //设定更新信息，并非所有信息都能更新
                    record.PersonName = model.PersonName;
                    record.PersonNumber = model.PersonNumber;
                    record.PersonPhone = model.PersonPhone;
                    record.PersonRole = model.PersonRole;

                    //更新内容用于写到日志
                    var updateData = service.GetUpdateList(record);
                    service.UpdateOne(record);
                    Request.WriteSDCLog("更新人员信息", $"更新人员信息 >> 人员ID：{record.ID} >> 姓名：{record.PersonName} >> 更新内容：{JsonConvert.SerializeObject(updateData, SystemConfig.JsonDateTimeConverter)}");
                    return new ApiResult() { StateCode = 1, Message = "更新成功", Data = record };
                }
            }
            //是新建
            //进行新建逻辑检测
            {
                //身份证号不能是测绘单位的人员
                var check1 = service.GetCount<CompanyEmployees>("PersonNumber=:pno", new OracleParameter(":pno", OracleDbType.Varchar2) { Value = model.PersonNumber });
                if (check1 > 0) {
                    return new ApiResult() { StateCode = 0, Message = "该身份证号是测绘单位的人员" };
                }

                //身份证号不能是同一单位的其它人员
                var check2 = service.GetCount<DeveloperEmployee>(
                    "PersonNumber=:pno AND RelationRequestId=:cid"
                    , new OracleParameter(":pno", OracleDbType.Varchar2) { Value = model.PersonNumber }
                    , new OracleParameter(":cid", OracleDbType.Varchar2) { Value = model.RelationRequestId }
                );
                if (check2 > 0) {
                    return new ApiResult() { StateCode = 0, Message = "该身份证号已对应本单位的其它人员" };
                }
            }
            var newId = Guid.NewGuid().ToString("N");
            var member = new DeveloperEmployee() {
                ID = newId,
                RelationRequestId = model.RelationRequestId,
                PersonName = model.PersonName,
                PersonNumber = model.PersonNumber,
                PersonPhone = model.PersonPhone,
                PersonRole = model.PersonRole,
            };
            service.InsertOne(member);
            var newLog = JsonConvert.SerializeObject(new {
                ID = newId,
                RelationRequestId = model.RelationRequestId,
                PersonName = model.PersonName,
                PersonNumber = model.PersonNumber,
                PersonPhone = model.PersonPhone,
                PersonRole = model.PersonRole,
            });
            Request.WriteSDCLog("新增人员信息", $"新增人员信息 >> 人员ID：{member.ID} >> 姓名：{member.PersonName} >> 人员信息：{JsonConvert.SerializeObject(newLog, SystemConfig.JsonDateTimeConverter)}");
            return new ApiResult() { StateCode = 1, Message = "添加成功", Data = member };
        }

        /// <summary>
        /// 删除单位人员
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteCompanyMember")]
        public ApiResult DeleteCompanyMember(DeleteCompanyMemberInput input) {
            if (input == null) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }

            if (string.IsNullOrWhiteSpace(input.MemberId)) {
                return new ApiResult() { StateCode = 0, Message = "请指定要删除的人员ID" };
            }
            //if (string.IsNullOrWhiteSpace(input.DeleteReason)) {
            //    return new ApiResult() { StateCode = 0, Message = "请填写删除原因" };
            //}

            var record = service.GetById<DeveloperEmployee>(input.MemberId);
            if (record == null) {
                return new ApiResult() { StateCode = 0, Message = "在系统中未找到要删除的人员" };
            }

            //找到了要删除的单位

            //目前仅能够手动删除【机关单位】和【一般企业】
            var company = service.GetById<CompanyBaseInfo>(record.RelationRequestId);
            if (company != null) {
                var type = company.CompanyType.CompanyTypeFromName();
                if (type != CompanyType.Institute && type != CompanyType.NormalCompany) {
                    return new ApiResult() { StateCode = 0, Message = "单位类型不正确，本操作只能是“机关单位”或者“一般企业”" };
                }
            }

            //判断删除的人员是否最后一个个人单位管理员
            if (record.PersonRole == "单位管理员") {
                var records = service.GetList<DeveloperEmployee>("RelationRequestId=:cid AND PersonRole='单位管理员' AND (USERTYPE != '1' OR USERTYPE IS NULL)"
                    , new OracleParameter(":cid", OracleDbType.Varchar2) { Value = company.ID }
                );
                if (records.Count <= 1) {
                    return new ApiResult() { StateCode = 0, Message = $"最后一个个人单位管理员不能删除" };
                }
            }

            //判断是否有未移交的业务，如果有就提示先移交业务
            if (service.Exists<BusinessBaseInfo>($"CreatePersonNo='{record.PersonNumber}' AND CreateCompanyNo='{company.CreditCode}'")) {
                return new ApiResult() { StateCode = 0, Message = $"请先联系单位管理员移交业务再删除" };
            }

            //todo 若还有其它限制删除的逻辑，继续补充

            //可以删除了
            service.DeleteOne(record);
            LogService.WriteDeleteLog(record, UserInfo.PersonName);

            Request.WriteSDCLog("删除单位人员信息", $"删除单位人员信息 >> 单位ID：{company?.ID} >> 单位名称：{company?.CompanyName} >> 姓名：{record.PersonName} >> 身份证号：{record.PersonNumber}");

            return new ApiResult() { StateCode = 1, Message = "人员已删除" };
        }

        /// <summary>
        /// 获取多测合一所有业务类型
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAllFlows")]
        public ApiResult GetAllFlows() {
            return new ApiResult() {
                StateCode = 1,
                Data = BusinessFlowConfig.AllFlows.Select(t => new {
                    BusinessClass = t.GetType().Name,
                    BusinessName = t.FlowName
                })
            };
        }

        /// <summary>
        /// 更新测绘单位的多测合一业务范围
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("UpdateSurveyCompanyBusinessRange")]
        public ApiResult UpdateSurveyCompanyBusinessRange(string companyId, string[] businessClasses) {
            //检查参数
            if (string.IsNullOrWhiteSpace(companyId)) {
                return new ApiResult() { StateCode = 0, Message = "companyId不能为空" };
            }

            if (businessClasses == null || !businessClasses.Any()) {
                return new ApiResult() { StateCode = 0, Message = "请指定要更新的业务类型" };
            }

            //判断业务类型是否匹配
            if (businessClasses.Any(b => BusinessFlowConfig.AllFlows.All(f => f.GetType().Name != b))) {
                var notMatchClasses = businessClasses.Where(b => BusinessFlowConfig.AllFlows.All(f => f.GetType().Name != b)).ToArray();
                return new ApiResult() { StateCode = 0, Message = $"“{string.Join("，", notMatchClasses)}”是不支持的业务类型" };
            }

            var company = service.GetById<CompanyBaseInfo>(companyId);

            if (company == null) {
                return new ApiResult() { StateCode = 0, Message = "单位信息不存在" };
            }

            if (company.CompanyType.CompanyBaseTypeFromName() != CompanyBaseType.SurveyCompany) {
                return new ApiResult() { StateCode = 0, Message = "该单位不属于测绘单位，不能设置业务范围" };
            }

            var quality = service.GetById<CompanyQualification>(companyId);
            if (quality == null) {
                return new ApiResult() { StateCode = 0, Message = "获取不到该测绘单位的资质信息，无法设置业务范围" };
            }

            //设置业务范围
            var flows = BusinessFlowConfig.AllFlows.Where(f => businessClasses.Contains(f.GetType().Name));
            quality.BusinessRange = JsonConvert.SerializeObject(flows.Select(f => f.GetType().Name).ToList());
            service.UpdateOne(quality);

            //写入日志
            Request.WriteSDCLog("更新业务范围", $"更新业务范围 >> 单位ID：{company.ID} >> 单位名称:{company.CompanyName} >> 业务范围：{quality.BusinessRange}");
            Hangfire.BackgroundJob.Enqueue(() => Jobs.SurveyCompanyJob.UpdateSurveyCompanyBusinessClass(companyId, null));

            return new ApiResult() { StateCode = 1, Message = "已更新测绘单位业务范围", Data = null };
        }

        public class DeleteCompanyInfoInput {
            /// <summary>
            /// 单位ID
            /// </summary>
            public string CompanyId { get; set; }
            /// <summary>
            /// 删除原因
            /// </summary>
            public string DeleteReason { get; set; }
        }

        public class DeleteCompanyMemberInput {
            /// <summary>
            /// 单位ID
            /// </summary>
            public string MemberId { get; set; }
            /// <summary>
            /// 删除原因
            /// </summary>
            public string DeleteReason { get; set; }
        }


    }
}

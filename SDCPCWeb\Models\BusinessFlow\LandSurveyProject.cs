﻿using Newtonsoft.Json;
using SDCPCWeb.Models.Attachment;
using SDCPCWeb.Models.System;
using SDCPCWeb.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using SDCPCWeb.Models.BusinessContent.ZhengChai;
using SDCPCWeb.Models.BusinessContent.YanXian;

namespace SDCPCWeb.Models.BusinessFlow {
    /// <summary>
    /// 征地拆迁数据交汇业务
    /// </summary>
    public class LandSurveyProject : BaseProject {
        //private static OracleDataService service = new OracleDataService();
        /// <summary>
        /// 基本信息
        /// </summary>
        public override BusinessBaseInfoModel BaseInfo { get; set; }
        /// <summary>
        /// 预测绘内容信息
        /// </summary>
        public LandSurvey_ContentInfo LandSurvey_ContentInfo { get; set; }
        /// <summary>
        /// 附件信息
        /// </summary>
        public override List<AttachmentInfoModel> Attachments { get; set; }
        /// <summary>
        /// 当前经过的流转环节信息
        /// </summary>
        public override List<BusinessLinkInfoModel> ActionsInfos { get; set; }
        /// <summary>
        /// 最新的环节，当前环节
        /// </summary>
        public override BusinessLinkInfoModel CurrentAction { get; set; }
        /// <summary>
        /// 工作流定义信息
        /// </summary>
        public override BusinessFlowBase FlowInfo { get; } = BusinessFlowConfig.LandSurveyProjectFlow;
        /// <summary>
        /// 根据ID获取信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static LandSurveyProject GetByBusinessID(string id) {
            LandSurveyProject project = new LandSurveyProject();
            using (OracleDataService service = new OracleDataService()) {
                project.BaseInfo = service.GetById<BusinessBaseInfo>(id);
                project.LandSurvey_ContentInfo = service.GetById<LandSurvey_ContentInfo>(id);
                project.Attachments = service.GetList<AttachmentInfo>("BusinessID='" + id + "' and StateCode=0")?.Select(a => a.ToModel()).ToList();
                project.ActionsInfos = service.GetList<BusinessLinkInfo>("BusinessID='" + id + "'")?.Select(a => a.ToModel()).OrderBy(a => a.StartTime).ToList();
                project.CurrentAction = project.ActionsInfos?.OrderByDescending(a => a.StartTime).FirstOrDefault();
                return project;
            }
        }

        /// <summary>
        /// 流程服务
        /// </summary>
        public static FlowService<LandSurveyProjectFlow> FlowService => new FlowService<LandSurveyProjectFlow>();
    }
    /// <summary>
    /// 征地拆迁数据交汇业务流程定义
    /// </summary>
    public sealed class LandSurveyProjectFlow : BusinessFlowBase {
        public LandSurveyProjectFlow() {
            //实例化时，在构造函数定义工作流程
            FlowName = "征地拆迁数据交汇";
            Catalog = "测绘数据汇交";
            FlowActionInfo = new BusinessFlowActionInfo() {
                Actions = new[] {
                    new BusinessFlowAction() {
                        ID = 0,
                        Index = 0,
                        ActionRoles = new[] {UserRole.SurveyNormal, UserRole.SurveyAdmin, UserRole.SurveyMaster},
                        Name = "汇交测绘数据"
                    }
                },
                StartActionId = 0,
                EndActionIds = new[] { 0 },
                Routes = new[] {
                    new BusinessFlowRoute() {FromActionId = 0,ToActionId = 0}
                },
                BackRoutes = new BusinessFlowBackRoute[0],
                FinishWhenIntoEndAction = false
            };
        }

        #region Overrides of BusinessFlowBase
        /// <summary>
        /// 流程环节定义信息JSON
        /// </summary>
        /// <returns></returns>
        public override string FlowActionJSON {
            get {
                return JsonConvert.SerializeObject(FlowActionInfo.Actions.Select(a => new {
                    a.ID,
                    a.Name
                }));
            }
        }

        /// <summary>
        /// 是否包含测绘作业
        /// </summary>
        public override bool HasSurvey { get; } = true;

        /// <summary>
        /// 流程是否启用
        /// </summary>
        public override bool Enable => BusinessFlowConfig.EnableFlows.Contains(GetType().Name);

        /// <summary>
        /// 该流程是否需要协议备案
        /// </summary>
        public override bool NeedBDCProtocol { get; } = false;

        /// <summary>
        /// 该流程是否在办理业务中可见
        /// </summary>
        public override bool Visible => BusinessFlowConfig.VisibleFlows.Contains(GetType().Name);
        #endregion
    }
}
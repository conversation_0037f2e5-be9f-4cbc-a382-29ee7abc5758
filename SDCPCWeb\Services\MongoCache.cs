﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration;
using System.Linq;
using System.Web;
using Hangfire;
using MongoDB.Driver;
using SDCPCWeb.Jobs;
using SDCPCWeb.Models.Mongo;

namespace SDCPCWeb.Services {
    /// <summary>
    /// MongoDB缓存
    /// </summary>
    public static class MongoCache {
        /// <summary>
        /// 缓存集合
        /// </summary>
        private static IMongoCollection<Cache> Collection { get; set; }

        public static void Initialize(string mongoConnectionConfigName) {
            var mongoConnString = ConfigurationManager.ConnectionStrings[mongoConnectionConfigName]?.ConnectionString;

            if (string.IsNullOrWhiteSpace(mongoConnString)) {
                throw new Exception($"数据库连接配置{mongoConnectionConfigName}指定的连接字符串不存在");
            }

            var mongoUrl = new MongoUrl(mongoConnString);
            if (string.IsNullOrWhiteSpace(mongoUrl.DatabaseName)) {
                throw new Exception($"数据库连接配置{mongoConnectionConfigName}指定的连接字符串“{mongoConnString}”必须指定数据库名");
            }

            var client = new MongoClient(mongoUrl);
            Collection = client.GetDatabase(mongoUrl.DatabaseName).GetCollection<Cache>(typeof(Cache).Name);
        }

        /// <summary>
        /// 获取缓存内容
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public static string Get(string key) {
            var cache = Collection.Find(c => c.Key == key).FirstOrDefault();
            if (cache != null && DateTime.Now > DateTime.Parse(cache.ExpireTime)) {
                Collection.DeleteMany(c => c.Key == cache.Key);
                return null;
            }
            return cache?.Value;
        }

        /// <summary>
        /// 设置或更新缓存
        /// </summary>
        /// <param name="key"></param>
        /// <param name="val"></param>
        /// <param name="lifeSpan"></param>
        /// <returns></returns>
        public static void Set(string key, string val, TimeSpan lifeSpan) {
            var item = new Cache(key, val, lifeSpan);
            var cache = Collection.Find(c => c.Key == item.Key).FirstOrDefault();
            if (cache != null) {
                Collection.ReplaceOne(c => c.Key == item.Key, item);
            }
            else {
                Collection.InsertOne(item);
            }
        }

        /// <summary>
        /// 移除缓存信息
        /// </summary>
        /// <param name="key"></param>
        public static void Clear(string key) {
            Collection.DeleteMany(c => c.Key == key);
        }

        /// <summary>
        /// 清除过期缓存
        /// </summary>
        [DisplayName("清除过期的缓存"), NoRecordJob]
        public static void ClearExpiredCaches() {
            var filter = Builders<Cache>.Filter.Lt(c => c.ExpireTime, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            Collection.DeleteMany(filter);
        }
    }
}
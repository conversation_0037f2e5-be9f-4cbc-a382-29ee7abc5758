﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using SDCPCWeb.Models.System;
using SDCPCWeb.Services;

namespace SDCPCWeb.Models.CompanyRegister {
    /// <summary>
    /// 单位注册申请的数据模型
    /// </summary>
    public class CompanyRegisterRequestModel {
        /// <summary>
        /// ID
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 用户ID
        /// </summary>
        public string CreateUserID { get; set; }
        /// <summary>
        /// 申请用户姓名
        /// </summary>
        public string CreateUserName { get; set; }
        /// <summary>
        /// 申请用户证件号
        /// </summary>
        public string CreateUserPersonNo { get; set; }
        /// <summary>
        /// 申请单位类型，取值范围：开发商、测绘单位、机关单位、一般企业
        /// </summary>
        public string CompanyType { get; set; }
        /// <summary>
        /// 注册单位名称
        /// </summary>
        public string CompanyName { get; set; }
        /// <summary>
        /// 注册单位统一社会信用代码
        /// </summary>
        public string CompanyNo { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }
        /// <summary>
        /// 提交审核时间
        /// </summary>
        public DateTime? PostTime { get; set; }
        /// <summary>
        /// 审核签收时间
        /// </summary>
        public DateTime? AcceptAuditTime { get; set; }
        /// <summary>
        /// 批准时间/审核通过时间
        /// </summary>
        public DateTime? SuccessTime { get; set; }
        /// <summary>
        /// 失效时间
        /// </summary>
        public DateTime? CloseTime { get; set; }
        /// <summary>
        /// 申请状态，0未提交，1已提交/待审核，2审核中，3完成注册，4退回修改，5已失效，6待协议备案
        /// </summary>
        public int StateCode { get; set; }
        /// <summary>
        /// 详细信息JSON
        /// </summary>
        public string DetailInfo { get; set; }
        /// <summary>
        /// 反馈信息
        /// </summary>
        public string ResponseMessage { get; set; }
        /// <summary>
        /// 审核人员
        /// </summary>
        public string ResponsePerson { get; set; }

    }

    public class CompanyRegisterRequest : CompanyRegisterRequestModel, IOracleDataTable {
        public CompanyRegisterRequestModel ToModel() {
            var model = JsonConvert.DeserializeObject<CompanyRegisterRequestModel>(JsonConvert.SerializeObject(this, SystemConfig.JsonDateTimeConverter));
            //处理一下多测合一
            var detailsInfo = JsonConvert.DeserializeObject<CompanyRegisterDetailInfo>(model.DetailInfo);
            var detailsInfoModel = JsonConvert.DeserializeObject<CompanyRegisterDetailInfoModel>(model.DetailInfo);
            if (detailsInfoModel.CompanyQualification != null) {
                detailsInfoModel.CompanyQualification = CompanyQualificationShowModel.FromModel(detailsInfo.CompanyQualification.ToModel());
            }

            model.DetailInfo = JsonConvert.SerializeObject(detailsInfoModel, SystemConfig.JsonDateTimeConverter);
            return model;
        }
    }
    /// <summary>
    /// 
    /// </summary>
    public class CompanyRegisterDetailInfo {
        public CompanyBaseInfo CompanyBaseInfo { get; set; }
        //public CompanyDetailsInfo CompanyDetailsInfo { get; set; }
        public List<CompanyEmployees> CompanyEmployees { get; set; }
        //public List<CompanyEquipments> CompanyEquipments { get; set; }
        public CompanyQualification CompanyQualification { get; set; }

        public CompanyRegisterDetailInfoModel ToModel() {
            return new CompanyRegisterDetailInfoModel() {
                CompanyBaseInfo = CompanyBaseInfo,
                CompanyEmployees = CompanyEmployees.Select(c => c.ToModel()).ToList(),
                CompanyQualification = CompanyQualificationShowModel.FromModel(CompanyQualification)
            };
        }
    }
    /// <summary>
    /// 多测合一单位申请的详细信息转换模型
    /// </summary>
    public class CompanyRegisterDetailInfoModel {
        public CompanyBaseInfoModel CompanyBaseInfo { get; set; }
        //public CompanyDetailsInfo CompanyDetailsInfo { get; set; }
        public List<CompanyEmployeesModel> CompanyEmployees { get; set; }
        //public List<CompanyEquipments> CompanyEquipments { get; set; }
        public CompanyQualificationShowModel CompanyQualification { get; set; }
    }

    public class CompanyInfoModifyModel : CompanyRegisterDetailInfo {
        public string ModifyReason { get; set; }
    }
}
﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Web;
using Hangfire;
using Hangfire.Console;
using Hangfire.Server;
using Microsoft.Ajax.Utilities;
using Newtonsoft.Json;
using Oracle.ManagedDataAccess.Client;
using SDCPCWeb.Models.CompanyRegister;
using SDCPCWeb.Models.System;
using SDCPCWeb.Services;

namespace SDCPCWeb.Jobs {
    /// <summary>
    /// 测绘单位相关的异步作业
    /// </summary>
    public static class SurveyCompanyJob {
        private static string Rule1CompanyId = "22c596b2a4b44b01a3c22fd9b1a80769";
        private static string Rule2CompanyId = "4b0ab80b0ba446d8bd27bfced170402e";

        /// <summary>
        /// 更新测绘单位排序列表
        /// </summary>
        /// <param name="console"></param>
        [DisplayName("更新测绘单位排序列表")]
        public static void RefreshSurveyCompanyOrder(PerformContext console) {
            using (var oracle = new OracleDataService()) {
                //获取所有测绘单位
                var surveyComapnyList = oracle.GetList<CompanyQualification>("0=0");
                var count = surveyComapnyList.Count;
                console?.WriteLine($"开始对{count}家测绘单位进行排序");

                //第一步：初始排序
                var orderList = new Dictionary<string, int>();

                console?.WriteLine("开始根据测绘资质等级进行初始排序");

                var bar1 = console?.WriteProgressBar();

                //根据最高测绘资质等级进行初始排序
                foreach (var qualification in surveyComapnyList.WithProgress(bar1)) {
                    switch (qualification.QualificationLevel) {
                        case "甲":
                            orderList.Add(qualification.ID, 4000);
                            break;
                        case "乙":
                            orderList.Add(qualification.ID, 3000);
                            break;
                        case "丙":
                            orderList.Add(qualification.ID, 2000);
                            break;
                        case "丁":
                            orderList.Add(qualification.ID, 1000);
                            break;
                        default:
                            orderList.Add(qualification.ID, 0);
                            break;
                    }
                }

                console?.WriteLine("初始排序完成");

                //第二步，同一个资质等级的单位，进行随机排序

                console?.WriteLine("同一个资质等级进行随机排序");
                //随机数生成器
                var rnd = new Random();
                var bar2 = console?.WriteProgressBar();
                var tmpList = new Dictionary<string, int>();
                orderList.CopyItemsTo(tmpList);
                //每个等级增加一个500以内的随机数
                foreach (var kv in tmpList.WithProgress(bar2)) {
                    var addrnd = rnd.Next(0, 500);
                    orderList[kv.Key] = kv.Value + addrnd;
                }

                console?.WriteLine("随机排序完成");

                orderList = orderList.OrderByDescending(kv => kv.Value).ToDictionary(kv => kv.Key, kv => kv.Value);

                //第三步，特殊排序规则
                console?.WriteLine("开始进行信用等级排序");
                //信用等级还没有开放，默认都是同一个等级，因此此处无需排序
                //attn 特殊排序算法是口头需求，因此无法追溯需求来源

                if (orderList.Keys.Contains(Rule1CompanyId)) {
                    //Rule1CompanyId的单位固定在首位
                    var max = orderList.Values.Max();
                    orderList[Rule1CompanyId] = max + 1000;

                    //重新排序
                    orderList = orderList.OrderByDescending(kv => kv.Value).ToDictionary(kv => kv.Key, kv => kv.Value);

                    //Rule2CompanyId的单位按照概率分布，随机出现在2到5位
                    //其中80%的概率出现在第二位，16%的概率出现在第三位，2%的概率出现在第四位，2%的概率出现在第5位

                    //计算概率排位
                    var rndVal = rnd.Next(0, 99);
                    var cal_r2cIndex = rndVal < 80 ? 1 : rndVal < 96 ? 2 : rndVal < 98 ? 3 : 4;
                    var r2cIndex = Array.IndexOf(orderList.Keys.ToArray(), Rule2CompanyId);
                    if (r2cIndex != cal_r2cIndex) {
                        //当目前排位与实际排位不符时，进行调整
                        switch (cal_r2cIndex) {
                            case 1:
                                orderList[Rule2CompanyId] = max + 500;
                                break;
                            case 2: {
                                    var min = orderList[orderList.Keys.ElementAt(2)];
                                    orderList[Rule2CompanyId] = rnd.Next(min, max);
                                }
                                break;
                            case 3: {
                                    var min = orderList[orderList.Keys.ElementAt(3)];
                                    var max2 = orderList[orderList.Keys.ElementAt(2)];
                                    orderList[Rule2CompanyId] = rnd.Next(min, max2);
                                }
                                break;
                            case 4: {
                                    var min = orderList[orderList.Keys.ElementAt(4)];
                                    var max3 = orderList[orderList.Keys.ElementAt(3)];
                                    orderList[Rule2CompanyId] = rnd.Next(min, max3);
                                }
                                break;
                            default:
                                break;
                        }
                    }
                }

                orderList = orderList.OrderByDescending(kv => kv.Value).ToDictionary(kv => kv.Key, kv => kv.Value);
                console?.WriteLine("排序完成，开始写入数据库");

                oracle.ExecuteUpdateSql("truncate table SurveyCompanyOrder");
                var bar3 = console?.WriteProgressBar();
                if (orderList.Any()) {
                    foreach (var kv in orderList.WithProgress(bar3)) {
                        oracle.ExecuteUpdateSql("Insert Into SurveyCompanyOrder Values(:1,:2)"
                            , new OracleParameter(":1", OracleDbType.Varchar2) { Value = kv.Key }
                            , new OracleParameter(":2", OracleDbType.Varchar2) { Value = kv.Value }
                        );
                    }
                }

                console?.WriteLine("入库完成");


            }
        }

        /// <summary>
        /// 更新测绘单位的业务范围
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="console"></param>
        [DisplayName("更新测绘单位业务范围")]
        public static void UpdateSurveyCompanyBusinessClass(string companyId, PerformContext console) {
            if (string.IsNullOrWhiteSpace(companyId)) {
                return;
            }

            using (var oracle = new OracleDataService()) {

                var qualification = oracle.GetById<CompanyQualification>(companyId);
                if (qualification == null) {
                    console?.WriteLine("无法获取到单位的测绘资质信息");
                    return;
                }

                console?.WriteLine("多测合一业务范围：");
                console?.WriteLine(qualification.BusinessRange);
                var businessClass = new List<string>();
                if (qualification.BusinessRange.StartsWith("[[")) {
                    console?.WriteLine("业务范围值需要转换，开始转换...");
                    var typeList = JsonConvert.DeserializeObject<List<List<string>>>(qualification.BusinessRange);

                    var flows = BusinessFlowConfig.AllFlows;
                    foreach (var type in typeList) {
                        var name = type.Last();
                        if (flows.Any(flow => flow.FlowName == name)) {
                            var flowObj = flows.First(flow => flow.FlowName == name);
                            var className = flowObj.GetType().Name;
                            console?.WriteLine(className);
                            businessClass.Add(className);
                        }
                    }

                    qualification.BusinessRange = JsonConvert.SerializeObject(businessClass);
                    oracle.UpdateOne(qualification);
                    console?.WriteLine("转换完成");
                }
                else {
                    businessClass = JsonConvert.DeserializeObject<List<string>>(qualification.BusinessRange);
                }

                oracle.ExecuteUpdateSql("Delete From SurveyCompanyBusinessClass Where SurveyCompanyID=:id",
                    new OracleParameter(":id", OracleDbType.Varchar2) { Value = companyId });
                foreach (var className in businessClass) {
                    oracle.ExecuteUpdateSql("Insert Into SurveyCompanyBusinessClass Values(:1,:2)"
                        , new OracleParameter(":1", OracleDbType.Varchar2) { Value = companyId }
                        , new OracleParameter(":2", OracleDbType.Varchar2) { Value = className }
                    );
                }

                console?.WriteLine("更新完成");
            }
        }

        /// <summary>
        /// 为新增的单位添加新的排序
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="console"></param>
        [DisplayName("为新增的单位添加新的排序")]
        public static void AddNewSurveyCompanyOrder(string companyId, PerformContext console) {
            using (var oracle = new OracleDataService()) {
                var dt = oracle.ExecuteQuerySql("Select ListOrder From SurveyCompanyOrder Where SurveyCompanyId=:id",
                    new OracleParameter(":id", OracleDbType.Varchar2) { Value = companyId });
                if (dt.Rows.Count == 0) {
                    var qualification = oracle.GetById<CompanyQualification>(companyId);
                    var listOrder = 0;
                    if (qualification != null) {
                        //随机数生成器
                        var rnd = new Random();
                        switch (qualification.QualificationLevel) {
                            case "甲":
                                listOrder = 4000 + rnd.Next(0, 500);
                                break;
                            case "乙":
                                listOrder = 3000 + rnd.Next(0, 500);
                                break;
                            case "丙":
                                listOrder = 2000 + rnd.Next(0, 500);
                                break;
                            case "丁":
                                listOrder = 1000 + rnd.Next(0, 500);
                                break;
                            default:
                                listOrder = 0 + rnd.Next(0, 500);
                                break;
                        }

                        oracle.ExecuteUpdateSql("Insert Into SurveyCompanyOrder Values(:1,:2)"
                            , new OracleParameter(":1", OracleDbType.Varchar2) { Value = companyId }
                            , new OracleParameter(":2", OracleDbType.Varchar2) { Value = listOrder }
                        );
                    }
                }
                else {
                    console?.WriteLine("测绘单位已进入排序");
                }
            }
        }

        /// <summary>
        /// 检测注册测绘师授权是否过期
        /// </summary>
        [DisplayName("检测注册测绘师授权是否过期")]
        public static void CheckSurveyMasterAuthExpired(PerformContext console) {
            using (var oracle = new OracleDataService()) {
                var expireList = oracle.GetList<SurveyMasterAuth>("StateCode=1 And AuthEndDate<SYSDATE");
                if (expireList.Any()) {
                    console?.WriteLine($"有{expireList.Count}条记录已过期");
                    foreach (var auth in expireList) {
                        auth.StateCode = 2;
                        oracle.UpdateOne(auth);
                    }
                }
            }
        }
    }
}
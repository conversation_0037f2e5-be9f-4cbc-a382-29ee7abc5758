﻿using Newtonsoft.Json;
using SDCPCWeb.Services;

namespace SDCPCWeb.Models.BusinessFlow {
    public class BusinessLinkToSignModel {
        /// <summary>
        /// 主键ID
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 活动环节ID
        /// </summary>
        public string BusinessLinkInfoID { get; set; }
        /// <summary>
        /// 角色代码
        /// </summary>
        public int RoleCode { get; set; }
    }
    public class BusinessLinkToSign : BusinessLinkToSignModel, IOracleDataTable {
        public BusinessLinkToSignModel ToModel() {
            return JsonConvert.DeserializeObject<BusinessLinkToSignModel>(JsonConvert.SerializeObject(this));
        }
    }
}
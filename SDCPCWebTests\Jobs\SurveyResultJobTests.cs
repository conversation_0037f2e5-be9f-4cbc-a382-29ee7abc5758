﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using SDCPCWeb.Jobs;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SDCPCWeb.Models.Result;
using SDCPCWeb.Models.System;
using System.Web;

namespace SDCPCWeb.Jobs.Tests {
    [TestClass()]
    public class SurveyResultJobTests {
        [TestMethod()]
        public void CreateValidateNumberTest() {
            Console.WriteLine(SurveyResultJob.CreateValidateNumber(4));
            Console.WriteLine(SurveyResultJob.CreateValidateNumber(4));
            Console.WriteLine(SurveyResultJob.CreateValidateNumber(4));
            Console.WriteLine(SurveyResultJob.CreateValidateNumber(4));
            Console.WriteLine(SurveyResultJob.CreateValidateNumber(4));
            Console.WriteLine(SurveyResultJob.CreateValidateNumber(4));
        }

        /// <summary>
        /// 测绘数据申请检查测试
        /// </summary>
        [TestMethod()]
        public async Task PostProjectScopeResultTest()
        {
            var txt = File.ReadAllText("G:\\基础数据.txt");
            string result2 = txt.Trim();
            result2 = result2.Replace("\r\n", ",").Trim();
            result2 = result2.Replace("\n", ",").Trim();

            var servicesUrl = "http://172.16.3.184/EPSSDCServices/EPSSDCServices.asmx/CheckProjectArea";
            string par = $"ID=c5c61f3905df49e1a61597929382f00a&strYWMC={HttpUtility.UrlEncode("测绘数据申请")}&strCOORDINATE={LongUrlEncode(result2)}";

            var httpClient = new HttpClient();

            using (StringContent content = new StringContent(par, Encoding.UTF8, "application/x-www-form-urlencoded"))
            {
                var responseMessage = await httpClient.PostAsync(servicesUrl, content);
                if (responseMessage.IsSuccessStatusCode)
                {
                    var responseTxt = await responseMessage.Content.ReadAsStringAsync();
                }
            }



            string responseString = "";
            byte[] bytes = Encoding.UTF8.GetBytes(par);
            HttpWebRequest request = WebRequest.Create(servicesUrl) as HttpWebRequest;
            request.Method = "POST";
            request.ContentType = "application/x-www-form-urlencoded";
            request.ContentLength = (long)bytes.Length;
            using (Stream requestStream = request.GetRequestStream()) {
                requestStream.Write(bytes, 0, bytes.Length);
                requestStream.Flush();
                requestStream.Close();
            }
            WebResponse rs = request.GetResponse();
            using (HttpWebResponse response = request.GetResponse() as HttpWebResponse) {
                StreamReader reader = new StreamReader(response.GetResponseStream());
                responseString = reader.ReadToEnd();
                reader.Close();
            }
            System.Xml.XmlDocument xml = new System.Xml.XmlDocument();
            xml.LoadXml(responseString);
            JObject result = (JObject)JsonConvert.DeserializeObject(xml.InnerText);
            string ProjectAreaStatus = result["PROJECTAREASTATUS"]?.Value<string>();
            string ProjectAreaError = result["PROJECTAREAERROR"]?.Value<string>();
            string resultStatus = result["status"]?.Value<string>();
            if (resultStatus != "success") {
                
            }
        }

        private string LongUrlEncode(string toEncodeString) {
            const int limit = 4000;
            var sb = new StringBuilder();
            var loops = toEncodeString.Length / limit;
            for (int i = 0; i <= loops; i++) {
                sb.Append(Uri.EscapeDataString(i < loops
                    ? toEncodeString.Substring(limit * i, limit)
                    : toEncodeString.Substring(limit * i)));
            }

            return sb.ToString();
        }
    }
}
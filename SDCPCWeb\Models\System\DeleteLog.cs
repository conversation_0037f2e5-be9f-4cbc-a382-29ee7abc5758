﻿#region 文件信息描述
// /* ===============================================
// * 功能描述：
// * 创 建 人：吕俊宏
// * 创建日期：2021年01月24日 13:50
// * 项目名称：SDCPCWeb
// * 文件名称：DeleteLog.cs
// * 创建用户名：Lvjunhong
// * ================================================*/
#endregion

using System;
using SDCPCWeb.Services;

namespace SDCPCWeb.Models.System {
    /// <summary>
    /// 删除记录
    /// </summary>
    public class DeleteLogModel {
        /// <summary>
        /// ID
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 删除者
        /// </summary>
        public string DeleteUser { get; set; }
        /// <summary>
        /// 删除记录创建时间（即删除时间）
        /// </summary>
        public DateTime CreateTime { get; set; }
        /// <summary>
        /// 删除的记录所在的表名称
        /// </summary>
        public string DeleteTableName { get; set; }
        /// <summary>
        /// 删除记录的完整内容
        /// </summary>
        [CLOB]
        public string RecordContent { get; set; }
        /// <summary>
        /// 删除原因
        /// </summary>
        public string DeleteReason { get; set; }
    }

    public class DeleteLog : DeleteLogModel, IOracleDataTable {

    }
}
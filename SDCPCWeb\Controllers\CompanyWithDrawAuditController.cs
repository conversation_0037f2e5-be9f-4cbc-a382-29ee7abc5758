﻿using SDCPCWeb.Extensions;
using SDCPCWeb.Models;
using SDCPCWeb.Models.CompanyRegister;
using SDCPCWeb.Models.Result;
using SDCPCWeb.Services;
using System;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using log4net.Util;
using Oracle.ManagedDataAccess.Client;
using SDCPCWeb.Models.BusinessFlow;

namespace SDCPCWeb.Controllers {
    /// <summary>
    /// 测绘单位注销名录库审核
    /// </summary>
    [RoutePrefix("internal/CompanyWithDrawAudit"), SDCAuthorize(SDCAuthorizeRole.xCloud)]
    public class CompanyWithDrawAuditController : ApiController {
        private readonly OracleDataService service = new OracleDataService();
        private UserInfo UserInfo => Request.Properties.ContainsKey("SDC-UserInfo") ? (UserInfo)Request.Properties["SDC-UserInfo"] : null;

        /// <summary>
        /// 判断是否达到注销条件
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("CheckCanWithDraw")]
        public async Task<ApiResult> CheckCanWithDraw(string companyId) {
            var company = service.GetById<CompanyBaseInfo>(companyId);
            if (company == null) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "单位不存在"
                };
            }

            if (company.CompanyType != "测绘单位") {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "该单位类型不是测绘单位，不允许注销"
                };
            }

            //判断是否达到注销条件
            var checkResult = CompanyWithDrawController.CheckCanWithDraw(company.ID, company.CompanyName,
                company.CreditCode);
            if (checkResult.Item1 == false) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = checkResult.Item2
                };
            }
            else {
                return new ApiResult() {
                    StateCode = 1
                };
            }

        }

        /// <summary>
        /// 获取测绘单位注销名录库申请列表
        /// </summary>
        /// <param name="stateCode"></param>
        /// <param name="companyName"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpGet, Route("GetCompanyWithDrawAuditList")]
        public ApiResult GetCompanyWithDrawAuditList(int stateCode = 1, string companyName = "", int pageIndex = 1, int pageSize = 20) {
            if (companyName.Contains("'")) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }

            //1是已提交待审核
            //2是审核中
            if (!new[] { 1, 2 }.Contains(stateCode)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误，StateCode的值必须是1或者2" };
            }
            string where = $"StateCode={stateCode}";
            string countWhere = "";
            if (!string.IsNullOrEmpty(companyName)) {
                where += " and CompanyName like '%" + companyName + "%'";
                countWhere += " and CompanyName like '%" + companyName + "%'";
            }

            try {
                var items = service.GetPagerList<SurveyCompanyWithDraw>(pageIndex, pageSize, where, "CreateTime");
                dynamic CountInfo = new {
                    Total = service.GetList<SurveyCompanyWithDraw>($"StateCode={stateCode}" + countWhere + "").Count
                };
                dynamic PageInfo = new {
                    PageIndex = pageIndex,
                    PageSize = pageSize,
                    Total = service.GetList<SurveyCompanyWithDraw>(where).Count
                };
                dynamic deObj = new {
                    DataTable = items.Select(r => r.ToModel()),
                    Count = CountInfo,
                    Page = PageInfo
                };
                return new ApiResult { StateCode = 1, Data = deObj, Message = "" };
            }
            catch (Exception e) {
                //记录操作日志
                HttpRequestBase req = ((HttpContextBase)Request.Properties["MS_HttpContext"]).Request;
                var reqInfoHelper = new HttpRequestInfoHelper(req);
                LogService.WriteLogs(Request, "获取测绘单位注销申请异常", $"异常消息：{e.GetStackTraces()}", reqInfoHelper);

                return new ApiResult { StateCode = 0, Message = "获取失败，错误信息：" + e.Message + "" };
            }
        }

        /// <summary>
        /// 单位注销名录库申请已完成（云平台主动注销）
        /// </summary>
        /// <param name="companyId">测绘单位id</param>
        /// <returns></returns>
        [HttpPost, Route("CompleteCompanyWithDrawByXCloudFlow")]
        public ApiResult CompleteCompanyWithDrawByXCloudFlow(dynamic input) {
            string businessId = input?.businessId?.ToString();
            string createUserId = input?.createUserId?.ToString();
            string createPersonName = input?.createPersonName?.ToString();
            string companyId = input?.companyId?.ToString();
            string reasonStr = input?.reason?.ToString();
            string otherReason = input?.otherReason?.ToString();

            if (string.IsNullOrWhiteSpace(businessId)) {
                return new ApiResult() { StateCode = 0, Message = "业务id参数无效" };
            }

            if (string.IsNullOrWhiteSpace(createUserId) || string.IsNullOrWhiteSpace(createPersonName)) {
                return new ApiResult() { StateCode = 0, Message = "创建人参数无效" };
            }

            if (string.IsNullOrWhiteSpace(reasonStr)) {
                return new ApiResult() { StateCode = 0, Message = "注销理由参数无效" };
            }

            int reason;
            if (int.TryParse(reasonStr, out reason) == false) {
                return new ApiResult() { StateCode = 0, Message = "注销理由参数无效" };
            }

            if (new int[] {1, 2, 3}.Contains(reason) == false) {
                return new ApiResult() { StateCode = 0, Message = "注销理由参数无效" };
            }

            if (reason == 3 && string.IsNullOrWhiteSpace(otherReason)) {
                return new ApiResult() { StateCode = 0, Message = "其他理由参数无效" };
            }

            var company = service.GetById<CompanyBaseInfo>(companyId);
            if (company == null) {
                return new ApiResult() { StateCode = 0, Message = "找不到测绘单位信息" };
            }

            if (company.CompanyType != "测绘单位") {
                return new ApiResult() { StateCode = 0, Message = "该单位类型不是测绘单位" };
            }


            //获取审核人信息
            string auditor = UserInfo.PersonName;
            if (string.IsNullOrWhiteSpace(auditor)) {
                return new ApiResult() { StateCode = 0, Message = "您不具备进行此操作的权限" };
            }

            /* 依法注销不需要判断
            //判断是否达到注销条件
            var checkResult = CompanyWithDrawController.CheckCanWithDraw(company.ID, company.CompanyName,
                company.CreditCode);
            if (checkResult.Item1 == false) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = checkResult.Item2
                };
            }
            */

            HttpRequestBase req = ((HttpContextBase)Request.Properties["MS_HttpContext"]).Request;
            var reqInfoHelper = new HttpRequestInfoHelper(req);

            try {
                //删除测绘数据单位
                CompanyWithDrawController.SurveyCompanyWithDraw(company.ID, createUserId, createPersonName, reason, otherReason, true, businessId);

                LogService.WriteLogs(Request, "测绘单位依法注销", $"测绘单位：{company.CompanyName}", reqInfoHelper);
            }
            catch (Exception e) {
                //记录操作日志
                LogService.WriteLogs(Request, "测绘单位依法注销异常", $"测绘单位：{company.CompanyName} >> 异常消息：{e.GetStackTraces()}", reqInfoHelper);

                return new ApiResult { StateCode = 0, Message = "提交失败，请稍后再试，错误信息：" + e.Message + "" };
            }

            return new ApiResult() {
                StateCode = 1,
                Message = "注销成功"
            };
        }

        #region 内部方法

        
        #endregion
    }
}

﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using Newtonsoft.Json;
using Oracle.ManagedDataAccess.Client;
using SDCPCWeb.Extensions;
using SDCPCWeb.Models;
using SDCPCWeb.Models.BusinessFlow;
using SDCPCWeb.Models.CompanyRegister;
using SDCPCWeb.Models.Result;
using SDCPCWeb.Services;

namespace SDCPCWeb.Controllers {
    /// <summary>
    /// 测绘单位注销名录库申请
    /// </summary>
    [RoutePrefix("sdcapi/CompanyWithDraw"), BDCAuthorize]
    public class CompanyWithDrawController : ApiController {
        private readonly OracleDataService service = new OracleDataService();
        private UserInfo UserInfo => (UserInfo)Request.Properties["SDC-UserInfo"];

        /// <summary>
        /// 判断是否达到注销条件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost, Route("CheckCanWithDraw")]
        public async Task<ApiResult> CheckCanWithDraw() {
            var company = UserInfo.GetRelateSurveyCompany();
            if (company == null) {
                return new ApiResult { StateCode = 0, Message = "您不是测绘单位的人员" };
            }

            if (UserInfo.IsSDCCompanyAdmin(company.ID) == false) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "您不是测绘单位的单位管理员"
                };
            }

            //判断是否达到注销条件
            var checkResult = CheckCanWithDraw(company.ID, company.CompanyName,
                company.CreditCode);
            if (checkResult.Item1 == false) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = checkResult.Item2
                };
            }
            else {
                return new ApiResult() {
                    StateCode = 1
                };
            }

        }

        /// <summary>
        /// 创建注销测绘单位名录库申请接口
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost, Route("CreateSurveyCompanyWithDraw")]
        public async Task<ApiResult> CreateSurveyCompanyWithDraw(dynamic input) {
            string reasonStr = input?.reason?.ToString();
            string otherReason = input?.otherReason?.ToString();

            if (string.IsNullOrWhiteSpace(reasonStr)) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "注销理由参数无效"
                };
            }

            int reason;
            if (int.TryParse(reasonStr, out reason) == false) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "注销理由参数无效"
                };
            }

            if (new int[] {1, 2, 3}.Contains(reason) == false) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "注销理由参数无效"
                };
            }

            if (reason == 3 && string.IsNullOrWhiteSpace(otherReason)) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "其他理由参数无效"
                };
            }

            if (reason != 3) {
                otherReason = null;
            }

            var company = UserInfo.GetRelateSurveyCompany();
            if (company == null) {
                return new ApiResult { StateCode = 0, Message = "您不是测绘单位的人员" };
            }

            if (UserInfo.IsSDCCompanyAdmin(company.ID) == false) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "您不是测绘单位的单位管理员"
                };
            }

            //判断是否达到注销条件
            var checkResult = CheckCanWithDraw(company.ID, company.CompanyName,
                    company.CreditCode);
            if (checkResult.Item1 == false) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = checkResult.Item2
                };
            }


            HttpRequestBase req = ((HttpContextBase)Request.Properties["MS_HttpContext"]).Request;
            var reqInfoHelper = new HttpRequestInfoHelper(req);

            try {
                //删除测绘数据单位
                var withDrawModel = SurveyCompanyWithDraw(company.ID, UserInfo.UserId, UserInfo.PersonName, reason, otherReason);

                #region 通知云平台

                string zxly = "";
                switch (reason) {
                    case 1: {
                        zxly = "已注销测绘资质";
                        break;
                    }
                    case 2: {
                        zxly = "暂不参与多测合一测绘市场";
                        break;
                    }
                    default: {
                        zxly = "其他";
                        break;
                    }
                }
                Hangfire.BackgroundJob.Enqueue(() => XCloudService.CompanyWithDrawToXCloudLog(withDrawModel.ID, company.CompanyName, company.CreditCode, "测绘单位", UserInfo.PersonName, withDrawModel.CreateTime, UserInfo.PersonNo, zxly, otherReason, null, null));

                #endregion

                LogService.WriteLogs(Request, "测绘单位自主注销", $"测绘单位：{company.CompanyName}", reqInfoHelper);
            }
            catch (Exception e) {
                //记录操作日志
                LogService.WriteLogs(Request, "测绘单位自主注销异常", $"测绘单位：{company.CompanyName} >> 异常消息：{e.GetStackTraces()}", reqInfoHelper);

                return new ApiResult { StateCode = 0, Message = "提交失败，请稍后再试，错误信息：" + e.Message + "" };
            }

            return new ApiResult() {
                StateCode = 1,
                Message = "注销成功"
            };
        }

        /// <summary>
        /// 获取测绘单位注销名录库申请接口
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("GetSurveyCompanyWithDraw")]
        public ApiResult GetSurveyCompanyWithDraw() {

            var company = UserInfo.GetRelateSurveyCompany();
            if (company == null) {
                return new ApiResult { StateCode = 0, Message = "您不是测绘单位的人员" };
            }

            if (UserInfo.IsSDCCompanyAdmin(company.ID) == false) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "您不是测绘单位的单位管理员"
                };
            }

            var list = service.GetList<SurveyCompanyWithDraw>("CompanyId=:companyId", 
                new OracleParameter(":companyId", OracleDbType.Varchar2) { Value = company.ID }
            );

            return new ApiResult() {
                StateCode = 1,
                Data = new {
                    IsWithDraw = list.Any(),
                    WithDrawInfo = list.OrderByDescending(s => s.CreateTime).FirstOrDefault()
                }
            };
        }

        #region 公共方法

        /// <summary>
        /// 检查是否达到注销条件
        /// </summary>
        /// <param name="companyId">单位id</param>
        /// <param name="surveyCompanyName">单位名称</param>
        /// <param name="surveyCompanyNo">单位证件号</param>
        /// <returns></returns>
        public static Tuple<bool, string> CheckCanWithDraw(string companyId, string surveyCompanyName, string surveyCompanyNo) {
            using (OracleDataService service = new OracleDataService()) {

                //查是否有在办业务
                var list = service.GetList<BusinessBaseInfo>(
                    $"(SurveyCompanyName=:SurveyCompanyName OR SurveyCompanyNo=:SurveyCompanyNo OR CREATECOMPANYNO=:SurveyCompanyNo) AND StateCode in (1, 3)", new OracleParameter[] {
                        new OracleParameter(":SurveyCompanyName", OracleDbType.Varchar2){ Value = surveyCompanyName },
                        new OracleParameter(":SurveyCompanyNo", OracleDbType.Varchar2){ Value = surveyCompanyNo },
                    });

                if (list.Any()) {
                    return new Tuple<bool, string>(false, $"单位下还有{list.Count}条业务在办理中，不能注销");
                }

                //查是否有在办变更申请
                var companyInfoModifyRequests = service.GetList<CompanyInfoModifyRequest>(
                    $"CompanyId=:CompanyId AND StateCode in (1, 2)", new OracleParameter[] {
                        new OracleParameter(":CompanyId", OracleDbType.Varchar2){ Value = companyId }
                    });
                if (companyInfoModifyRequests.Any()) {
                    return new Tuple<bool, string>(false, $"单位下还有变更申请在办理中，不能注销");
                }

                return new Tuple<bool, string>(true, null);
            }
        }


        /// <summary>
        /// 执行删除测绘单位相关数据表的数据操作
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="userId"></param>
        /// <param name="personName"></param>
        /// <param name="reason"></param>
        /// <param name="otherReason"></param>
        /// <param name="isFromXCloud"></param>
        /// <param name="xCloudBusinessId">云平台业务id</param>
        public static SurveyCompanyWithDraw SurveyCompanyWithDraw(string companyId, string userId, string personName, int reason, string otherReason, bool? isFromXCloud = false, string xCloudBusinessId = null) {
            using (OracleDataService service = new OracleDataService()) {

                var companyInfo = service.GetById<CompanyBaseInfo>(companyId);
                var companyEmployees = service.GetList<CompanyEmployees>("RelationRequestId=:companyId",
                    new OracleParameter(":companyId", OracleDbType.Varchar2) {Value = companyInfo.ID});
                var companyQualification = service.GetList<CompanyQualification>("RelationRequestId=:companyId",
                    new OracleParameter(":companyId", OracleDbType.Varchar2) {Value = companyInfo.ID});

                //获取未完成授权的注册测绘师
                var surveyMasterAuths = service.GetList<SurveyMasterAuth>("CompanyID=:companyId AND StateCode=0",
                    new OracleParameter(":companyId", OracleDbType.Varchar2) {Value = companyInfo.ID});

                string[] surveyMasterAuthIds = surveyMasterAuths.Select(s => s.ID).ToArray();

                List<SurveyMasterAuthClass> surveyMasterAuthClasses = new List<SurveyMasterAuthClass>();
                if (surveyMasterAuthIds.Any()) {
                    surveyMasterAuthClasses = service.GetList<SurveyMasterAuthClass>($"AUTHID in ('{string.Join("','", surveyMasterAuthIds)}')");
                }

                List<dynamic> surveyCompanyBusinessClasses = new List<dynamic>();
                var dtSurveyCompanyBusinessClass = service.ExecuteQuerySql("select SURVEYCOMPANYID, BUSINESSCLASS from SURVEYCOMPANYBUSINESSCLASS where SURVEYCOMPANYID=:companyId", new OracleParameter(":companyId", OracleDbType.Varchar2){ Value = companyInfo.ID });
                foreach (DataRow dataRow in dtSurveyCompanyBusinessClass.Rows) {
                    surveyCompanyBusinessClasses.Add(new { SURVEYCOMPANYID = dataRow["SURVEYCOMPANYID"].ToString(), BUSINESSCLASS = dataRow["BUSINESSCLASS"].ToString() });
                }

                List<dynamic> surveyCompanyCredits = new List<dynamic>();
                var dtSurveyCompanyCredit = service.ExecuteQuerySql("select SURVEYCOMPANYID, CREDITLEVEL, CREDITNAME from SURVEYCOMPANYCREDIT where SURVEYCOMPANYID=:companyId", new OracleParameter(":companyId", OracleDbType.Varchar2){ Value = companyInfo.ID });
                foreach (DataRow dataRow in dtSurveyCompanyCredit.Rows) {
                    surveyCompanyCredits.Add(new { SURVEYCOMPANYID = dataRow["SURVEYCOMPANYID"].ToString(), CREDITLEVEL = Convert.ToInt32(dataRow["CREDITLEVEL"].ToString()), CREDITNAME = dataRow["CREDITNAME"].ToString() });
                }

                List<dynamic> surveyCompanyOrders = new List<dynamic>();
                var dtSurveyCompanyOrder = service.ExecuteQuerySql("select SURVEYCOMPANYID, LISTORDER from SURVEYCOMPANYORDER where SURVEYCOMPANYID=:companyId", new OracleParameter(":companyId", OracleDbType.Varchar2){ Value = companyInfo.ID });
                foreach (DataRow dataRow in dtSurveyCompanyOrder.Rows) {
                    surveyCompanyOrders.Add(new { SURVEYCOMPANYID = dataRow["SURVEYCOMPANYID"].ToString(), LISTORDER = Convert.ToInt32(dataRow["LISTORDER"].ToString()) });
                }

                //进行注销
                //写入注销表
                SurveyCompanyWithDraw model = new SurveyCompanyWithDraw() {
                    ID = Guid.NewGuid().ToString("N"),
                    CreateTime = DateTime.Now,
                    CreateUserId = userId,
                    CreatePersonName = personName,
                    CompanyId = companyInfo.ID,
                    CompanyName = companyInfo.CompanyName,
                    Reason = reason,
                    OtherReason = otherReason,
                    StateCode = 3,
                    IsFromXCloud = isFromXCloud == true ? 1 : 0,
                    XCloudBusinessId = isFromXCloud == true ? xCloudBusinessId : null,
                    CompanyInfo = JsonConvert.SerializeObject(new {companyInfo, companyEmployees, companyQualification, surveyMasterAuths, surveyMasterAuthClasses, surveyCompanyBusinessClasses, surveyCompanyCredits, surveyCompanyOrders })
                };

                service.InsertOne(model);

                //删除测绘单位相关信息
                service.DeleteOne(companyInfo);
                service.DeleteMany<CompanyEmployees>("RelationRequestId=:companyId", new OracleParameter(":companyId", OracleDbType.Varchar2){ Value = companyInfo.ID });
                service.DeleteMany<CompanyQualification>("RelationRequestId=:companyId", new OracleParameter(":companyId", OracleDbType.Varchar2){ Value = companyInfo.ID });
                service.DeleteMany<SurveyMasterAuth>("CompanyID=:companyId AND StateCode=0",
                    new OracleParameter(":companyId", OracleDbType.Varchar2) {Value = companyInfo.ID});
                service.ExecuteUpdateSql("delete SURVEYMASTERAUTHCLASS where ID in (select B.ID from SURVEYMASTERAUTH A INNER JOIN SURVEYMASTERAUTHCLASS B on A.ID = B.AUTHID where A.COMPANYID = :companyId AND A.StateCode=0)", new OracleParameter(":companyId", OracleDbType.Varchar2){ Value = companyInfo.ID });

                service.ExecuteUpdateSql("delete SURVEYCOMPANYBUSINESSCLASS where SURVEYCOMPANYID=:companyId", new OracleParameter(":companyId", OracleDbType.Varchar2){ Value = companyInfo.ID });
                service.ExecuteUpdateSql("delete SURVEYCOMPANYCREDIT where SURVEYCOMPANYID=:companyId", new OracleParameter(":companyId", OracleDbType.Varchar2){ Value = companyInfo.ID });
                service.ExecuteUpdateSql("delete SURVEYCOMPANYORDER where SURVEYCOMPANYID=:companyId", new OracleParameter(":companyId", OracleDbType.Varchar2){ Value = companyInfo.ID });

                return model;
            }
        }

        #endregion
    }
}

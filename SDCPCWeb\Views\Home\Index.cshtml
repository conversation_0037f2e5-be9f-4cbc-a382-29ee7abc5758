﻿@{
    Layout = null;
}
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>@ViewBag.Title</title>
    @Styles.Render("~/Content/css")
    @Scripts.Render("~/bundles/modernizr")
</head>
<body>
    <div class="container body-content">
        <div class="jumbotron">
            <h1 style="font-size:52px ">@ViewBag.Title</h1>
            <p class="lead">@ViewBag.TestMessage</p>
            <p>
                <a href="~/sdcapi/hangfire" class="btn btn-primary btn-lg">进入Hangfire作业池 »</a>
                <a href="~/doc/index" class="btn btn-primary btn-lg">查看接口文档 »</a>
            </p>
        </div>

        <hr>
        <footer>
            <p>© @(DateTime.Now.Year) - 南宁市自然资源信息集团有限公司</p>
        </footer>
    </div>
    @Scripts.Render("~/bundles/jquery")
    @Scripts.Render("~/bundles/bootstrap")
</body>
</html>
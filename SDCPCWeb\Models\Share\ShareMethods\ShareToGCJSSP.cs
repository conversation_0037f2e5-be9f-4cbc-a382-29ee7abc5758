﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Web;
using System.Web.Configuration;
using Oracle.ManagedDataAccess.Client;
using SDCPCWeb.Models.Attachment;
using SDCPCWeb.Models.BusinessFlow;
using SDCPCWeb.Models.Result;
using SDCPCWeb.Models.System;

namespace SDCPCWeb.Models.Share {
    public partial class ShareMethods {
        #region 共享给工程建设审批系统的共享方法集

        /// <summary>
        /// 查询拨地定桩的业务列表
        /// </summary>
        /// <param name="arguments"></param>
        /// <returns></returns>
        private ApiResult GetMarkPointProjectList(object arguments) {
            var args = ArgumentToObject<GetMarkPointProjectListInput>(arguments);

            if (string.IsNullOrWhiteSpace(args.ProjectName) && string.IsNullOrWhiteSpace(args.ProjectNo)) {
                return new ApiResult() { StateCode = 0, Message = "请输入参数" };
            }

            //查询已完成拨地定桩的业务列表

            var parameters = new List<OracleParameter>();
            var mainSql =
                "SELECT BASE.ID,BASE.BUSINESSNUMBER \"BusinessNumber\",BASE.BUSINESSNAME \"BusinessName\",BASE.BUSINESSTYPE \"BusinessType\",BASE.CREATETIME \"CreateTime\",BASE.FINISHTIME \"FinishTime\"\n" +
                ",BASE.DEVELOPERNAME \"DeveloperName\",BASE.SURVEYCOMPANYNAME \"SurveyCompanyName\"\n" +
                ",CONT.PROJECTNAME \"ProjectName\",CONT.PROJECTNUMBER \"ProjectNo\"\n" +
                "FROM BUSINESSBASEINFO BASE\n" +
                "INNER JOIN MARKPOINTSURVEYCONTENTINFO CONT ON BASE.ID=CONT.ID\n";
            var where = "WHERE BASE.STATECODE=2";

            if (!string.IsNullOrWhiteSpace(args.ProjectName)) {
                where += " AND CONT.PROJECTNAME LIKE :pname";
                parameters.Add(new OracleParameter(":pname", OracleDbType.Varchar2) { Value = $"%{args.ProjectName}%" });
            }

            if (!string.IsNullOrWhiteSpace(args.ProjectNo)) {
                where += " AND CONT.PROJECTNUMBER LIKE :pno";
                parameters.Add(new OracleParameter(":pno", OracleDbType.Varchar2) { Value = $"%{args.ProjectNo}%" });
            }

            var dt = oracle.GetListWithSql<object>(mainSql + where, "6", parameters.ToArray());

            return new ApiResult() { StateCode = 1, Data = dt };
        }

        /// <summary>
        /// 根据业务ID获取完整的业务信息
        /// </summary>
        /// <param name="arguments"></param>
        /// <returns></returns>
        private ApiResult GetMarkPointProjectInfo(object arguments) {
            var args = ArgumentToObject<GetMarkPointProjectInfoInnput>(arguments);

            if (string.IsNullOrWhiteSpace(args.ID)) {
                return new ApiResult() { StateCode = 0, Message = "请输入参数" };
            }

            var project = MarkPointSurveyProject.GetByBusinessID(args.ID);
            if (project.BaseInfo?.StateCode != 2) {
                return new ApiResult() { StateCode = 0, Message = "项目信息未找到或项目状态未完成" };
            }

            if (project.FlowInfo.GetType().Name != project.BaseInfo.BusinessClass) {
                return new ApiResult() { StateCode = 0, Message = "该业务不属于拨地定桩类型的多测合一业务" };
            }

            return new ApiResult() {
                StateCode = 1,
                Data = new {
                    BaseInfo = new {
                        project.BaseInfo.ID,
                        project.BaseInfo.BusinessNumber,
                        project.BaseInfo.BusinessName,
                        project.BaseInfo.BusinessType,
                        project.BaseInfo.CreatePersonName,
                        project.BaseInfo.DeveloperName,
                        project.BaseInfo.SurveyCompanyName,
                        project.BaseInfo.CreateTime,
                        project.BaseInfo.FinishTime,
                        project.BaseInfo.StateCode,
                    },
                    ContentInfo = new {
                        project.ContentInfo.ProjectNumber,
                        project.ContentInfo.ProjectName,
                        project.ContentInfo.ProjectAddress,
                    },
                    Attachments = project.Attachments.Select(a => new {
                        a.ID,
                        a.AttachmentCategories,
                        a.AttachmentType,
                        a.AttachmentName
                    })
                }
            };
        }

        /// <summary>
        /// 根据业务ID获取完整的业务信息
        /// </summary>
        /// <param name="arguments"></param>
        /// <returns></returns>
        private ApiResult GetAttachment(object arguments) {
            var args = ArgumentToObject<GetMarkPointProjectInfoInnput>(arguments);

            if (string.IsNullOrWhiteSpace(args.ID)) {
                return new ApiResult() { StateCode = 0, Message = "请输入参数" };
            }

            var attachment = oracle.GetById<AttachmentInfo>(args.ID);
            if (attachment?.StateCode != 0) {
                return new ApiResult() { StateCode = 0, Message = "附件信息未找到" };
            }

            //获取附件
            try {
                string rootPath = WebConfigurationManager.AppSettings["RootPath"];
                var fileData = File.ReadAllBytes(rootPath + "\\" + attachment.AttachmentPath);
                //获取原始文件名称
                return new ApiResult() {
                    StateCode = 1,
                    Data = new {
                        FileName = attachment.AttachmentName,
                        FileExt = attachment.AttachmentExt,
                        FileData = Convert.ToBase64String(fileData)
                    }
                };
            }
            catch (Exception e) {
                return new ApiResult() { StateCode = 0, Message = e.Message };
            }
        }

        /// <summary>
        /// 查询实核业务的业务列表
        /// </summary>
        /// <param name="arguments"></param>
        /// <returns></returns>
        private ApiResult GetEstateActualSurveyProjectList(object arguments) {
            var args = ArgumentToObject<GetEstateActualSurveyProjectListInput>(arguments);

            if (string.IsNullOrWhiteSpace(args.ProjectName) && string.IsNullOrWhiteSpace(args.BusinessNumber)) {
                return new ApiResult() { StateCode = 0, Message = "请输入参数" };
            }

            //查询不动产实核业务和不动产实核成果变更业务列表

            var parameters = new List<OracleParameter>();
            var mainSql =
                "SELECT BASE.ID,BASE.BUSINESSNUMBER \"BusinessNumber\",BASE.BUSINESSNAME \"BusinessName\"\n" +
                ",BASE.BUSINESSTYPE \"BusinessType\",BASE.CREATETIME \"CreateTime\",BASE.FINISHTIME \"FinishTime\"\n" +
                ",BASE.DEVELOPERNAME \"DeveloperName\",BASE.CREATEPERSONNAME \"CreatePersonName\",BASE.CREATEPERSONPHONE \"CreatePersonPhone\"" +
                ",BASE.SURVEYCOMPANYNAME \"SurveyCompanyName\",BASE.STATECODE \"StateCode\"\n" +
                ",(SELECT LINKNAME FROM BUSINESSLINKINFO WHERE BUSINESSID=BASE.ID AND STATECODE IN(0,1,3) AND ROWNUM=1) \"CurrentActionName\"\n" +
                "FROM BUSINESSBASEINFO BASE\n";
            var where = "WHERE (BASE.BUSINESSCLASS='RealEstateActualSurveyFlow' OR BASE.BUSINESSCLASS='RealEstateActualResultChangeFlow') AND BASE.STATECODE!=4";

            if (!string.IsNullOrWhiteSpace(args.BusinessNumber)) {
                where += " AND BASE.BUSINESSNUMBER = :pno";
                parameters.Add(new OracleParameter(":pno", OracleDbType.Varchar2) { Value = $"{args.BusinessNumber}" });
            }

            if (!string.IsNullOrWhiteSpace(args.ProjectName)) {
                where += " AND BASE.BUSINESSNAME LIKE :pname";
                parameters.Add(new OracleParameter(":pname", OracleDbType.Varchar2) { Value = $"%{args.ProjectName}%" });
            }

            var dt = oracle.GetListWithSql<GetEstateActualSurveyProjectListItem>(mainSql + where, "6", parameters.ToArray());

            return new ApiResult() { StateCode = 1, Data = dt };
        }

        /// <summary>
        /// 根据业务ID获取完整的实核业务信息
        /// </summary>
        /// <param name="arguments"></param>
        /// <returns></returns>
        private ApiResult GetEstateActualSurveyProjectInfo(object arguments) {
            var args = ArgumentToObject<GetMarkPointProjectInfoInnput>(arguments);

            if (string.IsNullOrWhiteSpace(args.ID)) {
                return new ApiResult() { StateCode = 0, Message = "请输入参数" };
            }

            var project = RealEstateActualSurveyProject.GetByBusinessID(args.ID);
            if (project.BaseInfo?.StateCode != 2) {
                return new ApiResult() { StateCode = 0, Message = "项目信息未找到或项目状态未完成" };
            }
            if (project.FlowInfo.GetType().Name != project.BaseInfo.BusinessClass) {
                return new ApiResult() { StateCode = 0, Message = "该业务不属于多测合一不动产实核业务" };
            }
            return new ApiResult() {
                StateCode = 1,
                Data = new {
                    BaseInfo = new {
                        project.BaseInfo.ID,
                        project.BaseInfo.BusinessNumber,
                        project.BaseInfo.BusinessName,
                        project.BaseInfo.BusinessType,
                        project.BaseInfo.CreatePersonName,
                        project.BaseInfo.CreatePersonPhone,
                        project.BaseInfo.DeveloperName,
                        project.BaseInfo.SurveyCompanyName,
                        project.BaseInfo.CreateTime,
                        project.BaseInfo.FinishTime,
                        project.BaseInfo.StateCode,
                        CurrentActionName = project.CurrentAction.LinkName
                    },
                    ContentInfo = default(object),
                    //    new {
                    //    project.ContentInfo?.ProjectPlanPermission,
                    //    project.ContentInfo?.BuildingTableInfo,
                    //    project.ContentInfo?.ProjectResultInfo,
                    //},
                    Attachments = project.Attachments.Select(a => new {
                        a.ID,
                        a.AttachmentCategories,
                        a.AttachmentType,
                        a.AttachmentName
                    })
                }
            };
        }
        #endregion

        #region 入参定义

        /// <summary>
        /// 方法GetMarkPointProjectList的入参定义
        /// </summary>
        public class GetMarkPointProjectListInput {
            public string ProjectNo { get; set; }
            public string ProjectName { get; set; }
        }

        /// <summary>
        /// 方法GetMarkPointProjectInfo的入参定义
        /// </summary>
        public class GetMarkPointProjectInfoInnput {
            public string ID { get; set; }
        }

        /// <summary>
        /// 方法GetEstateActualSurveyProjectListInput的入参定义
        /// </summary>
        public class GetEstateActualSurveyProjectListInput {
            public string BusinessNumber { get; set; }
            public string ProjectName { get; set; }
        }

        public class GetEstateActualSurveyProjectListItem {
            public string ID { get; set; }
            public string BusinessNumber { get; set; }
            public string BusinessName { get; set; }
            public string BusinessType { get; set; }
            public DateTime CreateTime { get; set; }
            public DateTime? FinishTime { get; set; }
            public string DeveloperName { get; set; }
            public string CreatePersonName { get; set; }
            public string CreatePersonPhone { get; set; }
            public string SurveyCompanyName { get; set; }
            public int StateCode { get; set; }
            public string CurrentActionName { get; set; }
        }

        #endregion
    }
}
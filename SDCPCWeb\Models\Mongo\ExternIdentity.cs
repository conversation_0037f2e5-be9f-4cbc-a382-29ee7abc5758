﻿#region 文件信息描述
// /* ===============================================
// * 功能描述：
// * 创 建 人：吕俊宏
// * 创建日期：2021年02月04日 17:13
// * 项目名称：SDCPCWeb
// * 文件名称：ExternIdentity.cs
// * 创建用户名：Lvjunhong
// * ================================================*/
#endregion

using MongoDB.Bson.Serialization.Attributes;
using SDCPCWeb.Services;

namespace SDCPCWeb.Models.Mongo {
    /// <summary>
    /// 外部调用方
    /// </summary>
    public class ExternIdentity:IMongoEntity {
        /// <summary>
        /// 唯一身份标识
        /// </summary>
        [BsonId]
        public string Id { get; set; }
        /// <summary>
        /// 身份名称
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 公钥
        /// </summary>
        public string PublicKey { get; set; }
        /// <summary>
        /// 是否有效
        /// </summary>
        public bool IsValid { get; set; }
    }
}
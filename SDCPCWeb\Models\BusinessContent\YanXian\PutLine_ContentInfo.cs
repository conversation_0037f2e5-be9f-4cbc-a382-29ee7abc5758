﻿using Newtonsoft.Json;
using SDCPCWeb.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace SDCPCWeb.Models.BusinessContent.YanXian {
    public class PutLine_ContentInfoModel {
        /// <summary>
        /// 主键ID，与BusinessBaseInfo表的ID关联的外键
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 宗地代码
        /// </summary>
        public string GroundCode { get; set; }
        /// <summary>
        /// 工程规划许可
        /// </summary>
        public string ProjectPlanPermission { get; set; }
        /// <summary>
        /// 注册测绘师姓名
        /// </summary>
        public string SurveyMasterName { get; set; }
        /// <summary>
        /// 注册测绘师身份证号
        /// </summary>
        public string SurveyMasterNo { get; set; }
        /// <summary>
        /// 注册测绘师确认时间
        /// </summary>
        public DateTime? SurveyMasterSureTime { get; set; }
        /// <summary>
        /// 测绘成果状态
        /// 非负值：0成果未确认，1成果已确认，3验线中；
        /// 负值：-1验线不通过 -2 验线已通过
        /// </summary>
        public int DataCheckState { get; set; }
        /// <summary>
        /// 验线不通过原因
        /// </summary>
        public string RejectMessage { get; set; }
        /// <summary>
        /// 竣工用地面积
        /// </summary>
        public string CompletionLandArea { get; set; }
        /// <summary>
        /// 竣工总建筑面积
        /// </summary>
        public string CompletionBuildingArea { get; set; }
        
    }
    public class PutLine_ContentInfo : PutLine_ContentInfoModel, IOracleDataTable {
        public PutLine_ContentInfoModel ToModel() {
            return JsonConvert.DeserializeObject<PutLine_ContentInfoModel>(JsonConvert.SerializeObject(this));
        }

    }
}
﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace SDCPCWeb.Extensions {
    /// <summary>
    /// 异常扩展类
    /// </summary>
    public static class ExceptionExtension {
        /// <summary>
        /// 递归获取所有异常消息栈跟踪
        /// </summary>
        /// <param name="exception">异常信息</param>
        /// <returns></returns>
        public static string GetStackTraces(this Exception exception) {
            if (exception == null) return null;

            List<object> list = new List<object>();
            var e = exception;
            while (e != null) {
                list.Add(new {
                    e.Message,
                    e.StackTrace
                });

                e = e.InnerException;
            }

            return JsonConvert.SerializeObject(list);
        }
    }
}
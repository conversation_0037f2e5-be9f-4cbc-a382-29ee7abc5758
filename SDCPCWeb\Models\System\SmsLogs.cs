﻿using Newtonsoft.Json;
using SDCPCWeb.Services;
using System;

namespace SDCPCWeb.Models.System {
    /// <summary>
    /// 短信发送日志
    /// </summary>
    public class SmsLogsModel {
        /// <summary>
        /// ID
        /// </summary>
        public string ID { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 手机号码
        /// </summary>
        public string Phone { get; set; }

        /// <summary>
        /// 内容
        /// </summary>
        public string Content { get; set; }

    }
    /// <summary>
    /// 
    /// </summary>
    public class SmsLogs : SmsLogsModel, IOracleDataTable {
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public SmsLogsModel ToModel() {
            return JsonConvert.DeserializeObject<SmsLogsModel>(JsonConvert.SerializeObject(this));
        }
    }
}
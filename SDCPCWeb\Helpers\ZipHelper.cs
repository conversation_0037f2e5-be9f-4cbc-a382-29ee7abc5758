﻿using System;
using System.Collections.Generic;
using System.IO;
using Ionic.Zip;
using System.Text;
using System.Threading.Tasks;
using System.Linq;

namespace SDCPCWeb.Helpers {
    /// <summary>
    /// zip压缩类
    /// </summary>
    public class ZipHelper {
        /// <summary>
        /// 系统配置
        /// </summary>
        private string _tempFilePath;

        public ZipHelper(string tempFilePath) {
            this._tempFilePath = tempFilePath;
        }

        /// <summary>
        /// 压缩文件/文件夹
        /// </summary>
        /// <param name="filePath">需要压缩的文件/文件夹路径</param>
        /// <param name="zipFileName">压缩文件名称，名称必须全局唯一（zip后缀）</param>
        /// <param name="password">密码</param>
        /// <param name="reFileName">被压缩文件重命名新文件名，名称必须全局唯一（只有压缩文件才有效，压缩文件夹无效）</param>
        /// <param name="filterExts">需要过滤的文件后缀名</param>
        /// <returns>返回压缩文件路径</returns>
        public string CompressionFile(string filePath, string zipFileName, string password = "", string reFileName = "", List<string> filterExts = null) {
            using (ZipFile zip = new ZipFile(Encoding.UTF8)) {

                //设置压缩临时文件路径
                zip.TempFileFolder = this._tempFilePath;

                if (!string.IsNullOrWhiteSpace(password)) {
                    //设置压缩密码
                    zip.Password = password;
                }
                var dateStrPath = Path.Combine(this._tempFilePath, DateTime.Now.ToString("yyyyMMdd"));
                if (!Directory.Exists(dateStrPath)) {
                    Directory.CreateDirectory(dateStrPath);
                }
                string saveZipPath = Path.Combine(dateStrPath, zipFileName);

                //重命名后的文件路径
                string reFilePath = "";

                try {
                    //判断是否文件夹
                    if (Directory.Exists(filePath)) {
                        if (filterExts == null || !filterExts.Any())
                            zip.AddDirectory(filePath);
                        else
                            AddDirectory(zip, filePath, filePath, filterExts);
                    } else if (File.Exists(filePath)) { //文件
                                                        //如果需要重命名文件
                        if (!string.IsNullOrWhiteSpace(reFileName)) {
                            reFilePath = Path.Combine(dateStrPath, reFileName);
                            //复制源文件到新文件
                            File.Copy(filePath, reFilePath);
                            zip.AddFile(reFilePath, "");
                        } else {
                            zip.AddFile(filePath, "");
                        }
                    }
                    zip.Save(saveZipPath);
                    return saveZipPath;
                } finally {
                    if (!string.IsNullOrWhiteSpace(reFilePath) && File.Exists(reFilePath)) {
                        Task.Run(() => File.Delete(reFilePath));
                    }
                }
            }
        }

        /// <summary>
        /// 添加文件夹
        /// </summary>
        /// <param name="zip">ZipFile对象</param>
        /// <param name="dirPath">需要压缩的文件夹路径</param>
        /// <param name="rootPath">根目录路径</param>
        /// <param name="filterExts">需要过滤的文件后缀名</param>
        public void AddDirectory(ZipFile zip, string dirPath, string rootPath, List<string> filterExts) {
            var files = Directory.GetFiles(dirPath);
            for (int i = 0; i < files.Length; i++) {
                if (filterExts == null || (filterExts != null && !filterExts.Any(d => Path.GetExtension(files[i]).Contains(d)))) {
                    //获取相对路径作为zip文件中目录路径
                    //zip.AddFile(files[i], Path.GetRelativePath(rootPath, dirPath));
                    //如果没有Path.GetRelativePath方法，可以用下面代码替换
                    string relativePath = Path.GetFullPath(dirPath).Replace(Path.GetFullPath(rootPath), "");
                    zip.AddFile(files[i], relativePath);
                }
            }
            var dirs = Directory.GetDirectories(dirPath);
            for (int i = 0; i < dirs.Length; i++) {
                AddDirectory(zip, dirs[i], rootPath, filterExts);
            }
        }
    }
}

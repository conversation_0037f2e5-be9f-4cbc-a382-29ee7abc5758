﻿using Newtonsoft.Json;
using SDCPCWeb.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace SDCPCWeb.Models.System {
    /// <summary>
    /// 操作日志
    /// </summary>
    public class SystemLogsModel {
        /// <summary>
        /// ID
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 用户姓名
        /// </summary>
        public string UserName { get; set; }
        /// <summary>
        /// 用户身份证号
        /// </summary>
        public string UserNo { get; set; }
        /// <summary>
        /// 操作时间
        /// </summary>
        public DateTime OperationTime { get; set; }
        /// <summary>
        /// 操作类型
        /// </summary>
        public string OperationType { get; set; }
        /// <summary>
        /// 操作内容
        /// </summary>
        public string OperationContent { get; set; }
        /// <summary>
        /// 操作IP
        /// </summary>
        public string OperationIp { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
    }
    /// <summary>
    /// 
    /// </summary>
    public class SystemLogs : SystemLogsModel, IOracleDataTable {
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public SystemLogsModel ToModel() {
            return JsonConvert.DeserializeObject<SystemLogsModel>(JsonConvert.SerializeObject(this));
        }
    }
}
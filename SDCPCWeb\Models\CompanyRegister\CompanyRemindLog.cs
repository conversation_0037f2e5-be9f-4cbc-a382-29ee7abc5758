﻿using Newtonsoft.Json;
using SDCPCWeb.Services;
using System;

namespace SDCPCWeb.Models.CompanyRegister {
    /// <summary>
    /// 已经提醒过的即将到期的注册测绘师信息
    /// </summary>
    public class CompanyRemindLogModel {
        /// <summary>
        /// ID
        /// </summary>
        public string ID { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 企业id
        /// </summary>
        public string CompanyId { get; set; }

        /// <summary>
        /// 企业名称
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public string PersonName { get; set; }
        /// <summary>
        /// 身份证号
        /// </summary>
        public string PersonNumber { get; set; }

        /// <summary>
        /// 注册测绘师证书有效日期
        /// </summary>
        public DateTime? ValidityTime { get; set; }

        /// <summary>
        /// 提醒时间
        /// </summary>
        public DateTime? RemindTime { get; set; }

        /// <summary>
        /// 提醒的手机号，多个使用英文逗号","分隔
        /// </summary>
        public string RemindPhones { get; set; }
    }

    public class CompanyRemindLog : CompanyRemindLogModel, IOracleDataTable {
        public CompanyRemindLogModel ToModel() {
            return JsonConvert.DeserializeObject<CompanyRemindLogModel>(JsonConvert.SerializeObject(this));
        }
    }
}
﻿using Newtonsoft.Json;
using SDCPCWeb.Models.Attachment;
using SDCPCWeb.Models.BusinessContent.YanXian;
using SDCPCWeb.Models.System;
using SDCPCWeb.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace SDCPCWeb.Models.BusinessFlow {
    /// <summary>
    /// 不动产预核测绘自动办理业务
    /// </summary>
    public class RealEstatePreCheckSurveyAutoProject : BaseProject {
        //private static OracleDataService service = new OracleDataService();
        /// <summary>
        /// 基本信息
        /// </summary>
        public override BusinessBaseInfoModel BaseInfo { get; set; }
        /// <summary>
        /// 预测绘内容信息
        /// </summary>
        public PreSurvey_ContentInfo PreSurveyContentInfo { get; set; }
        /// <summary>
        /// 放线测量内容信息
        /// </summary>
        public PutLine_ContentInfo PutLineContentInfo { get; set; }
        /// <summary>
        /// 附件信息
        /// </summary>
        public override List<AttachmentInfoModel> Attachments { get; set; }
        /// <summary>
        /// 当前经过的流转环节信息
        /// </summary>
        public override List<BusinessLinkInfoModel> ActionsInfos { get; set; }
        /// <summary>
        /// 最新的环节，当前环节
        /// </summary>
        public override BusinessLinkInfoModel CurrentAction { get; set; }
        /// <summary>
        /// 工作流定义信息
        /// </summary>
        public override BusinessFlowBase FlowInfo { get; } = BusinessFlowConfig.RealEstatePreCheckSurveyAutoFlow;
        /// <summary>
        /// 根据ID获取信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static RealEstatePreCheckSurveyAutoProject GetByBusinessID(string id) {
            RealEstatePreCheckSurveyAutoProject project = new RealEstatePreCheckSurveyAutoProject();
            using (OracleDataService service = new OracleDataService()) {
                project.BaseInfo = service.GetById<BusinessBaseInfo>(id);
                project.PreSurveyContentInfo = service.GetById<PreSurvey_ContentInfo>(id);
                project.PutLineContentInfo = service.GetById<PutLine_ContentInfo>(id);
                project.Attachments = service.GetList<AttachmentInfo>("BusinessID='" + id + "' and StateCode=0")?.Select(a => a.ToModel()).ToList();
                //为了兼容附件分类名称做修改
                foreach (var attachmentInfoModel in project.Attachments.Where(s => s.AttachmentCategories == "报批规划指标校验图")) {
                    attachmentInfoModel.AttachmentCategories = "楼层平面图面积计算框线图";
                }
                project.ActionsInfos = service.GetList<BusinessLinkInfo>("BusinessID='" + id + "'")?.Select(a => a.ToModel()).OrderBy(a => a.StartTime).ToList();
                project.CurrentAction = project.ActionsInfos?.OrderByDescending(a => a.StartTime).FirstOrDefault();
                return project;
            }
        }
        /// <summary>
        /// 保存信息
        /// </summary>
        /// <param name="project"></param>
        /// <returns></returns>
        public static string Save(RealEstatePreCheckSurveyAutoProject project) {
            string result = "";
            using (OracleDataService service = new OracleDataService()) {
                try {
                    var record = service.GetById<BusinessBaseInfo>(project.BaseInfo.ID);
                    //更新业务基本信息表
                    record.BusinessName = project.BaseInfo.BusinessName;
                    record.ExtendInfo = project.BaseInfo.ExtendInfo;
                    service.UpdateOne(record);
                    //判断业务内容信息是否存在
                    PreSurvey_ContentInfo ps_content = service.GetById<PreSurvey_ContentInfo>(project.BaseInfo.ID);
                    if (ps_content == null) {
                        ps_content = project.PreSurveyContentInfo;
                        ps_content.ID = ps_content.ID ?? Guid.NewGuid().ToString("N");
                        service.InsertOne(ps_content);
                    }
                    else {
                        ps_content.GroundCode = project.PreSurveyContentInfo.GroundCode;
                        ps_content.ProjectPlanPermission = project.PreSurveyContentInfo.ProjectPlanPermission;
                        service.UpdateOne(ps_content);
                    }
                    PutLine_ContentInfo pl_content = service.GetById<PutLine_ContentInfo>(project.BaseInfo.ID);
                    if (pl_content == null) {
                        pl_content = project.PutLineContentInfo;
                        pl_content.ID = pl_content.ID ?? Guid.NewGuid().ToString("N");
                        service.InsertOne(pl_content);
                    }
                    else {
                        pl_content.GroundCode = project.PutLineContentInfo.GroundCode;
                        pl_content.ProjectPlanPermission = project.PutLineContentInfo.ProjectPlanPermission;
                        service.UpdateOne(pl_content);
                    }
                }
                catch (Exception e) {
                    result = e.Message;
                }
            }
            return result;
        }

        /// <summary>
        /// 流程服务
        /// </summary>
        public static FlowService<RealEstatePreCheckSurveyAutoFlow> FlowService => new FlowService<RealEstatePreCheckSurveyAutoFlow>();
    }
    /// <summary>
    /// 不动产预测绘流程定义
    /// </summary>
    public sealed class RealEstatePreCheckSurveyAutoFlow : BusinessFlowBase {
        public RealEstatePreCheckSurveyAutoFlow() {
            //实例化时，在构造函数定义工作流程
            FlowName = "不动产预核业务（即时办结）";
            Catalog = "规划验线";
            FlowActionInfo = new BusinessFlowActionInfo() {
                Actions = new[] {
                    new BusinessFlowAction() {
                        ID = 0,
                        Index = 0,
                        ActionRoles = new[] {UserRole.BusinessAdmin, UserRole.BusinessNormal},
                        Name = "填写申请信息"
                    },
                    new BusinessFlowAction() {
                        ID = 1,
                        Index = 1,
                        ActionRoles = new[] {UserRole.Myself},
                        Name = "委托测绘单位"
                    },
                    new BusinessFlowAction() {
                        ID = 2,
                        Index = 2,
                        ActionRoles = new[] {UserRole.SurveyMaster},
                        Name = "汇交测绘成果"
                    },
                    new BusinessFlowAction() {
                        ID = 4,
                        Index = 4,
                        ActionRoles = new[] {UserRole.ProjectCreator},
                        Name = "成果验收"
                    },
                    new BusinessFlowAction() {
                        ID = 5,
                        Index = 5,
                        ActionRoles = new[] {UserRole.Myself},
                        Name = "成果备案"
                    }
                },
                StartActionId = 0,
                EndActionIds = new[] { 5 },
                Routes = new[] {
                    new BusinessFlowRoute() {FromActionId = 0,ToActionId = 1},
                    new BusinessFlowRoute() {FromActionId = 1,ToActionId = 2},
                    new BusinessFlowRoute() {FromActionId = 2,ToActionId = 4},
                    new BusinessFlowRoute() {FromActionId = 4,ToActionId = 5}
                },
                BackRoutes = new[] {
                    new BusinessFlowBackRoute() {FromActionId = 5,ToActionId = 4,NeedResponseReason = true,IsBackToMyself = true },
                    new BusinessFlowBackRoute() {FromActionId = 4,ToActionId = 2,NeedResponseReason = true,IsBackToMyself = false },
                    //new BusinessFlowBackRoute() {FromActionId = 3,ToActionId = 2,NeedResponseReason = true,IsBackToMyself = false },
                    new BusinessFlowBackRoute() {FromActionId = 2,ToActionId = 1,NeedResponseReason = true,IsBackToMyself = false },
                    new BusinessFlowBackRoute() {FromActionId = 1,ToActionId = 0,NeedResponseReason = false,IsBackToMyself = true }
                },
                FinishWhenIntoEndAction = false
            };

            //增加路由事件
            var backRouteF2T1 = FlowActionInfo.BackRoutes.FirstOrDefault(r => r.FromActionId == 2 && r.ToActionId == 1);
            if (backRouteF2T1 != null) {
                backRouteF2T1.RoutePassed += BackRouteF2T1_RoutePassed; ;
            }
            var backRouteF4T2 = FlowActionInfo.BackRoutes.FirstOrDefault(r => r.FromActionId == 4 && r.ToActionId == 2);
            if (backRouteF4T2 != null) {
                backRouteF4T2.RoutePassed += BackRouteF4T2_RoutePassed; ;
            }
            var backRouteF5T4 = FlowActionInfo.BackRoutes.FirstOrDefault(r => r.FromActionId == 5 && r.ToActionId == 4);
            if (backRouteF5T4 != null) {
                backRouteF5T4.RoutePassed += BackRouteF5T4_RoutePassed; ;
            }
        }

        /// <summary>
        /// 从第3步返回第2步触发的事件
        /// </summary>
        /// <param name="baseInfo"></param>
        /// <param name="linkInfo"></param>
        private void BackRouteF2T1_RoutePassed(BusinessBaseInfoModel baseInfo, BusinessLinkInfoModel linkInfo) {
            //退回委托方，要移除已上传的测绘成果
            var businessId = baseInfo.ID;
            var oracle = new OracleDataService();
            //移除项目附件
            var attachments = oracle.GetList<AttachmentInfo>($"BusinessID='{businessId}' AND AttachmentType = '项目成果附件'");
            if (attachments.Any()) {
                foreach (var item in attachments) {
                    if (item.StateCode != 1) {
                        item.StateCode = 1;
                        oracle.UpdateOne(item);
                    }
                }
            }
            //移除项目的成果信息
            var contentInfo = oracle.GetById<PreSurvey_ContentInfo>(businessId);
            contentInfo.ProjectResultInfo = null;
            contentInfo.BuildingTableInfo = null;
            contentInfo.DataCheckID = null;
            contentInfo.DataCheckState = 0;
            contentInfo.SurveyMasterName = null;
            contentInfo.SurveyMasterNo = null;
            contentInfo.SurveyMasterSureTime = null;

            oracle.UpdateOne(contentInfo);

            var baseBusinessInfo = BusinessBaseInfo.FromModel(baseInfo);
            baseBusinessInfo.SurveyCompanyNo = null;
            baseBusinessInfo.SurveyCompanyName = null;
            oracle.UpdateOne(baseBusinessInfo);
        }


        /// <summary>
        /// 从第4步返回第2步触发的事件
        /// </summary>
        /// <param name="baseInfo"></param>
        /// <param name="linkInfo"></param>
        private void BackRouteF4T2_RoutePassed(BusinessBaseInfoModel baseInfo, BusinessLinkInfoModel linkInfo) {
            //退回测绘单位，要移除注册测绘师确认的标记
            var businessId = baseInfo.ID;
            var oracle = new OracleDataService();

            //移除项目的成果信息
            var contentInfo = oracle.GetById<PreSurvey_ContentInfo>(businessId);
            contentInfo.DataCheckState = 2;
            contentInfo.SurveyMasterName = null;
            contentInfo.SurveyMasterNo = null;
            contentInfo.SurveyMasterSureTime = null;

            oracle.UpdateOne(contentInfo);
        }
        /// <summary>
        /// 从第5步退回第4步触发的事件 
        /// </summary>
        /// <param name="baseInfo"></param>
        /// <param name="linkInfo"></param>
        private void BackRouteF5T4_RoutePassed(BusinessBaseInfoModel baseInfo, BusinessLinkInfoModel linkInfo) {
            //退回委托方，要移除已上传的测绘成果
            var businessId = baseInfo.ID;
            var oracle = new OracleDataService();

            //移除注册测绘师确认信息
            //预核测绘退回的时候，不需要标记审核不通过，也不需要处理注册测绘师状态，恢复为签收前的状态即可
            var pl_contentInfo = oracle.GetById<PutLine_ContentInfo>(businessId);
            if (pl_contentInfo!=null) {
                pl_contentInfo.DataCheckState = 1; 
                oracle.UpdateOne(pl_contentInfo);
            }

            var ps_contentInfo = oracle.GetById<PreSurvey_ContentInfo>(businessId);
            if (ps_contentInfo!=null) {
                ps_contentInfo.DataCheckState = 1;
                oracle.UpdateOne(ps_contentInfo);
            }
        }
        #region Overrides of BusinessFlowBase
        /// <summary>
        /// 流程环节定义信息JSON
        /// </summary>
        /// <returns></returns>
        public override string FlowActionJSON {
            get {
                return JsonConvert.SerializeObject(FlowActionInfo.Actions.Select(a => new {
                    a.ID,
                    a.Name
                }));
            }
        }

        /// <summary>
        /// 是否包含测绘作业
        /// </summary>
        public override bool HasSurvey { get; } = true;

        /// <summary>
        /// 流程是否启用
        /// </summary>
        public override bool Enable => BusinessFlowConfig.EnableFlows.Contains(GetType().Name);

        /// <summary>
        /// 该流程是否需要协议备案
        /// </summary>
        public override bool NeedBDCProtocol { get; } = true;

        /// <summary>
        /// 该流程是否在办理业务中可见
        /// </summary>
        public override bool Visible => BusinessFlowConfig.VisibleFlows.Contains(GetType().Name);
        #endregion
    }
}
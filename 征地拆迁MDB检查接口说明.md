# 征地拆迁数据交汇MDB检查接口说明

## 概述

为了支持征地拆迁数据交汇业务流程（LandSurveyProjectFlow），系统新增了专门的MDB检查接口，独立于原有的EPS接口。新接口提供了更完善的MDB文件检查功能，包括基础检查、要素类检查、拓扑检查和数据库检查四个步骤。

## 新增接口

### 1. 提交征地拆迁MDB测绘成果
- **接口路径**: `POST /sdcapi/BusinessFlow/PostLandSurveyMdbResult`
- **参数**: `id` (业务ID)
- **功能**: 专门用于征地拆迁数据交汇业务的MDB文件上传和检查
- **限制**: 仅支持 LandSurveyProjectFlow 业务类型

### 2. 获取征地拆迁MDB检查状态
- **接口路径**: `GET /sdcapi/BusinessFlow/GetLandSurveyMdbCheckState`
- **参数**: `id` (业务ID)
- **功能**: 查询MDB检查状态和结果
- **返回**: 检查状态、任务ID、失败原因、测绘师信息等

### 3. 征地拆迁MDB入库

- **接口路径**: `POST /sdcapi/BusinessFlow/SubmitLandSurveyMdbToDatabase`
- **参数**: `id` (业务ID), `layer` (图层名称，可选，默认为"default")
- **功能**: 提交MDB数据入库，更新注册测绘师信息
- **限制**: 仅支持 LandSurveyProjectFlow 业务类型，且MDB检查必须已完成
- **返回**: 入库成功或失败信息，失败时包含详细错误信息

## 接口配置

### 配置文件修改

在 `Web.config` 和 `Web.Release.config` 中添加了新的配置项：

```xml
<!--MDB检查接口基础地址-->
<add key="MdbCheckApiUrl" value="http://172.16.48.133:5000" />
```

### 代码配置

在 `SystemConfigs.cs` 中添加了新的配置属性：

```csharp
/// <summary>
/// MDB检查接口基础地址
/// </summary>
public static string MdbCheckApiUrl = WebConfigurationManager.AppSettings["MdbCheckApiUrl"];
```

## 接口流程

### 1. 文件上传和检查流程

当业务类型为 `LandSurveyProjectFlow` 时，系统会：

1. **文件上传**：调用 `POST {MdbCheckApiUrl}/mdb/upload` 上传MDB文件
2. **启动检查**：调用 `POST {MdbCheckApiUrl}/mdb/check/{task_id}` 启动检查流程
3. **状态监控**：通过后台任务定期调用 `GET {MdbCheckApiUrl}/mdb/status/{task_id}` 检查状态
4. **结果下载**：检查完成后调用 `GET {MdbCheckApiUrl}/mdb/checkresult/download/{task_id}` 获取结果

### 2. 检查状态说明

MDB检查包含四个步骤，需要按顺序完成：
- `step1:completed` - 基础检查完成
- `step2:completed` - 要素类检查完成
- `step3:completed` - 拓扑检查完成
- `step4:completed` - 入库检查完成

只有当 `step4:completed`（入库检查完成）后才算整个MDB检查完成。

### 3. 数据状态说明

`LandSurvey_ContentInfo.DataCheckState` 只有两个值：
- `0` - 检查中
- `1` - 检查完成

## 代码实现

### 新增控制器接口

在 `BusinessFlowManageController` 中新增了两个专门的接口：

#### 1. PostLandSurveyMdbResult 接口
```csharp
[HttpPost, Route("PostLandSurveyMdbResult")]
public async Task<ApiResult> PostLandSurveyMdbResult(string id) {
    // 验证业务类型为 LandSurveyProjectFlow
    // 清空原有数据
    // 调用MDB上传接口
    // 启动MDB检查
    // 安排后台监控任务
}
```

#### 2. GetLandSurveyMdbCheckState 接口
```csharp
[HttpGet, Route("GetLandSurveyMdbCheckState")]
public ApiResult GetLandSurveyMdbCheckState(string id) {
    // 验证业务类型为 LandSurveyProjectFlow
    // 返回检查状态和相关信息
}
```

### 后台任务

新增了 `SurveyResultJob.LoadMdbCheckState` 方法，专门用于监控MDB检查状态：

- 自动重试机制：最多20次，每次间隔60秒
- 状态检查：监控4个检查步骤的完成状态
- 完成条件：只有当step4（入库检查）完成后才算整个检查完成
- 结果下载：检查完成后自动下载txt检查结果文件
- 文件保存：将txt文件保存到AttachmentInfo表作为项目成果附件
- 状态更新：更新DataCheckState为1（检查完成）

### 数据模型

使用 `LandSurvey_ContentInfo` 模型存储检查信息：

- `DataCheckID`: 存储任务ID
- `DataCheckState`: 检查状态（0=检查中，1=通过，2=失败）
- `RejectMessage`: 失败原因
- `SurveyMasterName`: 测绘师姓名
- `SurveyMasterNo`: 测绘师编号
- `SurveyMasterSureTime`: 确认时间

## 测试

### 单元测试

在 `SurveyResultJobTests` 中添加了两个测试方法：

1. `TestMdbCheckApiConnectivity`: 测试MDB检查接口连通性
2. `TestMdbCheckResultParsing`: 测试MDB检查结果解析逻辑

### 运行测试

```bash
# 在Visual Studio中运行测试
# 或使用命令行
dotnet test SDCPCWebTests/Jobs/SurveyResultJobTests.cs
```

## 使用说明

### 前端调用

对于征地拆迁数据交汇业务（LandSurveyProjectFlow），前端需要调用新的专用接口：

1. **提交MDB文件**: 调用 `PostLandSurveyMdbResult` 接口
2. **查询检查状态**: 调用 `GetLandSurveyMdbCheckState` 接口

### 接口调用示例

#### 提交MDB文件
```javascript
// 使用FormData上传文件
const formData = new FormData();
formData.append('file', mdbFile);

fetch('/sdcapi/BusinessFlow/PostLandSurveyMdbResult?id=' + businessId, {
    method: 'POST',
    body: formData
})
.then(response => response.json())
.then(data => {
    if (data.StateCode === 1) {
        console.log('提交成功，任务ID：', data.Data);
    } else {
        console.error('提交失败：', data.Message);
    }
});
```

#### 查询检查状态
```javascript
fetch('/sdcapi/BusinessFlow/GetLandSurveyMdbCheckState?id=' + businessId)
.then(response => response.json())
.then(data => {
    if (data.StateCode === 1) {
        const checkInfo = data.Data;
        console.log('检查状态：', checkInfo.DataCheckState);
        // 0=检查中，1=通过，2=失败
    }
});
```

### 错误处理

- 上传失败：返回具体的错误信息
- 检查失败：在 `RejectMessage` 字段中记录详细的失败原因
- 网络异常：自动重试机制确保稳定性

## 注意事项

1. **独立接口**：新增了专门的接口，不影响原有的PostSurveyProjectResult接口
2. **业务类型限制**：只有 `LandSurveyProjectFlow` 业务类型才能使用这些新接口
3. **文件格式**：仅支持 `.mdb` 格式的文件
4. **检查完成条件**：必须等待step4（入库检查）完成才算整个检查完成
5. **状态值简化**：DataCheckState只有0（检查中）和1（检查完成）两个值
6. **自动文件保存**：检查完成后会自动下载并保存txt结果文件
7. **重试机制**：后台任务会自动重试，无需手动干预

## 故障排查

### 常见问题

1. **接口连接失败**
   - 检查网络连接
   - 确认MDB检查服务是否正常运行
   - 验证配置的URL是否正确

2. **检查一直处于pending状态**
   - 检查MDB检查服务的处理能力
   - 确认文件是否过大
   - 查看服务端日志

3. **检查结果解析失败**
   - 检查返回的JSON格式是否正确
   - 确认所有必要的字段都存在
   - 查看应用程序日志

### 日志查看

系统会在以下位置记录相关日志：
- Hangfire控制台：后台任务执行日志
- 应用程序日志：接口调用和异常信息
- 业务日志：文件上传和处理记录

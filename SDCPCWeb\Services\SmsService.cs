﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using Hangfire;
using Hangfire.Console;
using Hangfire.Server;
using Newtonsoft.Json;
using SDCPCWeb.Models.System;

namespace SDCPCWeb.Services {
    /// <summary>
    /// 短信发送服务
    /// </summary>
    public static class SmsService {
        private static string SmsServiceUrl = $"{ExternalApiConfig.CloudSmsServiceUrl}/data/savesendsms";
        /// <summary>
        /// 通用短信服务
        /// </summary>
        /// <param name="Mobile"></param>
        /// <param name="Content"></param>
        /// <returns></returns>
        public static async Task<string> SendSms(string Mobile, string Content) {
            var http = HttpClientFactory.Create();
            var resposne = await http.PostAsync(SmsServiceUrl, new StringContent(JsonConvert.SerializeObject(new {
                Mobile, Content
            }), Encoding.UTF8, "application/json"));

            if (resposne.IsSuccessStatusCode) {
                //记录返回值
                var responseTxt = await resposne.Content.ReadAsStringAsync();
                return responseTxt;
            }
            else {
                return $"接口返回状态失败：{(int) resposne.StatusCode}";
            }
        }

        /// <summary>
        /// Hangfire发送短信
        /// </summary>
        /// <param name="mobile"></param>
        /// <param name="content"></param>
        /// <param name="console"></param>
        /// <returns></returns>
        [DisplayName("发送短信"), AutomaticRetry(Attempts = 0)]
        public static async Task HangfireSendSms(string mobile, string content, PerformContext console) {
#if DEBUG
            console?.WriteLine("测试环境不发送短信");
            string result = "true";
#else

            string result = await SmsService.SendSms(mobile, content);
            console?.WriteLine(result);
#endif
            if (result.Contains("true")) {
                console?.WriteLine("发送成功");
                //写入日志表
                using (OracleDataService service = new OracleDataService()) {
                    SmsLogs log = new SmsLogs() {
                        ID = Guid.NewGuid().ToString("N"),
                        CreateTime = DateTime.Now,
                        Phone = mobile,
                        Content = content
                    };
                    service.InsertOne(log);
                }
            }
            else {
                console?.WriteLine("发送失败");
                throw new Exception(result);
            }
        }
    }
}
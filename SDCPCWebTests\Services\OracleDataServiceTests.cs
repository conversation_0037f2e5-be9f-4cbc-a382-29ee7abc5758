﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using SDCPCWeb.Services;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Oracle.ManagedDataAccess.Client;
using SDCPCWeb.Helpers;
using SDCPCWeb.Jobs;
using SDCPCWeb.Models.BusinessFlow;
using SDCPCWeb.Models.CompanyRegister;
using SDCPCWeb.Models.EstateConstruction;
using SDCPCWeb.Models.System;

namespace SDCPCWeb.Services.Tests {
    [TestClass()]
    public class OracleDataServiceTests {
        [TestMethod()]
        public void GetListTest() {
            using (var service = new OracleDataService()) {
                var list = service.GetList<CompanyRegisterRequest>("1=1");
            }
        }

        [TestMethod()]
        public void InsertOneTest() {
            var request = JsonConvert.DeserializeObject<CompanyRegisterRequest>(
                "{\"CompanyType\":\"测绘单位\",\"CompanyName\":\"测试测绘单位注册\",\"CompanyNo\":\"124501000075762645\",\"DetailInfo\":\"{\\\"CompanyBaseInfo\\\":{\\\"CompanyName\\\":\\\"测试测绘单位注册\\\",\\\"CompanyType\\\":\\\"测绘单位\\\",\\\"CompanyNo\\\":\\\"124501000075762645\\\",\\\"legalpersonName\\\":\\\"罗永梅\\\",\\\"legalpersonNo\\\":\\\"450205199111300725\\\",\\\"linkMan\\\":\\\"罗永梅\\\",\\\"contactPhone\\\":\\\"18376686974\\\",\\\"CompanyAddress\\\":\\\"测试\\\"}}\"}");
            //赋初始值
            request.ID = Guid.NewGuid().ToString("N");
            request.CreateUserID = "test";
            request.CreateUserName = "罗永梅";
            request.CreateUserPersonNo = "450205199111300725";
            request.CreateTime = DateTime.Now;
            request.PostTime = null;
            request.CloseTime = null;
            request.SuccessTime = null;
            request.ResponseMessage = null;
            request.ResponsePerson = null;
            request.StateCode = 0;
            using (var service = new OracleDataService()) {
                service.InsertOne(request);
            }
        }

        [TestMethod()]
        public void UpdateOneTest() {
            using (var service = new OracleDataService()) {
                var record = service.GetById<CompanyRegisterRequest>("3d79dfd3b59643cf947f40129ae9580a");
                record.StateCode = 5;
                service.UpdateOne(record);
                record.StateCode = 0;
                service.UpdateOne(record);
            }
        }

        [TestMethod()]
        public void GetPageTest() {
            using (var service = new OracleDataService()) {
                var list = service.GetPagerList<CompanyRegisterRequest>(1, 9, "1=1", "ID");
            }
        }

        [TestMethod()]
        public void InsertDirectoryDataTest() {
            using (var service = new OracleDataService()) {
                try {
                    string str = "甲级专业范围,乙级专业范围,丙级专业范围,丁级专业范围";
                    string[] a = str.Split(',');
                    for (int i = 0; i < a.Length; i++) {
                        DataDirectoryInfo d = new DataDirectoryInfo();
                        d.ID = Guid.NewGuid().ToString("N");
                        d.Name = a[i].ToString();
                        d.ParentID = "0";
                        d.Remark = "";
                        d.Sort = i + 1;
                        d.StateCode = 0;
                        //service.InsertOne(d);
                    }
                }
                catch (Exception e) {
                    Console.WriteLine(e.Message);
                }
            }
        }

        [TestMethod]
        public void EstateProjectInfoSaveTest() {
            var item = new EstateProjectInfo() {
                ID = Guid.NewGuid().ToString("N"),
                PlanCerificateCode = "450101202050173",
                PutLineSurveyID = "1c1536de0dce491b808117d3257826d3",
                PutLineSurveyState = SurveyState.CompleteSurvey,
                PreSurveyID = "a585d1d5fa9a410ea6c8545b5c2a9a1c",
                PreSurveyState = SurveyState.CompleteSurvey,
                CheckConditionSurveyID = null,
                CheckConditionSurveyState = SurveyState.NotSurvey,
                RealSurveyID = null,
                RealSurveyState = SurveyState.NotSurvey
            };
            using (var oracle = new OracleDataService()) {
                oracle.InsertOne(item);
                var selItem = EstateProjectInfo.GetEstateProjectInfo(item.ID); //oracle.GetById<EstateProjectInfo>(item.ID);
                Assert.IsNotNull(selItem);
                //oracle.DeleteOne(selItem);
                //selItem = oracle.GetById<EstateProjectInfo>(item.ID);
                //Assert.IsNull(selItem);
            }
        }

        [TestMethod]
        public void CompanyInfoModifyModelTest() {
            using (var oracle = new OracleDataService()) {
                var cid = "c8c3cc96a1844a7bbf2ed752c04cd2ce";
                var model = new CompanyInfoModifyModel();
                model.ModifyReason = "申请变更原因";
                model.CompanyBaseInfo = oracle.GetById<CompanyBaseInfo>(cid);
                model.CompanyQualification = oracle.GetById<CompanyQualification>(cid);
                model.CompanyEmployees = oracle.GetList<CompanyEmployees>(
                    "RelationRequestId=:companyId AND PersonRole='注册测绘师'",
                    new OracleParameter(":companyId", OracleDbType.Varchar2) { Value = cid });

                Console.WriteLine(JsonConvert.SerializeObject(model, new IsoDateTimeConverter() { DateTimeFormat = "yyyy-MM-dd HH:mm:ss" }));
            }
        }

        [TestMethod]
        public void TestModifyType() {
            var type1 = ModifyType.ModifyQualification;
            var type2 = ModifyType.AddAlterSurveyMaster;

            Console.WriteLine(type1 | type2);
        }

        [TestMethod]
        public void TestGetOrderedSurveyCompanyList() {
            var service = new OracleDataService();
            string companyname = null;
            string businessClass = "MarkPointSurveyFlow";
            var pageIndex = 1;
            var pageSize = 10;
            string where = "WHERE B.BUSINESSCLASS=:classname\n";
            var parameters = new List<OracleParameter>();
            parameters.Add(new OracleParameter(":classname", OracleDbType.Varchar2) { Value = businessClass });

            if (!string.IsNullOrWhiteSpace(companyname)) {
                where += "AND C.COMPANYNAME LIKE :companyname";
                parameters.Add(new OracleParameter(":companyname", OracleDbType.Varchar2) { Value = $"%{companyname}%" });
            }

            var pageList = service.GetPageResultWithSql<SurveyCompanyListItem>(SurveyCompanyListItem.MainSql + where,
                "ListOrder", pageIndex, pageSize, parameters.ToArray());
        }

        /// <summary>
        /// 检测实核测绘是否完成或者被驳回
        /// </summary>
        [TestMethod]
        public void CheckSHCHIsCompletedOrRejected() {
            var oracle = new OracleDataService();
            var completedList = new List<string>();
            var rejectedList = new List<string>();

            //获取正在成果备案环节的业务
            var businessList = oracle.GetList<BusinessBaseInfo>(
                "EXISTS(SELECT 1 FROM BUSINESSLINKINFO WHERE BUSINESSID=BUSINESSBASEINFO.ID AND STATECODE=1 AND LINKNAME='成果备案')");
            System.Diagnostics.Debug.WriteLine($"一共有{businessList.Count}条业务在成果备案环节");
            if (businessList.Count > 0) {
                foreach (var baseInfo in businessList) {
                    //逐条检测是否已经结束或者退件
                    List<dynamic> shchList = GetxCloudSHCHRecord(baseInfo.ID);
                    if (shchList.Count == 0) {
                        System.Diagnostics.Debug.WriteLine($"业务[{baseInfo.BusinessNumber},{baseInfo.BusinessName}]尚未签收");
                    }
                    else if (shchList.Count == 1) {
                        var shch = shchList.First();
                        //判断状态是否为签发或者退件签发
                        if (shch.CurActionName == "签发") {
                            System.Diagnostics.Debug.WriteLine($"业务[{baseInfo.BusinessNumber},{baseInfo.BusinessName}]已结案，将输出请求参数");
                            completedList.Add($"[{baseInfo.BusinessNumber},{baseInfo.BusinessName}]，请求参数：{JsonConvert.SerializeObject(new { SurveyId = baseInfo.ID })}");
                        }
                        else if (shch.CurActionName == "退件签发") {
                            System.Diagnostics.Debug.WriteLine($"业务[{baseInfo.BusinessNumber},{baseInfo.BusinessName}]已退件，将输出请求参数");
                            rejectedList.Add($"[{baseInfo.BusinessNumber},{baseInfo.BusinessName}]，请求参数：{JsonConvert.SerializeObject(new { SurveyId = baseInfo.ID, Message = shch.TJXX })}");
                        }
                        else {
                            System.Diagnostics.Debug.WriteLine($"业务[{baseInfo.BusinessNumber},{baseInfo.BusinessName}]正在[{shch.CurActionName}]环节，无需处理");
                        }
                    }
                    else {
                        System.Diagnostics.Debug.WriteLine($"业务[{baseInfo.BusinessNumber},{baseInfo.BusinessName}]包含多条业务");
                    }
                }

            }
            Console.WriteLine("已结案业务");
            foreach (var s in completedList) {
                Console.WriteLine(s);
            }
            Console.WriteLine("已退件业务");
            foreach (var s in rejectedList) {
                Console.WriteLine(s);
            }
        }

        private List<dynamic> GetxCloudSHCHRecord(string xsywid) {
            const string connStr =
                "Data Source=sql03.nlis.local;Initial Catalog=xCloudDb;Integrated Security=True;User ID=sa;Password=*********;Persist Security Info=True;Integrated Security=False;MultipleActiveResultSets=true;";
            var conn = new SqlConnection(connStr);
            var sql = $"Select * From DC_SHCH Where XSYWID='{xsywid}'";
            var da = new SqlDataAdapter(sql, conn);
            var dt = new DataTable();
            da.Fill(dt);
            return JsonConvert.DeserializeObject<List<object>>(JsonConvert.SerializeObject(dt,
                new IsoDateTimeConverter() { DateTimeFormat = "yyyy-MM-dd HH:mm:ss" }));
        }

        [TestMethod]
        public async Task ExportList() {
            var service = new OracleDataService();
            string sql =
                " select A.ID, A.BUSINESSNUMBER, A.BUSINESSNAME, A.FINISHTIME, A.BUSINESSCLASS, B.PROJECTPLANPERMISSION from BUSINESSBASEINFO A INNER JOIN ESTATEACTUALSURVEYCONTENTINFO B on A.ID = B.ID \n" +
                " where A.STATECODE = 2 and A.BUSINESSCLASS = 'RealEstateActualSurveyFlow' order by A.CREATETIME";

            var dt = service.ExecuteQuerySql(sql);

            DataTable newDt = new DataTable();
            DataColumn dc = new DataColumn("业务号", typeof(string));
            newDt.Columns.Add(dc);
            dc = new DataColumn("业务名称", typeof(string));
            newDt.Columns.Add(dc);
            dc = new DataColumn("办结时间", typeof(string));
            newDt.Columns.Add(dc);
            dc = new DataColumn("工规证号", typeof(string));
            newDt.Columns.Add(dc);
            dc = new DataColumn("是否仅实测", typeof(string));
            newDt.Columns.Add(dc);

            foreach (DataRow dataRow in dt.Rows) {
                string id = dataRow["ID"].ToString();
                string businessNumber = dataRow["BUSINESSNUMBER"].ToString();
                string businessName = dataRow["BUSINESSNAME"].ToString();
                string finishTime = Convert.ToDateTime(dataRow["FINISHTIME"]).ToString("yyyy-MM-dd HH:mm:ss");
                string businessClass = dataRow["BUSINESSCLASS"].ToString();
                var projectPlanpermissions = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(dataRow["PROJECTPLANPERMISSION"].ToString());



                var isOnlySC = await XCloudService.CheckIsOnlySC(id, businessClass);

                DataRow newRow = newDt.NewRow();
                newRow["业务号"] = businessNumber;
                newRow["业务名称"] = businessName;
                newRow["办结时间"] = finishTime;
                newRow["工规证号"] = string.Join(",", projectPlanpermissions.Select(s => s.Code));
                newRow["是否仅实测"] = isOnlySC == true ? "是" : "否";

                newDt.Rows.Add(newRow);

            }

            ExportToOffice2003XmlHelper exportHelper = new ExportToOffice2003XmlHelper();
            List<ExportExcelInfo> exportExcelInfoList = new List<ExportExcelInfo>();
            ExportExcelInfo exportExcelInfo = new ExportExcelInfo() {
                Titles = new List<string>() {"不动产实核业务工规证信息"},
                Data = newDt
            };
            exportExcelInfoList.Add(exportExcelInfo);
            var stream = exportHelper.CreateXmlStream(exportExcelInfoList);

            using (FileStream fileStream = File.Create("c:\\exportList.xls")) {
                await stream.CopyToAsync(fileStream);
                await fileStream.FlushAsync();
            }
        }
    }
}
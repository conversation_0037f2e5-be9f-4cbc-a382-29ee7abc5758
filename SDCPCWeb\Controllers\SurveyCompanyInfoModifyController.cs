﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using Oracle.ManagedDataAccess.Client;
using SDCPCWeb.Extensions;
using SDCPCWeb.Jobs;
using SDCPCWeb.Models;
using SDCPCWeb.Models.CompanyRegister;
using SDCPCWeb.Models.Result;
using SDCPCWeb.Models.System;
using SDCPCWeb.Services;
using WebGrease.Css.Extensions;

namespace SDCPCWeb.Controllers {
    /// <summary>
    /// 测绘单位信息变更控制器
    /// </summary>
    [RoutePrefix("sdcapi/SurveyCompanyInfoModify"), SDCAuthorize(SDCAuthorizeRole.BDCWeb)]
    public class SurveyCompanyInfoModifyController : ApiController {
        private readonly OracleDataService service = new OracleDataService();
        private UserInfo UserInfo => Request.Properties.ContainsKey("SDC-UserInfo") ? (UserInfo)Request.Properties["SDC-UserInfo"] : null;

        /// <summary>
        /// 创建并提交变更申请
        /// </summary>
        /// <param name="modifyModelObj"></param>
        /// <returns></returns>
        [HttpPost, Route("CreateModifyRequest")]
        public ApiResult CreateModifyRequest(object modifyModelObj) {
            if (modifyModelObj == null) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }
            //attn 反序列化程序也会按照这个Converter进行反序列化转换，导致不符合该格式的字符串不能反序列化为DateTime，因此该字段被转换为null，导致数据丢失
            //attn 因此，将入参使用JObject的FromObject和ToObject方法转换对象，可防止DateTime数据转换失败
            var modifyModel = JObject.FromObject(modifyModelObj).ToObject<CompanyInfoModifyModel>();
            //获取参数
            var companyId = modifyModel.CompanyBaseInfo?.ID;
            if (string.IsNullOrWhiteSpace(companyId)) {
                return new ApiResult() { StateCode = 0, Message = "单位ID未指定" };
            }

            var requestings = service.GetList<CompanyInfoModifyRequest>("CompanyId=:cid AND StateCode IN(0,1,2)",
                new OracleParameter(":cid", OracleDbType.Varchar2) { Value = companyId });
            if (requestings.Any()) {
                return new ApiResult() { StateCode = 0, Message = "单位已经提交变更申请了，请勿重复提交" };
            }

            var companyBaseInfo = service.GetById<CompanyBaseInfo>(companyId);
            if (companyBaseInfo.CompanyType.CompanyTypeFromName() != CompanyType.SurveyCompany) {
                return new ApiResult() { StateCode = 0, Message = "单位信息错误" };
            }

            var companyQualification = service.GetById<CompanyQualification>(companyId);
            //只取注册测绘师
            var companyEmployeeList = service.GetList<CompanyEmployees>("RelationRequestId=:companyId AND PersonRole='注册测绘师'",
                new OracleParameter(":companyId", OracleDbType.Varchar2) { Value = companyId });

            var oldDataModel = new CompanyRegisterDetailInfo() {
                CompanyBaseInfo = companyBaseInfo,
                CompanyQualification = companyQualification,
                CompanyEmployees = companyEmployeeList
            };

            var modifyType = CheckModifyType(oldDataModel, modifyModel);

            if (modifyType == ModifyType.NothingChanged) {
                return new ApiResult() { StateCode = 0, Message = "提交失败，系统检测到没有任何信息发生变化" };
            }

            //创建变更申请
            var id = Guid.NewGuid().ToString("N");
            var request = new CompanyInfoModifyRequest() {
                ID = id,
                Type = modifyType,
                StateCode = 1,
                CreateTime = DateTime.Now,
                CompanyId = companyId,
                CreateUserId = UserInfo.UserId,
                CreateUserName = UserInfo.PersonName,
                CreateUserNo = UserInfo.PersonNo,
                CreateUserPhone = UserInfo.Phone,
                OldContent = JsonConvert.SerializeObject(oldDataModel, SystemConfig.JsonDateTimeConverter),
                NewContent = JsonConvert.SerializeObject(modifyModel, SystemConfig.JsonDateTimeConverter),
                ModifyReason = modifyModel.ModifyReason
            };
            service.InsertOne(request);

            Request.WriteSDCLog("单位变更申请", $"提交申请 >> 申请ID：{request.ID} >> 单位ID：{request.CompanyId} ");
            return new ApiResult() { StateCode = 1, Message = "提交成功", Data = id };
        }

        /// <summary>
        /// 获取正在申请的单位信息变更申请
        /// </summary>
        /// <returns></returns>
        [HttpGet, Route("GetRequesting")]
        public ApiResult GetRequesting() {
            //获取用户所在单位
            var personNo = UserInfo.PersonNo;
            var employee = service.GetList<CompanyEmployees>("PersonNumber=:pno AND PersonRole='单位管理员'",
                new OracleParameter(":pno", OracleDbType.Varchar2) { Value = personNo });
            var companyId = employee.FirstOrDefault()?.RelationRequestId;
            if (string.IsNullOrWhiteSpace(companyId)) {
                return new ApiResult() { StateCode = 0, Message = "用户不是测绘单位的管理员" };
            }

            var requestings = service.GetList<CompanyInfoModifyRequest>("CompanyId=:cid AND StateCode IN(0,1,2)",
                new OracleParameter(":cid", OracleDbType.Varchar2) { Value = companyId });
            return new ApiResult() { StateCode = 1, Message = "获取完成", Data = requestings.FirstOrDefault()?.ToInfoModel() };
        }

        /// <summary>
        /// 获取变更信息详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("GetModifyRequest")]
        public ApiResult GetModifyRequest(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }

            return new ApiResult() { StateCode = 1, Data = service.GetById<CompanyInfoModifyRequest>(id)?.ToInfoModel() };

        }

        /// <summary>
        /// 获取单位申请变更的列表
        /// </summary>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpGet, Route("GetRequestList")]
        public ApiResult GetRequestList(int pageIndex = 1, int pageSize = 10) {
            //获取用户所在单位
            var personNo = UserInfo.PersonNo;
            var employee = service.GetList<CompanyEmployees>("PersonNumber=:pno AND PersonRole='单位管理员'",
                new OracleParameter(":pno", OracleDbType.Varchar2) { Value = personNo });
            var companyId = employee.FirstOrDefault()?.RelationRequestId;
            if (string.IsNullOrWhiteSpace(companyId)) {
                return new ApiResult() { StateCode = 0, Message = "您不是测绘单位管理员" };
            }

            var where = $"CompanyId='{companyId}'";
            var result = service.GetPagerList<CompanyInfoModifyRequest>(pageIndex, pageSize, where, "CreateTime");
            var total = service.GetTotalCountWithSql($"SELECT 1 FROM CompanyInfoModifyRequest WHERE {where}");
            var dt = new List<object>();
            var resObj = result.Select(r => new {
                r.ID,
                r.CompanyId,
                r.Type,
                TypeName = r.ModifyTypeName(),
                r.CreateUserName,
                r.CreateUserNo,
                r.CreateUserPhone,
                r.CreateTime,
                r.StateCode,
            });
            dt.AddRange(resObj);
            return new ApiResult() {
                StateCode = 1,
                Data = new PageResult<object>() {
                    DataTable = dt,
                    Page = new Page() {
                        PageIndex = pageIndex,
                        PageSize = pageSize,
                        Total = total
                    }
                }
            };
        }

        /// <summary>
        /// 比较两个模型的数据，得到修改类型
        /// </summary>
        /// <param name="oldDataModel"></param>
        /// <param name="modifyModel"></param>
        /// <returns></returns>
        private ModifyType CheckModifyType(CompanyRegisterDetailInfo oldDataModel, CompanyRegisterDetailInfo modifyModel) {
            ModifyType result = ModifyType.NothingChanged;
            //基本信息判断
            var oldBaseInfo = oldDataModel.CompanyBaseInfo;
            var newBaseInfo = modifyModel.CompanyBaseInfo;
            if (oldBaseInfo.CompanyName != newBaseInfo.CompanyName
                || oldBaseInfo.CreditCode != newBaseInfo.CreditCode
                || oldBaseInfo.CompanyAddress != newBaseInfo.CompanyAddress
                || oldBaseInfo.LegalPersonName != newBaseInfo.LegalPersonName
                || oldBaseInfo.LegalPersonNumber != newBaseInfo.LegalPersonNumber
                || oldBaseInfo.Contacter != newBaseInfo.Contacter
                || oldBaseInfo.ContacterPhone != newBaseInfo.ContacterPhone
                ) {
                result = result != ModifyType.NothingChanged ? result | ModifyType.ModifyBaseInfo : ModifyType.ModifyBaseInfo;
                //设置不能修改的字段信息
                newBaseInfo.RelationRequestId = oldBaseInfo.RelationRequestId;
                newBaseInfo.CompanyType = oldBaseInfo.CompanyType;
            }

            //资质信息判断
            var oldQualityInfo = oldDataModel.CompanyQualification;
            var newQualityInfo = modifyModel.CompanyQualification;
            if (oldQualityInfo.BusinessRange != newQualityInfo.BusinessRange
                || oldQualityInfo.CertificateNo != newQualityInfo.CertificateNo
                || oldQualityInfo.QualificationLevel != newQualityInfo.QualificationLevel
                || oldQualityInfo.ValidityTime != newQualityInfo.ValidityTime
                || oldQualityInfo.AttachmentInfo != newQualityInfo.AttachmentInfo
                ) {
                result = result != ModifyType.NothingChanged ? result | ModifyType.ModifyQualification : ModifyType.ModifyQualification;
                //设置不能修改的字段信息
                newQualityInfo.ID = oldQualityInfo.ID;
                newQualityInfo.RelationRequestId = oldQualityInfo.RelationRequestId;
                newQualityInfo.Notes = oldQualityInfo.Notes;
            }

            //人员信息变更判断
            var oldEmployees = oldDataModel.CompanyEmployees; //原来的注册测绘师
            var newEmployees = modifyModel.CompanyEmployees; //新提交进来的注册测绘师
            //判断是否有新增的注册测绘师
            if (newEmployees.Any(e => e.PersonRole == "注册测绘师" && string.IsNullOrWhiteSpace(e.ID))) {
                result = result != ModifyType.NothingChanged ? result | ModifyType.AddAlterSurveyMaster : ModifyType.AddAlterSurveyMaster;
            }
            else {
                //没有新增的，则判断是否有原本修改的
                if (newEmployees.Any(e => oldEmployees.Any(o => o.ID == e.ID && (
                                                              o.PersonPhone != e.PersonPhone
                                                                  || o.RegisteredSurveyorNo != e.RegisteredSurveyorNo
                                                                  || o.ValidityTime != e.ValidityTime
                                                                  || o.AttachmentInfo != e.AttachmentInfo
                                                              )))) {
                    //修改注册测绘师不支持姓名和身份证号修改
                    result = result != ModifyType.NothingChanged ? result | ModifyType.AddAlterSurveyMaster : ModifyType.AddAlterSurveyMaster;
                }
            }

            if ((result & ModifyType.AddAlterSurveyMaster) != 0) {
                var relationRequestId = oldEmployees.FirstOrDefault()?.RelationRequestId;
                if (string.IsNullOrWhiteSpace(relationRequestId)) {
                    relationRequestId = oldDataModel.CompanyBaseInfo.ID;
                }
                //有注册测绘师修改，则新增部分要赋值RelationRequestId
                newEmployees.Where(e => e.PersonRole == "注册测绘师" && string.IsNullOrWhiteSpace(e.ID)).ForEach(e => e.RelationRequestId = relationRequestId);
                //修改部分，则限制部分字段不能修改，如身份证号等
                newEmployees.Where(e => e.PersonRole == "注册测绘师" && !string.IsNullOrWhiteSpace(e.ID)).ForEach(e => {
                    e.RelationRequestId = relationRequestId;
                    if (oldEmployees.All(o => o.ID != e.ID)) {
                        e.ID = null;
                    }
                    else {
                        e.PersonRole = oldEmployees.First(o => o.ID == e.ID).PersonRole;
                        e.PersonNumber = oldEmployees.First(o => o.ID == e.ID).PersonNumber;
                        e.PersonName = oldEmployees.First(o => o.ID == e.ID).PersonName;
                    }
                });
            }

            return result;
        }
    }

    /// <summary>
    /// 测绘单位变更审核控制器
    /// </summary>
    [RoutePrefix("internal/SurveyCompanyInfoModifyAudit"), SDCAuthorize(SDCAuthorizeRole.xCloud)]
    public class SurveyCompanyInfoModifyAuditController : ApiController {
        private readonly OracleDataService service = new OracleDataService();
        private UserInfo UserInfo => Request.Properties.ContainsKey("SDC-UserInfo") ? (UserInfo)Request.Properties["SDC-UserInfo"] : null;

        /// <summary>
        /// 获取审核列表，带分页
        /// </summary>
        /// <param name="includeNotSign">是否包含未签收</param>
        /// <param name="includeSigned">是否包含已签收</param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        /// <remarks>当includeNotSign和includeSigned都是false的时候，includeNotSign强制为true</remarks>
        [HttpGet, Route("GetCompanyInfoModifyAuditList")]
        public PageResult<object> GetCompanyInfoModifyAuditList(bool includeNotSign = true, bool includeSigned = false, int pageIndex = 1, int pageSize = 20) {
            var stateCode = new List<int>();
            if (includeNotSign) {
                stateCode.Add(1);
            }
            if (includeSigned) {
                stateCode.Add(2);
            }
            var where = "StateCode in (1)";
            if (stateCode.Any()) {
                where = $"StateCode in ({string.Join(",", stateCode)})";
            }
            var result = service.GetPagerList<CompanyInfoModifyRequest>(pageIndex, pageSize, where, "CreateTime");
            var total = service.GetTotalCountWithSql($"SELECT 1 FROM CompanyInfoModifyRequest WHERE {where}");
            var dt = new List<object>();
            var resObj = result.Select(r => new {
                r.ID,
                r.CompanyId,
                r.Type,
                TypeName = r.ModifyTypeName(),
                r.CreateUserName,
                r.CreateUserNo,
                r.CreateUserPhone,
                r.CreateTime,
                r.OldContent,
                r.NewContent,
                r.AuditPerson
            });
            dt.AddRange(resObj);
            return new PageResult<object>() {
                DataTable = dt,
                Page = new Page() {
                    PageIndex = pageIndex,
                    PageSize = pageSize,
                    Total = total
                }
            };
        }

        /// <summary>
        /// 获取变更信息详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("GetModifyRequest")]
        public ApiResult GetModifyRequest(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }

            return new ApiResult() { StateCode = 1, Data = service.GetById<CompanyInfoModifyRequest>(id)?.ToInfoModel() };

        }

        /// <summary>
        /// 签收变更申请
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost, Route("SignModifyRequest")]
        public ApiResult SignModifyRequest(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }

            var record = service.GetById<CompanyInfoModifyRequest>(id);
            if (record == null) {
                return new ApiResult() { StateCode = 0, Message = "该变更申请不存在" };
            }

            if (record.StateCode != 1 && record.StateCode != 2) {
                return new ApiResult() { StateCode = 0, Message = "该变更申请已不能签收" };
            }

            record.StateCode = 2;
            record.AuditPerson = UserInfo.PersonName;
            service.UpdateOne(record);

            Request.WriteSDCLog("单位变更审核", $"签收申请 >> 申请ID：{record.ID} >> 申请单位ID：{record.CompanyId} ");
            return new ApiResult() { StateCode = 1, Message = "操作成功" };
        }

        /// <summary>
        /// 变更审核通过
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost, Route("AcceptModify")]
        public ApiResult AcceptModify(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }

            var record = service.GetById<CompanyInfoModifyRequest>(id);
            if (record == null) {
                return new ApiResult() { StateCode = 0, Message = "该变更申请不存在" };
            }

            if (record.StateCode != 1 && record.StateCode != 2) {
                return new ApiResult() { StateCode = 0, Message = "该变更申请已不能签收" };
            }

            AcceptCompanyInfoModify(record);

            Request.WriteSDCLog("单位变更审核", $"审核通过 >> 申请ID：{record.ID} >> 申请单位ID：{record.CompanyId} ");
            return new ApiResult() { StateCode = 1, Message = "操作成功" };
        }

        /// <summary>
        /// 变更驳回
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost, Route("RejectModify")]
        public ApiResult RejectModify(string id, string reason) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }

            var record = service.GetById<CompanyInfoModifyRequest>(id);
            if (record == null) {
                return new ApiResult() { StateCode = 0, Message = "该变更申请不存在" };
            }

            if (record.StateCode != 1 && record.StateCode != 2) {
                return new ApiResult() { StateCode = 0, Message = "该变更申请已不能签收" };
            }

            record.StateCode = 4;
            record.AuditPerson = UserInfo.PersonName;
            record.ResponseReason = reason;
            service.UpdateOne(record);

            Request.WriteSDCLog("单位变更审核", $"驳回申请 >> 申请ID：{record.ID} >> 申请单位ID：{record.CompanyId} ");
            return new ApiResult() { StateCode = 1, Message = "操作成功" };
        }

        /// <summary>
        /// 执行变更到单位信息
        /// </summary>
        /// <param name="record"></param>
        /// <remarks>调用此方法，入参已经经过检查</remarks>
        private void AcceptCompanyInfoModify(CompanyInfoModifyRequest record) {
            if (record != null) {
                //取变更后的信息
                var newDataModel = JsonConvert.DeserializeObject<CompanyRegisterDetailInfo>(record.NewContent);
                //取变更前的信息
                var oldBaseInfo = service.GetById<CompanyBaseInfo>(record.CompanyId);
                var oldQualityInfo = service.GetById<CompanyQualification>(record.CompanyId);

                if ((record.Type & ModifyType.ModifyBaseInfo) != 0) {
                    var newBaseInfo = newDataModel.CompanyBaseInfo;
                    //更新基本信息
                    oldBaseInfo.CompanyName = newBaseInfo.CompanyName;
                    oldBaseInfo.CreditCode = newBaseInfo.CreditCode;
                    oldBaseInfo.CompanyAddress = newBaseInfo.CompanyAddress;
                    oldBaseInfo.LegalPersonName = newBaseInfo.LegalPersonName;
                    oldBaseInfo.LegalPersonNumber = newBaseInfo.LegalPersonNumber;
                    oldBaseInfo.Contacter = newBaseInfo.Contacter;
                    oldBaseInfo.ContacterPhone = newBaseInfo.ContacterPhone;

                    service.UpdateOne(oldBaseInfo);
                }

                if ((record.Type & ModifyType.ModifyQualification) != 0) {
                    var newQualityInfo = newDataModel.CompanyQualification;
                    //判断是否多测合一业务类型有变化
                    var updateClasses = oldQualityInfo.BusinessRange != newQualityInfo.BusinessRange;
                    //更新资质信息
                    oldQualityInfo.BusinessRange = newQualityInfo.BusinessRange;
                    oldQualityInfo.CertificateNo = newQualityInfo.CertificateNo;
                    oldQualityInfo.QualificationLevel = newQualityInfo.QualificationLevel;
                    oldQualityInfo.ValidityTime = newQualityInfo.ValidityTime;
                    oldQualityInfo.AttachmentInfo = newQualityInfo.AttachmentInfo;

                    service.UpdateOne(oldQualityInfo);
                    //更新单位关联业务类型
                    if (updateClasses) {
                        Hangfire.BackgroundJob.Enqueue(() =>
                            SurveyCompanyJob.UpdateSurveyCompanyBusinessClass(record.CompanyId, null));
                    }
                }

                if ((record.Type & ModifyType.AddAlterSurveyMaster) != 0) {
                    //更新注册测绘师
                    var surveyMasters = newDataModel.CompanyEmployees.Where(e => e.PersonRole == "注册测绘师").ToList();

                    //处理新增的注册测绘师
                    var newSurveyMasters = surveyMasters.Where(e => string.IsNullOrWhiteSpace(e.ID));
                    foreach (var surveyMaster in newSurveyMasters) {
                        //判断每一个新的注册测绘师是否已经被注册
                        var personNo = surveyMaster.PersonNumber;
                        var companyEmployees = service.GetList<CompanyEmployees>($"PersonNumber=:pno",
                            new OracleParameter(":pno", OracleDbType.Varchar2) {Value = personNo});
                        if (companyEmployees.Any()) {
                            //如果注册测绘师角色已存在，就跳过
                            if (companyEmployees.FirstOrDefault().PersonRole == "注册测绘师")
                                continue;
                            else { 
                                //判断人员是否属于该单位，如果不属于就跳过
                                if (record.CompanyId != companyEmployees.FirstOrDefault().RelationRequestId)
                                    continue;

                                //否则删除原来的人员
                                service.DeleteOne(companyEmployees.FirstOrDefault());

                                //然后新增
                                surveyMaster.ID = Guid.NewGuid().ToString("N");
                                service.InsertOne(surveyMaster);
                            }
                        }
                        else {
                            surveyMaster.ID = Guid.NewGuid().ToString("N");
                            service.InsertOne(surveyMaster);
                        }
                    }

                    //处理修改的注册测绘师
                    var toUpdateSurveyMasters = surveyMasters.Where(e => !string.IsNullOrWhiteSpace(e.ID));
                    foreach (var surveyMaster in toUpdateSurveyMasters) {
                        var masterRecord = service.GetById<CompanyEmployees>(surveyMaster.ID);
                        if (masterRecord != null) {
                            masterRecord.AttachmentInfo = surveyMaster.AttachmentInfo;
                            masterRecord.PersonPhone = surveyMaster.PersonPhone;
                            masterRecord.RegisteredSurveyorNo = surveyMaster.RegisteredSurveyorNo;
                            masterRecord.ValidityTime = surveyMaster.ValidityTime;
                            service.UpdateOne(masterRecord);
                        }
                    }
                }

                //变更完成
                record.StateCode = 3;
                service.UpdateOne(record);
            }
        }
    }
}

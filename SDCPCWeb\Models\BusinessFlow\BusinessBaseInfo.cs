﻿using Newtonsoft.Json;
using SDCPCWeb.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace SDCPCWeb.Models.BusinessFlow {
    /// <summary>
    /// 业务基本信息
    /// </summary>
    public class BusinessBaseInfoModel {
        /// <summary>
        /// 业务ID
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 业务编号
        /// </summary>
        public string BusinessNumber { get; set; }
        /// <summary>
        /// 业务名称
        /// </summary>
        public string BusinessName { get; set; }
        /// <summary>
        /// 业务类型
        /// </summary>
        public string BusinessType { get; set; }
        /// <summary>
        /// 业务逻辑类
        /// </summary>
        public string BusinessClass { get; set; }
        /// <summary>
        /// 业务状态，0待签收，1办理中，2已完成，3已退回，4已关闭
        /// </summary>
        public int StateCode { get; set; }
        /// <summary>
        /// 创建业务的用户ID
        /// </summary>
        public string CreateUserId { get; set; }
        /// <summary>
        /// 创建业务的用户姓名
        /// </summary>
        public string CreatePersonName { get; set; }
        /// <summary>
        /// 创建业务的用户身份证号
        /// </summary>
        public string CreatePersonNo { get; set; }
        /// <summary>
        /// 创建业务的用户联系电话
        /// </summary>
        public string CreatePersonPhone { get; set; }
        /// <summary>
        /// 建设单位名称
        /// </summary>
        public string DeveloperName { get; set; }
        /// <summary>
        /// 建设单位社会统一信用代码
        /// </summary>
        public string DeveloperNo { get; set; }
        /// <summary>
        /// 测绘单位名称
        /// </summary>
        public string SurveyCompanyName { get; set; }
        /// <summary>
        /// 测绘单位社会统一信用代码
        /// </summary>
        public string SurveyCompanyNo { get; set; }
        /// <summary>
        /// 预留的扩展信息，Blob类型
        /// </summary>
        public string ExtendInfo { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }
        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? FinishTime { get; set; }

        /// <summary>
        /// 创建业务的单位证件号
        /// </summary>
        public string CreateCompanyNo { get; set; }

        /// <summary>
        /// 是否五象业务，值为1：是，其它：否；
        /// </summary>
        public string IsWuXiang { get; set; }

        /// <summary>
        /// 是否已办理规划核实业务，值为1：是，其它：否。
        /// </summary>
        public string IsCompletedGHHS { get; set; }
    }

    public class BusinessBaseInfo : BusinessBaseInfoModel, IOracleDataTable {

        public BusinessBaseInfoModel ToModel() {
            return JsonConvert.DeserializeObject<BusinessBaseInfoModel>(JsonConvert.SerializeObject(this));
        }

        public static BusinessBaseInfo FromModel(BusinessBaseInfoModel model) {
            return JsonConvert.DeserializeObject<BusinessBaseInfo>(JsonConvert.SerializeObject(model));
        }
    }

    /// <summary>
    /// 扩展类
    /// </summary>
    public class BusinessBaseInfoEx : BusinessBaseInfo {
        /// <summary>
        /// 当前环节Id
        /// </summary>
        public string CurrentLinkInfoId { get; set; }


    }
}
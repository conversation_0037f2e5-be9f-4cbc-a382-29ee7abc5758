﻿using SDCPCWeb.Models;
using SDCPCWeb.Models.EstateConstruction;
using SDCPCWeb.Models.Result;
using SDCPCWeb.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;

namespace SDCPCWeb.Controllers {
    [RoutePrefix("sdcapi/EstateConstruction"), BDCAuthorize]
    public class EstateConstructionController : ApiController {
        private readonly OracleDataService service = new OracleDataService();
        /// <summary>
        /// 保存宗地信息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost, Route("SaveCadastreInfo")]
        public ApiResult SaveCadastreInfo(CadastreInfo request) {
            return new ApiResult { StateCode = 1, Message = "保存成功" };
        }
        /// <summary>
        /// 删除宗地信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost, Route("DeleteCadastreInfo")]
        public ApiResult DeleteCadastreInfo(string id) {
            //需要判断是否有自然幢信息
            return new ApiResult { StateCode = 1, Message = "删除成功" };
        }
        /// <summary>
        /// 获取宗地列表
        /// </summary>
        /// <returns></returns>
        [HttpGet, Route("GetCadastreList")]
        public ApiResult GetCadastreList() {
            return new ApiResult { StateCode = 1, Data = "" };
        }
        /// <summary>
        /// 保存自然幢信息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost, Route("SaveNatureBuildInfo")]
        public ApiResult SaveNatureBuildInfo(NatureBuildInfo request) {
            return new ApiResult { StateCode = 1, Message = "保存成功" };
        }
        /// <summary>
        /// 删除自然幢信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost, Route("DeleteNatureBuildInfo")]
        public ApiResult DeleteNatureBuildInfo(string id) {
            //需要判断是否已审核
            return new ApiResult { StateCode = 1, Message = "删除成功" };
        }
        /// <summary>
        /// 获取自然幢列表
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("GetNatureBuildList")]
        public ApiResult GetNatureBuildList(string id) {
            return new ApiResult { StateCode = 1, Data = "" };
        }
        /// <summary>
        /// 自然幢提交审核
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost, Route("SubmmitNatureBuildAudit")]
        public ApiResult SubmmitNatureBuildAudit(string id) {
            return new ApiResult { StateCode = 1, Message = "提交成功" };
        }
        /// <summary>
        /// 自然幢审核签收
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost, Route("AcceptNatureBuildAudit")]
        public ApiResult AcceptNatureBuildAudit(string id) {
            return new ApiResult { StateCode = 1, Message = "签收成功" };
        }
        /// <summary>
        /// 自然幢审核通过
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost, Route("ThroughNatureBuildAudit")]
        public ApiResult ThroughNatureBuildAudit(string id) {
            return new ApiResult { StateCode = 1, Message = "审核成功" };
        }
        /// <summary>
        /// 自然幢审核退回
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost, Route("BackNatureBuildAudit")]
        public ApiResult BackNatureBuildAudit(string id)
        {
            return new ApiResult { StateCode = 1, Message = "退回成功" };
        }
        /// <summary>
        /// 获取自然幢审核列表
        /// </summary>
        /// <returns></returns>
        [HttpGet, Route("GetNatureBuildAuditList")]
        public ApiResult GetNatureBuildAuditList() {
            return new ApiResult { StateCode = 1, Data = "" };
        }
    }
}

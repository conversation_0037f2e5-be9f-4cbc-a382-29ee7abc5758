﻿using System;
using Newtonsoft.Json;
using SDCPCWeb.Services;

namespace SDCPCWeb.Models.BusinessFlow {
    /// <summary>
    /// 转移业务日志表
    /// </summary>
    public class TransferBusinessLogModel {
        /// <summary>
        /// id
        /// </summary>
        public string ID { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 创建人id
        /// </summary>
        public string CreateUserId { get; set; }

        /// <summary>
        /// 转出人id
        /// </summary>
        public string FromUserId { get; set; }

        /// <summary>
        /// 转入人id
        /// </summary>
        public string ToUserId { get; set; }

        /// <summary>
        /// 业务主表id
        /// </summary>
        public string BusinessBaseInfoId { get; set; }
    }

    public class TransferBusinessLog : TransferBusinessLogModel, IOracleDataTable {
        /// <summary>
        /// 转换为基类模型对象
        /// </summary>
        /// <returns></returns>
        public TransferBusinessLogModel ToModel() {
            return JsonConvert.DeserializeObject<TransferBusinessLogModel>(JsonConvert.SerializeObject(this));
        }

        /// <summary>
        /// 从基类模型对象转换为可入库的数据对象
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public static TransferBusinessLog FromModel(TransferBusinessLogModel model) {
            return JsonConvert.DeserializeObject<TransferBusinessLog>(JsonConvert.SerializeObject(model));
        }
    }
}
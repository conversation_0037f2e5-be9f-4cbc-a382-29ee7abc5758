﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text.RegularExpressions;
using System.Web.Http;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Oracle.ManagedDataAccess.Client;
using SDCPCWeb.Extensions;
using SDCPCWeb.Jobs;
using SDCPCWeb.Models;
using SDCPCWeb.Models.CompanyRegister;
using SDCPCWeb.Models.Result;
using SDCPCWeb.Models.System;
using SDCPCWeb.Services;

namespace SDCPCWeb.Controllers {
    /// <summary>
    /// 单位注册审核数据交换接口
    /// </summary>
    [RoutePrefix("internal/CompanyRegisterAudit"), SDCAuthorize(SDCAuthorizeRole.xCloud)]
    public class CompanyRegisterAuditController : ApiController {
        private readonly OracleDataService service = new OracleDataService();
        private UserInfo UserInfo => Request.Properties.ContainsKey("SDC-UserInfo") ? (UserInfo)Request.Properties["SDC-UserInfo"] : null;

        /// <summary>
        /// 获取单位信息审核接口，默认测绘单位
        /// </summary>
        /// <param name="stateCode"></param>
        /// <param name="companyName"></param>
        /// <param name="companyType"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpGet, Route("GetCompanyAuditList")]
        public ApiResult GetCompanyAuditList(int stateCode = 1, string companyName = "", string companyType = "测绘单位", int pageIndex = 1, int pageSize = 20) {
            if (companyName.Contains("'")) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }

            //1是待审核签收
            //2是审核中
            //6是待协议备案
            if (!new[] { 1, 2, 6 }.Contains(stateCode)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误，StateCode的值必须是1或者2或者6" };
            }
            string where = $"StateCode={stateCode}";
            string countWhere = "";
            if (!string.IsNullOrEmpty(companyName)) {
                where += " and CompanyName like '%" + companyName + "%'";
                countWhere += " and CompanyName like '%" + companyName + "%'";
            }

            if (string.IsNullOrWhiteSpace(companyType)) {
                companyType = CompanyType.SurveyCompany.ToName();
            }
            //检查companyType是否符合标准
            var type = companyType.CompanyTypeFromName();
            if (!Enum.IsDefined(typeof(CompanyType), type)) {
                return new ApiResult() { StateCode = 0, Message = $"参数错误，不支持的单位类型：{companyType}" };
            }
            where += $" and CompanyType='{companyType}'";
            countWhere += $" and CompanyType='{companyType}'";
            try {
                List<CompanyRegisterRequest> items = service.GetPagerList<CompanyRegisterRequest>(pageIndex, pageSize, where, "PostTime");
                dynamic CountInfo = new {
                    Total = service.GetList<CompanyRegisterRequest>($"StateCode={stateCode}" + countWhere + "").Count
                };
                dynamic PageInfo = new {
                    PageIndex = pageIndex,
                    PageSize = pageSize,
                    Total = service.GetList<CompanyRegisterRequest>(where).Count
                };
                dynamic deObj = new {
                    DataTable = items.Select(r => r.ToModel()),
                    Count = CountInfo,
                    Page = PageInfo
                };
                return new ApiResult { StateCode = 1, Data = deObj, Message = "" };
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 0, Message = "获取失败，错误信息：" + e.Message + "" };
            }
        }

        /// <summary>
        /// 返回特定状态的单位注册信息数
        /// </summary>
        /// <param name="stateCode"></param>
        /// <param name="companyType"></param>
        /// <returns></returns>
        [HttpGet, Route("GetCompanyAuditCount"), AllowAnonymous]
        public int GetCompanyAuditCount(int stateCode = 1, string companyType = "测绘单位") {
            if (companyType.Contains("'")) {
                return -1;
            }

            //1是待审核签收
            //2是审核中
            //6是待协议备案
            if (!new[] { 1, 2, 6 }.Contains(stateCode)) {
                return -1;
            }
            if (string.IsNullOrWhiteSpace(companyType)) {
                companyType = CompanyType.SurveyCompany.ToName();
            }
            //检查companyType是否符合标准
            var type = companyType.CompanyTypeFromName();
            if (!Enum.IsDefined(typeof(CompanyType), type)) {
                return -1;
            }
            var countWhere = $" and CompanyType='{companyType}'";
            return service.GetTotalCountWithSql($"Select 1 From CompanyRegisterRequest Where StateCode={stateCode}" + countWhere + "");
        }

        /// <summary>
        /// 根据ID获取单位注册申请信息
        /// </summary>
        /// <param name="companyRegId"></param>
        /// <returns></returns>
        [HttpGet, Route("GetCompanyRegistry")]
        public ApiResult GetCompanyRegistry(string companyRegId) {
            return new ApiResult() { StateCode = 1, Data = service.GetById<CompanyRegisterRequest>(companyRegId)?.ToModel() };
        }

        /// <summary>
        /// 签收单位注册审核信息
        /// </summary>
        /// <param name="companyRegId"></param>
        /// <returns></returns>
        [HttpPost, Route("SignCompanyRegistry")]
        public ApiResult SignCompanyRegistry(string companyRegId) {
            if (string.IsNullOrWhiteSpace(companyRegId)) {
                return new ApiResult { StateCode = 0, Message = "参数错误" };
            }
            //ID必须存在对应的记录
            var record = service.GetById<CompanyRegisterRequest>(companyRegId);
            if (record == null) {
                return new ApiResult { StateCode = 0, Message = "无法找到单位注册申请信息" };
            }
            //更改审核信息
            //获取审核人信息
            string auditor = UserInfo.PersonName;
            if (string.IsNullOrWhiteSpace(auditor)) {
                return new ApiResult() { StateCode = 0, Message = "您不具备进行此操作的权限" };
            }

            if (record.StateCode != 1 && record.StateCode != 2) {
                return new ApiResult() { StateCode = 0, Message = "操作失败，当前状态不可签收" };
            }

            if (record.StateCode == 1) {
                record.StateCode = 2;
                record.AcceptAuditTime = DateTime.Now;
                service.UpdateOne(record);
            }
            Request.WriteSDCLog("单位注册", $"审核签收 >> 注册ID：{record.ID} >> 注册单位：{record.CompanyName} >> ");
            return new ApiResult() { StateCode = 1, Message = "签收成功" };
        }

        /// <summary>
        /// 退回测绘单位注册申请
        /// </summary>
        /// <param name="companyRegId"></param>
        /// <param name="reason"></param>
        /// <returns></returns>
        [HttpPost, Route("RejectCompanyRegistry")]
        public ApiResult RejectCompanyRegistry(string companyRegId, string reason) {
            if (string.IsNullOrWhiteSpace(companyRegId)) {
                return new ApiResult { StateCode = 0, Message = "参数错误" };
            }
            if (string.IsNullOrWhiteSpace(reason)) {
                return new ApiResult { StateCode = 0, Message = "请填写退回原因" };
            }
            //ID必须存在对应的记录
            var record = service.GetById<CompanyRegisterRequest>(companyRegId);
            if (record == null) {
                return new ApiResult { StateCode = 0, Message = "无法找到单位注册申请信息" };
            }
            //更改审核信息
            //获取审核人信息
            string auditor = UserInfo.PersonName;
            if (string.IsNullOrWhiteSpace(auditor)) {
                return new ApiResult() { StateCode = 0, Message = "您不具备进行此操作的权限" };
            }

            record.ResponsePerson = auditor;
            record.ResponseMessage = reason;
            record.StateCode = 4;
            try {
                service.UpdateOne(record);
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 0, Message = "退回失败，错误信息：" + e.Message + "" };
            }
            Request.WriteSDCLog("单位注册", $"审核退回 >> 注册ID：{record.ID} >> 注册单位：{record.CompanyName} >> ");
            return new ApiResult { StateCode = 1, Message = "退回成功" };
        }

        /// <summary>
        /// 测绘单位通过资质审核
        /// </summary>
        /// <param name="companyRegId">申请ID</param>
        /// <returns></returns>
        [HttpPost, Route("AcceptSurveyCompanyRegistry")]
        public ApiResult AcceptSurveyCompanyRegistry(string companyRegId) {
            var companyReg = service.GetById<CompanyRegisterRequest>(companyRegId);
            if (companyReg == null) {
                return new ApiResult() { StateCode = 0, Message = "找不到测绘单位注册申请信息" };
            }

            if (companyReg.CompanyType.CompanyTypeFromName() != CompanyType.SurveyCompany) {
                return new ApiResult() { StateCode = 0, Message = "只有测绘单位的注册申请才能进行此操作" };
            }

            //测绘单位通过资质审核，需要检测状态值
            if (companyReg.StateCode != 2) {
                if (companyReg.StateCode == 0) {
                    return new ApiResult() { StateCode = 0, Message = "该申请尚未提交" };
                }

                if (companyReg.StateCode == 1) {
                    return new ApiResult() { StateCode = 0, Message = "请先签收该注册申请" };
                }

                if (companyReg.StateCode > 2) {
                    return new ApiResult() { StateCode = 0, Message = "该注册申请已通过或者已失效" };
                }

                return new ApiResult() { StateCode = 0, Message = "只有审核中的申请才能进行此操作" };
            }

            //获取审核人信息
            string auditor = UserInfo.PersonName;
            if (string.IsNullOrWhiteSpace(auditor)) {
                return new ApiResult() { StateCode = 0, Message = "您不具备进行此操作的权限" };
            }

            //根据申请的多测合一业务范围判断是否需要签署协议，若还需要则增加一个条件判断
            companyReg.ResponsePerson = auditor;
            companyReg.AcceptAuditTime = DateTime.Now;
            companyReg.StateCode = 3;

            //获取多测合一业务范围信息
            var detailInfo = JsonConvert.DeserializeObject<CompanyRegisterDetailInfo>(companyReg.DetailInfo);
            var qualificationInfo = detailInfo.CompanyQualification;
            var businessRange = JsonConvert.DeserializeObject<List<string>>(qualificationInfo.BusinessRange);
            var needProtocol = false; //是否需要签署协议
            foreach (var className in businessRange) {
                var flow = BusinessFlowConfig.GetFlow(Type.GetType($"SDCPCWeb.Models.BusinessFlow.{className}"));
                if (flow?.NeedBDCProtocol == true) {
                    needProtocol = true; //需要签署协议
                    break;
                }
            }

            if (needProtocol) {
                //需要签署协议的话，签署协议
                companyReg.StateCode = 6;
                service.UpdateOne(companyReg);
                Request.WriteSDCLog("单位注册", $"通过资质审核 >> 进入协议备案 >> 注册ID：{companyReg.ID} >> 注册单位：{companyReg.CompanyName} >> ");
                return new ApiResult() { StateCode = 1, Message = "审核通过，进入下一步签署协议" };
            }

            //控制人员信息，过滤已属于其它单位的人员
            var employeeList = detailInfo.CompanyEmployees;
            var realEmployeeList = new List<CompanyEmployees>();
            foreach (var employee in employeeList) {
                var existsList = service.GetList<CompanyEmployees>($"PersonNumber='{employee.PersonNumber}'");
                if (existsList.Any()) {
                    continue;
                }
                realEmployeeList.Add(employee);
            }

            if (companyReg.CompanyType.CompanyTypeFromName() == CompanyType.SurveyCompany) {
                //控制人员信息的比例
                if (realEmployeeList.All(e => e.PersonRole != "单位管理员") || realEmployeeList.All(e => e.PersonRole != "注册测绘师")) {
                    return new ApiResult() { StateCode = 0, Message = "测绘单位至少要有1名单位管理员和1名注册测绘师，且他们不能在其它单位的人员列表中" };
                }
            }

            //不需要签署协议，则直接完成创建单位
            CompleteCompany(companyReg);
            Request.WriteSDCLog("单位注册", $"通过资质审核 >> 完成注册 >> 注册ID：{companyReg.ID} >> 注册单位：{companyReg.CompanyName} >> ");
            return new ApiResult() { StateCode = 0, Message = "审核通过，单位注册完成" };
        }

        /// <summary>
        /// 单位注册申请已完成协议备案
        /// </summary>
        /// <param name="companyRegId"></param>
        /// <returns></returns>
        [HttpPost, Route("CompleteCompanyProtocolInfo")]
        public ApiResult CompleteCompanyProtocolInfo(string companyRegId) {
            var companyReg = service.GetById<CompanyRegisterRequest>(companyRegId);
            if (companyReg == null) {
                return new ApiResult() { StateCode = 0, Message = "找不到测绘单位注册申请信息" };
            }

            if (companyReg.StateCode != 6) {
                return new ApiResult() { StateCode = 0, Message = "只有等待协议备案的注册申请才能进行此操作" };
            }

            //获取审核人信息
            string auditor = UserInfo.PersonName;
            if (string.IsNullOrWhiteSpace(auditor)) {
                return new ApiResult() { StateCode = 0, Message = "您不具备进行此操作的权限" };
            }


            var detailInfo = JsonConvert.DeserializeObject<CompanyRegisterDetailInfo>(companyReg.DetailInfo);
            //控制人员信息
            var employeeList = detailInfo.CompanyEmployees;
            var realEmployeeList = new List<CompanyEmployees>();
            foreach (var employee in employeeList) {
                var existsList = service.GetList<CompanyEmployees>($"PersonNumber='{employee.PersonNumber}'");
                if (existsList.Any()) {
                    continue;
                }
                realEmployeeList.Add(employee);
            }

            if (companyReg.CompanyType.CompanyTypeFromName() == CompanyType.SurveyCompany) {
                //控制人员信息的比例
                if (realEmployeeList.All(e => e.PersonRole != "单位管理员") || realEmployeeList.All(e => e.PersonRole != "注册测绘师")) {
                    return new ApiResult() { StateCode = 0, Message = "测绘单位至少要有1名单位管理员和1名注册测绘师，且他们不能在其它单位的人员列表中" };
                }
            }
            else {
                if (realEmployeeList.All(e => e.PersonRole != "单位管理员")) {
                    return new ApiResult() { StateCode = 0, Message = "建设单位至少要有1名单位管理员，且他们不能在其它单位的人员列表中" };
                }
            }
            detailInfo.CompanyEmployees = realEmployeeList;
            companyReg.DetailInfo = JsonConvert.SerializeObject(detailInfo, SystemConfig.JsonDateTimeConverter);

            companyReg.StateCode = 3;
            CompleteCompany(companyReg);
            Request.WriteSDCLog("单位注册", $"完成协议备案 >> 注册ID：{companyReg.ID} >> 注册单位：{companyReg.CompanyName} >> ");
            return new ApiResult() { StateCode = 1, Message = "操作成功，单位注册完成" };

        }

        /// <summary>
        /// 判断用户是否是单位其它单位的工作人员
        /// </summary>
        /// <param name="personNo"></param>
        /// <returns></returns>
        [HttpPost, Route("CheckPersonIsRealEmployee")]
        public ApiResult CheckPersonIsRealEmployee(string personNo) {
            if (string.IsNullOrWhiteSpace(personNo)) {
                return new ApiResult() { StateCode = 0, Message = "personNo不能为空" };
            }

            var employees = service.GetList<CompanyEmployees>($"PersonNumber='{personNo}'");
            if (!employees.Any()) {
                return new ApiResult() { StateCode = 1, Message = "该人员不属于任何企业" };
            }
            //该人员属于企业
            var result = new List<object>();
            foreach (var employee in employees) {
                var company = service.GetById<CompanyBaseInfo>(employee.RelationRequestId);
                result.Add(new { PersonNo = employee.PersonNumber, employee.PersonRole, company.CompanyName, company.CompanyType });
            }
            return new ApiResult() { StateCode = 1, Data = result, Message = "该人员已属于其它企业" };
        }

        /// <summary>
        /// 获取已注册单位列表
        /// </summary>
        /// <param name="companyType">单位类型，默认测绘单位，取值范围有：测绘单位、开发商、机关单位、一般企业</param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpGet, Route("GetRegistedCompanyList")]
        public PageResult GetRegistedCompanyList(string find = "", string companyType = "测绘单位", int pageIndex = 1, int pageSize = 20) {
            if (string.IsNullOrWhiteSpace(companyType)) {
                companyType = CompanyType.SurveyCompany.ToName();
            }
            //检查companyType是否符合标准
            var type = companyType.CompanyTypeFromName();
            if (!Enum.IsDefined(typeof(CompanyType), type)) {
                return new PageResult() {
                    PageIndex = pageIndex,
                    PageSize = pageSize,
                    TotalCount = 0,
                    List = new List<object>()
                };
            }
            var where = $"CompanyType='{companyType}'";
            var parameters = new List<OracleParameter>();
            if (!string.IsNullOrWhiteSpace(find)) {
                //防注入
                where += " AND (CompanyName Like :name Or CreditCode=:code)";
                parameters.Add(new OracleParameter(":name", OracleDbType.Varchar2) { Value = $"%{find}%" });
                parameters.Add(new OracleParameter(":code", OracleDbType.Varchar2) { Value = find });
            }
            var list = service.GetPagerList<CompanyBaseInfo>(pageIndex, pageSize, where, "CompanyName", parameters.ToArray());
            var total = service.GetList<CompanyBaseInfo>(where, parameters.ToArray()).Count;
            return new PageResult() {
                PageIndex = pageIndex,
                PageSize = pageSize,
                TotalCount = total,
                List = list.Select(c => new {
                    c.ID,
                    c.CompanyName,
                    c.CreditCode,
                    c.CompanyType,
                    c.Contacter,
                    c.ContacterPhone
                })
            };
        }

        /// <summary>
        /// 根据ID获取已注册单位的信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("GetRegistedCompanyInfo")]
        public ApiResult GetRegistedCompanyInfo(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }
            var company = service.GetById<CompanyBaseInfo>(id);
            if (company == null) {
                return new ApiResult() { StateCode = 0, Message = "ID对应的记录不存在" };
            }

            var companyJObj = JObject.FromObject(company);
            if (company.CompanyType.CompanyTypeFromName() == CompanyType.SurveyCompany) {
                var employees = service.GetList<CompanyEmployees>($"RelationRequestId='{company.ID}'");
                companyJObj.Add("Employees", JArray.FromObject(employees));
                var quality = service.GetById<CompanyQualification>(company.ID);
                companyJObj.Add("Qualification", JObject.FromObject(CompanyQualificationShowModel.FromModel(quality.ToModel())));
            }
            else {
                var employees = service.GetList<DeveloperEmployee>($"RelationRequestId='{company.ID}'");
                companyJObj.Add("Employees", JArray.FromObject(employees));
            }

            return new ApiResult() { StateCode = 1, Data = companyJObj.ToObject<object>() };
        }

        /// <summary>
        /// 从单位注册申请信息创建单位信息并完成注册
        /// </summary>
        /// <param name="companyReg"></param>
        private void CompleteCompany(CompanyRegisterRequest companyReg) {
            //第一步，组织数据

            var detailInfo = JsonConvert.DeserializeObject<CompanyRegisterDetailInfo>(companyReg.DetailInfo);
            //基本信息
            var baseInfo = detailInfo.CompanyBaseInfo;
            baseInfo.ID = companyReg.ID;
            baseInfo.RelationRequestId = companyReg.ID;
            //详细信息
            //var companyDetail = detailInfo.CompanyDetailsInfo;
            //companyDetail.ID = companyReg.ID;
            //companyDetail.RelationRequestId = companyReg.ID;
            //人员信息
            var employeeList = detailInfo.CompanyEmployees;
            var grantemployeeList = new List<CompanyEmployees>();
            foreach (var employee in employeeList) {
                var existsList = service.GetList<CompanyEmployees>($"PersonNumber='{employee.PersonNumber}'");
                if (existsList.Any()) {
                    continue;
                }
                employee.ID = Guid.NewGuid().ToString("N");
                employee.RelationRequestId = companyReg.ID;
                grantemployeeList.Add(employee);
            }

            CompanyQualification quaInfo = null;
            //List<CompanyEquipments> equipmentList = null;
            if (companyReg.CompanyType.CompanyTypeFromName() == CompanyType.SurveyCompany) {
                //插入单位资质信息
                quaInfo = detailInfo.CompanyQualification;
                quaInfo.ID = companyReg.ID;
                quaInfo.RelationRequestId = companyReg.ID;
                //插入单位设备信息
                //equipmentList = detailInfo.CompanyEquipments;
                //if (equipmentList != null) {
                //    foreach (var equipment in equipmentList) {
                //        equipment.ID = Guid.NewGuid().ToString("N");
                //        equipment.RelationRequestId = companyReg.ID;
                //    }
                //}
            }

            //第二步，保存数据
            service.InsertOne(baseInfo);
            // service.InsertOne(companyDetail);
            foreach (var item in grantemployeeList) {
                //判断是否存在此员工
                service.InsertOne(item);
            }

            if (companyReg.CompanyType.CompanyTypeFromName() == CompanyType.SurveyCompany) {
                if (quaInfo != null) {
                    service.InsertOne(quaInfo);
                }
                //if (equipmentList != null) {
                //    foreach (var item in equipmentList) {
                //        service.InsertOne(item);
                //    }
                //}
            }
            //更新申请状态信息
            service.UpdateOne(companyReg);
            Hangfire.BackgroundJob.Enqueue(() => SurveyCompanyJob.UpdateSurveyCompanyBusinessClass(baseInfo.ID, null));
            Hangfire.BackgroundJob.Enqueue(() => SurveyCompanyJob.AddNewSurveyCompanyOrder(baseInfo.ID, null));
        }

        /// <summary>
        /// 更新指定测绘单位业务范围关联表
        /// </summary>
        /// <param name="companyId"></param>
        [HttpGet, Route("StartUpdateSurveyCompanyBusinessClass"), AllowAnonymous]
        public void StartUpdateSurveyCompanyBusinessClass(string companyId) {
            Hangfire.BackgroundJob.Enqueue(() => SurveyCompanyJob.UpdateSurveyCompanyBusinessClass(companyId, null));
        }
    }
}

﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace SDCPCWeb.Models.BusinessFlow {
    /// <summary>
    /// 测绘成果确认列表
    /// </summary>
    public class SurveyProjectResultListItem {
        /// <summary>
        /// 多测合一业务ID
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 业务编号
        /// </summary>
        public string BusinessNo { get; set; }
        /// <summary>
        /// 业务当前环节ID
        /// </summary>
        public string ActionId { get; set; }
        /// <summary>
        /// 业务名称
        /// </summary>
        public string BusinessName { get; set; }
        /// <summary>
        /// 业务类型
        /// </summary>
        public string BusinessType { get; set; }
        /// <summary>
        /// 确认时间
        /// </summary>
        public string ConfirmTime { get; set; }

        /// <summary>
        /// 参数顺序：name,personno
        /// </summary>
        public static string MainSql =>
            "SELECT BASE.ID,ACT.ID \"ActionId\",BASE.BusinessNumber \"BusinessNo\",BASE.BusinessName \"BusinessName\",BASE.BusinessType \"BusinessType\",CONTENT.SURVEYMASTERSURETIME \"ConfirmTime\"\n" +
            "FROM BUSINESSBASEINFO BASE\n" +
            "INNER JOIN BUSINESSLINKINFO ACT ON BASE.ID=ACT.BUSINESSID AND ACT.STATECODE IN(0,1,3)\n" +
            "INNER JOIN (\n" +
            "SELECT ID,SURVEYMASTERSURETIME,DATACHECKSTATE FROM PRESURVEY_CONTENTINFO UNION ALL\n" +
            "SELECT ID,SURVEYMASTERSURETIME,(CASE WHEN EXISTS(SELECT 1 FROM ATTACHMENTINFO WHERE BUSINESSID=PUTLINE_CONTENTINFO.ID AND STATECODE=0 AND ATTACHMENTTYPE='项目成果附件') THEN 1 ELSE 0 END) FROM PUTLINE_CONTENTINFO UNION ALL\n" +
            "SELECT ID,SURVEYMASTERSURETIME,(CASE WHEN EXISTS(SELECT 1 FROM ATTACHMENTINFO WHERE BUSINESSID=BLUELINESURVEYCONTENTINFO.ID AND STATECODE=0 AND ATTACHMENTTYPE='项目成果附件') THEN 1 ELSE 0 END) FROM BLUELINESURVEYCONTENTINFO UNION ALL\n" +
            "SELECT ID,SURVEYMASTERSURETIME,(CASE WHEN EXISTS(SELECT 1 FROM ATTACHMENTINFO WHERE BUSINESSID=MARKPOINTSURVEYCONTENTINFO.ID AND STATECODE=0 AND ATTACHMENTTYPE='项目成果附件') THEN 1 ELSE 0 END) FROM MARKPOINTSURVEYCONTENTINFO UNION ALL\n" +
            "SELECT ID,SURVEYMASTERSURETIME,(CASE WHEN EXISTS(SELECT 1 FROM ATTACHMENTINFO WHERE BUSINESSID=PLANCHECKSURVEYCONTENTINFO.ID AND STATECODE=0 AND ATTACHMENTTYPE='项目成果附件') THEN 1 ELSE 0 END) FROM PLANCHECKSURVEYCONTENTINFO UNION ALL\n" +
            "SELECT ID,SURVEYMASTERSURETIME,DATACHECKSTATE FROM ESTATEACTUALSURVEYCONTENTINFO \n" +
            ") CONTENT ON BASE.ID=CONTENT.ID\n" +
            "INNER JOIN (\n" +
            "SELECT C.CREDITCODE,E.PERSONNAME,E.PERSONNUMBER FROM COMPANYBASEINFO C INNER JOIN COMPANYEMPLOYEES E ON C.ID=E.RELATIONREQUESTID \n" +
            "WHERE C.COMPANYTYPE='测绘单位' AND E.PERSONROLE='注册测绘师' AND E.PERSONNAME='{0}' AND E.PERSONNUMBER='{1}'\n" +
            ") SM ON BASE.SURVEYCOMPANYNO=SM.CREDITCODE\n" +
            "WHERE CONTENT.DATACHECKSTATE=1 AND CONTENT.SURVEYMASTERSURETIME IS NULL \n" +
            "UNION ALL\n" +
            "SELECT BASE.ID,NULL \"ActionId\",BASE.BusinessNumber \"BusinessNo\",BASE.BusinessName \"BusinessName\",BASE.BusinessType \"BusinessType\",CONTENT.SURVEYMASTERSURETIME \"ConfirmTime\"\n" +
            "FROM BUSINESSBASEINFO BASE\n" +
            "INNER JOIN (\n" +
            "SELECT ID,SURVEYMASTERNAME,SURVEYMASTERNO,SURVEYMASTERSURETIME FROM PRESURVEY_CONTENTINFO WHERE SURVEYMASTERNAME='{0}' AND SURVEYMASTERNO='{1}' UNION ALL\n" +
            "SELECT ID,SURVEYMASTERNAME,SURVEYMASTERNO,SURVEYMASTERSURETIME FROM PUTLINE_CONTENTINFO WHERE SURVEYMASTERNAME='{0}' AND SURVEYMASTERNO='{1}' UNION ALL\n" +
            "SELECT ID,SURVEYMASTERNAME,SURVEYMASTERNO,SURVEYMASTERSURETIME FROM BLUELINESURVEYCONTENTINFO WHERE SURVEYMASTERNAME='{0}' AND SURVEYMASTERNO='{1}' UNION ALL\n" +
            "SELECT ID,SURVEYMASTERNAME,SURVEYMASTERNO,SURVEYMASTERSURETIME FROM MARKPOINTSURVEYCONTENTINFO WHERE SURVEYMASTERNAME='{0}' AND SURVEYMASTERNO='{1}' UNION ALL\n" +
            "SELECT ID,SURVEYMASTERNAME,SURVEYMASTERNO,SURVEYMASTERSURETIME FROM PLANCHECKSURVEYCONTENTINFO WHERE SURVEYMASTERNAME='{0}' AND SURVEYMASTERNO='{1}' UNION ALL\n" +
            "SELECT ID,SURVEYMASTERNAME,SURVEYMASTERNO,SURVEYMASTERSURETIME FROM ESTATEACTUALSURVEYCONTENTINFO WHERE SURVEYMASTERNAME='{0}' AND SURVEYMASTERNO='{1}'\n" +
            ") CONTENT ON BASE.ID=CONTENT.ID";
    }
}
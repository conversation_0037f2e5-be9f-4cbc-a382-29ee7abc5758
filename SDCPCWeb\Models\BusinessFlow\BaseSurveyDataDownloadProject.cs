﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Newtonsoft.Json;
using SDCPCWeb.Models.Attachment;
using SDCPCWeb.Models.BusinessContent;
using SDCPCWeb.Models.System;
using SDCPCWeb.Services;

namespace SDCPCWeb.Models.BusinessFlow {
    /// <summary>
    /// 基础测绘数据下载业务信息
    /// </summary>
    public class BaseSurveyDataDownloadProject : BaseProject {
        //private static OracleDataService service = new OracleDataService();
        /// <summary>
        /// 基本信息
        /// </summary>
        public override BusinessBaseInfoModel BaseInfo { get; set; }

        /// <summary>
        /// 内容信息
        /// </summary>
        public BaseGISData_ContentInfoModel ContentInfo { get; set; }

        /// <summary>
        /// 附件信息
        /// </summary>
        public override List<AttachmentInfoModel> Attachments { get; set; }
        /// <summary>
        /// 当前经过的流转环节信息
        /// </summary>
        public override List<BusinessLinkInfoModel> ActionsInfos { get; set; }

        /// <summary>
        /// 最新的环节，当前环节
        /// </summary>
        public override BusinessLinkInfoModel CurrentAction { get; set; }

        /// <summary>
        /// 工作流定义信息
        /// </summary>
        public override BusinessFlowBase FlowInfo { get; } = BusinessFlowConfig.BaseSurveyDataDownloadFlow;

        /// <summary>
        /// 根据ID获取信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static BaseSurveyDataDownloadProject GetByBusinessID(string id) {
            BaseSurveyDataDownloadProject project = new BaseSurveyDataDownloadProject();
            using (OracleDataService service = new OracleDataService()) {
                project.BaseInfo = service.GetById<BusinessBaseInfo>(id);
                project.ContentInfo = service.GetById<BaseGISData_ContentInfo>(id);
                project.Attachments = service.GetList<AttachmentInfo>("BusinessID='" + id + "' and StateCode=0")?.Select(a => a.ToModel()).ToList();
                project.ActionsInfos = service.GetList<BusinessLinkInfo>("BusinessID='" + id + "'")?.Select(a => a.ToModel()).OrderBy(a => a.StartTime).ToList();
                project.CurrentAction = project.ActionsInfos?.OrderByDescending(a => a.StartTime).FirstOrDefault();
                return project;
            }
        }
        /// <summary>
        /// 保存信息
        /// </summary>
        /// <param name="project"></param>
        /// <returns></returns>
        public static string Save(BaseSurveyDataDownloadProject project) {
            string result = "";
            using (OracleDataService service = new OracleDataService()) {
                try {
                    var record = service.GetById<BusinessBaseInfo>(project.BaseInfo.ID);
                    //更新业务基本信息表
                    record.BusinessName = project.BaseInfo.BusinessName;
                    record.ExtendInfo = project.BaseInfo.ExtendInfo;
                    service.UpdateOne(record);
                    //判断业务内容信息是否存在
                    BaseGISData_ContentInfo content = service.GetById<BaseGISData_ContentInfo>(project.BaseInfo.ID);
                    //更新业务内容信息
                    if (content == null) {
                        content = BaseGISData_ContentInfo.FromModel(project.ContentInfo);
                        content.ID = content.ID ?? Guid.NewGuid().ToString("N");
                        service.InsertOne(content);
                    }
                    else {
                        content.ProjectName = project.ContentInfo.ProjectName;
                        content.ProjectBasis = project.ContentInfo.ProjectBasis;
                        content.ProjectScope = project.ContentInfo.ProjectScope;
                        content.ApplyData = project.ContentInfo.ApplyData;
                        service.UpdateOne(content);
                    }
                }
                catch (Exception e) {
                    result = e.Message;
                }
            }
            return result;
        }

        /// <summary>
        /// 直接关闭业务
        /// </summary>
        /// <param name="project"></param>
        /// <returns></returns>
        public static void Close(BaseSurveyDataDownloadProject project, string closeReason) {
            if (project != null) {
                using (OracleDataService service = new OracleDataService()) {
                    project.BaseInfo.StateCode = 4;
                    project.BaseInfo.ExtendInfo = JsonConvert.SerializeObject(new { CloseReason = closeReason });
                    service.UpdateOne(BusinessBaseInfo.FromModel(project.BaseInfo));

                    project.CurrentAction.StateCode = 4;
                    project.CurrentAction.SignatureTime = project.CurrentAction.SignatureTime ?? DateTime.Now;
                    project.CurrentAction.EndTime = project.CurrentAction.EndTime ?? DateTime.Now;
                    service.UpdateOne(BusinessLinkInfo.FromModel(project.CurrentAction));

                    project.ContentInfo.DownLoadState = 4;
                    project.ContentInfo.FailedReason = closeReason;
                    service.UpdateOne(BaseGISData_ContentInfo.FromModel(project.ContentInfo));
                }
            }
        }
    }

    /// <summary>
    /// 基础数据下载流程定义
    /// </summary>
    public sealed class BaseSurveyDataDownloadFlow : BusinessFlowBase {

        public BaseSurveyDataDownloadFlow() {
            //实例化时，在构造函数定义工作流程
            FlowName = "测绘数据申请";
            Catalog = null;
            FlowActionInfo = new BusinessFlowActionInfo() {
                Actions = new[] {
                    new BusinessFlowAction() {
                        ID = 0,
                        Index = 0,
                        ActionRoles = new[] {UserRole.Myself},
                        Name = "填写申请信息"
                    },
                    new BusinessFlowAction() {
                        ID = 1,
                        Index = 1,
                        ActionRoles = new[] {UserRole.Myself},
                        Name = "上传项目范围"
                    },
                    new BusinessFlowAction() {
                        ID = 2,
                        Index = 2,
                        ActionRoles = new[] {UserRole.Myself},
                        Name = "审核"
                    },
                    new BusinessFlowAction() {
                        ID = 3,
                        Index = 3,
                        ActionRoles = new[] {UserRole.ProjectCreator},
                        Name = "下载数据"
                    }
                },
                StartActionId = 0,
                EndActionIds = new[] { 3 },
                Routes = new[] {
                    new BusinessFlowRoute() {FromActionId = 0,ToActionId = 1},
                    new BusinessFlowRoute() {FromActionId = 1,ToActionId = 2},
                    new BusinessFlowRoute() {FromActionId = 2,ToActionId = 3}
                },
                BackRoutes = new[] {
                    new BusinessFlowBackRoute() {FromActionId = 2,ToActionId = 1,NeedResponseReason = true,IsBackToMyself = false},
                    new BusinessFlowBackRoute() {FromActionId = 1,ToActionId = 0,NeedResponseReason = false,IsBackToMyself = true}
                },
                //反序列化会失败
                //ValidateActionPost = BaseSurveyDataDownloadPostValidate
                FinishWhenIntoEndAction = true
            };
            //增加路由事件
            var backRouteF2T1 = FlowActionInfo.BackRoutes.FirstOrDefault(r => r.FromActionId == 1 && r.ToActionId == 0);
            if (backRouteF2T1 != null) {
                backRouteF2T1.RoutePassed += BackRouteF1T0_RoutePassed; ;
            }
        }

        /// <summary>
        /// 从第2步返回第1步触发的事件
        /// </summary>
        /// <param name="baseInfo"></param>
        /// <param name="linkInfo"></param>
        private void BackRouteF1T0_RoutePassed(BusinessBaseInfoModel baseInfo, BusinessLinkInfoModel linkInfo) {
            //退回委托方，要移除已上传的测绘成果
            var businessId = baseInfo.ID;
            var oracle = new OracleDataService();
            //移除项目附件
            var attachments = oracle.GetList<AttachmentInfo>($"BusinessID='{businessId}' AND AttachmentType IN( '立项依据文件' )");
            if (attachments.Any()) {
                foreach (var item in attachments) {
                    if (item.StateCode != 1) {
                        item.StateCode = 1;
                        oracle.UpdateOne(item);
                    }
                }
            }
            //移除检查信息
            var contentInfo = oracle.GetById<BaseGISData_ContentInfo>(businessId);
            contentInfo.DataCheckID = null;
            contentInfo.DataCheckState = 0;
            oracle.UpdateOne(contentInfo);
        }
        #region Overrides of BusinessFlowBase

        /// <summary>
        /// 流程环节定义信息JSON
        /// </summary>
        /// <returns></returns>
        public override string FlowActionJSON {
            get {
                return JsonConvert.SerializeObject(FlowActionInfo.Actions.Select(a => new {
                    a.ID,
                    a.Name
                }));
            }
        }

        /// <summary>
        /// 是否包含测绘作业
        /// </summary>
        public override bool HasSurvey { get; } = false;

        /// <summary>
        /// 流程是否启用
        /// </summary>
        public override bool Enable => BusinessFlowConfig.EnableFlows.Contains(GetType().Name);

        /// <summary>
        /// 该流程是否需要协议备案
        /// </summary>
        public override bool NeedBDCProtocol { get; } = false;

        /// <summary>
        /// 该流程是否在办理业务中可见
        /// </summary>
        public override bool Visible => BusinessFlowConfig.VisibleFlows.Contains(GetType().Name);

        #endregion

        #region Delegates

        //private bool BaseSurveyDataDownloadPostValidate(BusinessBaseInfoModel baseInfo, BusinessLinkInfoModel linkInfo) {
        //    return true;
        //}

        #endregion
    }
}
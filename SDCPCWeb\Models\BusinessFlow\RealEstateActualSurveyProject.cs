﻿using Newtonsoft.Json;
using SDCPCWeb.Models.Attachment;
using SDCPCWeb.Models.BusinessContent.GuiHuaHeShi;
using SDCPCWeb.Models.System;
using SDCPCWeb.Services;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Configuration;
using Newtonsoft.Json.Linq;
using SDCPCWeb.Models.EstateConstruction;
using SDCPCWeb.ViewModels;

namespace SDCPCWeb.Models.BusinessFlow {
    /// <summary>
    /// 不动产实核测绘
    /// </summary>
    public class RealEstateActualSurveyProject : BaseProject {
        //private static OracleDataService service = new OracleDataService();
        /// <summary>
        /// 基本信息
        /// </summary>
        public override BusinessBaseInfoModel BaseInfo { get; set; }
        /// <summary>
        /// 内容信息
        /// </summary>
        public EstateActualSurveyContentInfo ContentInfo { get; set; }
        /// <summary>
        /// 附件信息
        /// </summary>
        public override List<AttachmentInfoModel> Attachments { get; set; }
        /// <summary>
        /// 当前经过的流转环节信息
        /// </summary>
        public override List<BusinessLinkInfoModel> ActionsInfos { get; set; }
        /// <summary>
        /// 最新的环节，当前环节
        /// </summary>
        public override BusinessLinkInfoModel CurrentAction { get; set; }
        /// <summary>
        /// 工作流定义信息
        /// </summary>
        public override BusinessFlowBase FlowInfo { get; } = BusinessFlowConfig.RealEstateActualSurveyFlow;

        /// <summary>
        /// 根据ID获取信息
        /// </summary>
        /// <param name="id"></param>
        /// <param name="getExtendAttr">是否获取扩展信息(主要用于提供给前端)</param>
        /// <returns></returns>
        public static RealEstateActualSurveyProject GetByBusinessID(string id) {
            RealEstateActualSurveyProject project = new RealEstateActualSurveyProject();
            using (OracleDataService service = new OracleDataService()) {
                project.BaseInfo = service.GetById<BusinessBaseInfo>(id);
                project.ContentInfo = service.GetById<EstateActualSurveyContentInfo>(id);
                project.Attachments = service.GetList<AttachmentInfo>("BusinessID='" + id + "' and StateCode=0")?.Select(a => a.ToModel()).ToList();
                project.ActionsInfos = service.GetList<BusinessLinkInfo>("BusinessID='" + id + "'")?.Select(a => a.ToModel()).OrderBy(a => a.StartTime).ToList();
                project.CurrentAction = project.ActionsInfos?.OrderByDescending(a => a.StartTime).FirstOrDefault();

                return project;
            }
        }

        /// <summary>
        /// 根据ID获取信息
        /// </summary>
        /// <param name="id">业务id</param>
        /// <param name="getExtendAttr">是否获取扩展信息(主要用于提供给前端)</param>
        /// <returns></returns>
        public static async Task<RealEstateActualSurveyProject> GetByBusinessID(string id, bool? getExtendAttr) {
            var project = GetByBusinessID(id);

            if (getExtendAttr == true) {
                if (project.CurrentAction != null) {
                    var BusinessLinkInfoModelEx = new BusinessLinkInfoModelEx(project.CurrentAction);
                    BusinessLinkInfoModelEx.ExtendAttr =
                        JObject.FromObject(new {
                            @UploadMDBOnly = await XCloudService.CheckIsReplaceMDB(id, nameof(RealEstateActualSurveyFlow)), //是否退回只能修改上传MDB文件
                            @XCloudSteps = (project.CurrentAction.ActionId == 5) ? await BusinessFlowConfig.CreateXCloudSteps(id, nameof(RealEstateActualSurveyFlow)) : null, //获取云平台流程步骤
                            @XCloudSPYJ = (project.CurrentAction.ActionId == 5) ? await XCloudService.GetSpyjByBusinessId(id, nameof(RealEstateActualSurveyFlow)) : null, //获取云平台审批意见
                            @IsOnlySC = (project.CurrentAction.ActionId == 5 && project.BaseInfo.StateCode == 2) ? await XCloudService.CheckIsOnlySC(id, nameof(RealEstateActualSurveyFlow)) : false, //判断是否仅实测
                            @XCloudSuspendInfo = (project.CurrentAction.ActionId == 5) ? await XCloudService.GetSuspendInfo(id, nameof(RealEstateActualSurveyFlow)) : null, //获取云平台挂起信息
                        }); 

                    project.CurrentAction = BusinessLinkInfoModelEx;
                }
            }

            return project;
        }

        /// <summary>
        /// 保存信息
        /// </summary>
        /// <param name="project"></param>
        /// <returns></returns>
        public static async Task<string> Save(RealEstateActualSurveyProject project) {
            string result = "";
            using (OracleDataService service = new OracleDataService()) {
                try {
                    var record = service.GetById<BusinessBaseInfo>(project.BaseInfo.ID);
                    //更新业务基本信息表
                    record.BusinessName = project.BaseInfo.BusinessName;
                    record.ExtendInfo = project.BaseInfo.ExtendInfo;
                    

                    //判断业务内容信息是否存在
                    EstateActualSurveyContentInfo content = service.GetById<EstateActualSurveyContentInfo>(project.BaseInfo.ID);

                    //判断是否五象业务
                    bool isWuXiang = false;
                    List<string> wuxiangCodes = new List<string>();

                    if (!string.IsNullOrWhiteSpace(project.ContentInfo?.ProjectPlanPermission)) {
                        var projectPlanPermission = project.ContentInfo?.ProjectPlanPermission;
                        var plans =
                                JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(projectPlanPermission);
                        foreach (var projectPlanPermissionInfo in plans) {
                            if (!string.IsNullOrWhiteSpace(projectPlanPermissionInfo.CaseCode)) {
                                if (projectPlanPermissionInfo.CaseCode.StartsWith("五象")) {
                                    isWuXiang = true;
                                    //break;
                                    wuxiangCodes.Add(projectPlanPermissionInfo.Code);
                                }
                            }
                            else {
                                if (projectPlanPermissionInfo.Code.Length > 11 && projectPlanPermissionInfo.Code.Substring(10, 1) == "5") {
                                    isWuXiang = true;
                                    //break;
                                    wuxiangCodes.Add(projectPlanPermissionInfo.Code);
                                }
                            }
                        }
                    }

                    record.IsWuXiang = isWuXiang ? "1" : null;

                    //判断是否已办理规划核实业务
                    if (wuxiangCodes.Any()) {
                        var checkResult = await XCloudService.CheckIsCompletedGHHS(wuxiangCodes.ToArray());
                        if (!string.IsNullOrWhiteSpace(checkResult.Item1)) {
                            return checkResult.Item1;
                        }
                        else {
                            record.IsCompletedGHHS = checkResult.Item2 == true ? "1" : null;
                        }
                    }

                    service.UpdateOne(record);

                    if (content == null) {
                        content = project.ContentInfo;
                        content.ID = content.ID ?? Guid.NewGuid().ToString("N");
                        service.InsertOne(content);
                    }
                    else {
                        content.GroundCode = project.ContentInfo.GroundCode;
                        content.ProjectPlanPermission = project.ContentInfo.ProjectPlanPermission;
                        content.ProjectLandUse = project.ContentInfo.ProjectLandUse;
                        content.CompanyAddress = project.ContentInfo.CompanyAddress;
                        content.LegalPersonName = project.ContentInfo.LegalPersonName;
                        content.LegalPersonNumber = project.ContentInfo.LegalPersonNumber;
                        content.LegalPersonPhone = project.ContentInfo.LegalPersonPhone;
                        content.PlanConditionNumber = project.ContentInfo.PlanConditionNumber;
                        content.UnifiedProjectCode = project.ContentInfo.UnifiedProjectCode;
                        content.ProjectType = project.ContentInfo.ProjectType;
                        content.IsZJ = project.ContentInfo.IsZJ;
                        content.PersonType = project.ContentInfo.PersonType;

                        service.UpdateOne(content);
                    }
                }
                catch (Exception e) {
                    result = e.Message;
                }
                return result;
            }
        }

        /// <summary>
        /// 流程服务
        /// </summary>
        public static FlowService<RealEstateActualSurveyFlow> FlowService => new FlowService<RealEstateActualSurveyFlow>();
    }
    /// <summary>
    /// 不动产实核测绘流程定义
    /// </summary>
    public sealed class RealEstateActualSurveyFlow : BusinessFlowBase {
        /// <summary>
        /// 不动产实核测绘流程
        /// </summary>
        public RealEstateActualSurveyFlow() {
            //实例化时，在构造函数定义工作流程
            FlowName = "不动产实核业务";
            Catalog = "不动产规划核实及实测";
            FlowActionInfo = new BusinessFlowActionInfo() {
                Actions = new[] {
                    new BusinessFlowAction() {
                        ID = 0,
                        Index = 0,
                        ActionRoles = new[] {UserRole.BusinessAdmin, UserRole.BusinessNormal},
                        Name = "填写申请信息"
                    },
                    new BusinessFlowAction() {
                        ID = 1,
                        Index = 1,
                        ActionRoles = new[] {UserRole.Myself},
                        Name = "委托测绘单位"
                    },
                    new BusinessFlowAction() {
                        ID = 2,
                        Index = 2,
                        ActionRoles = new[] {UserRole.SurveyMaster},
                        Name = "汇交测绘成果"
                    },
                    new BusinessFlowAction() {
                        ID = 4,
                        Index = 4,
                        ActionRoles = new[] {UserRole.ProjectCreator},
                        Name = "成果验收"
                    },
                    new BusinessFlowAction() {
                        ID=5,
                        Index = 5,
                        ActionRoles = new []{UserRole.Myself},
                        Name = "成果备案"
                    },
                },
                StartActionId = 0,
                EndActionIds = new[] { 5 },
                Routes = new[] {
                    new BusinessFlowRoute() {FromActionId = 0,ToActionId = 1},
                    new BusinessFlowRoute() {FromActionId = 1,ToActionId = 2},
                    new BusinessFlowRoute() {FromActionId = 2,ToActionId = 4},
                    new BusinessFlowRoute() {FromActionId = 4,ToActionId = 5},
                },
                BackRoutes = new[] {
                    new BusinessFlowBackRoute() {FromActionId = 5,ToActionId = 4,NeedResponseReason = true,IsBackToMyself = true },
                    new BusinessFlowBackRoute() {FromActionId = 4,ToActionId = 2,NeedResponseReason = true,IsBackToMyself = false },
                    new BusinessFlowBackRoute() {FromActionId = 2,ToActionId = 1,NeedResponseReason = true,IsBackToMyself = false },
                    new BusinessFlowBackRoute() {FromActionId = 1,ToActionId = 0,NeedResponseReason = false,IsBackToMyself = true }
                },
                FinishWhenIntoEndAction = false
            };

            //增加路由事件
            var backRouteF2T1 = FlowActionInfo.BackRoutes.FirstOrDefault(r => r.FromActionId == 2 && r.ToActionId == 1);
            if (backRouteF2T1 != null) {
                backRouteF2T1.RoutePassed += BackRouteF2T1_RoutePassed; ;
            }
            var backRouteF4T2 = FlowActionInfo.BackRoutes.FirstOrDefault(r => r.FromActionId == 4 && r.ToActionId == 2);
            if (backRouteF4T2 != null) {
                backRouteF4T2.RoutePassed += BackRouteF4T2_RoutePassed; ;
            }
            var backRouteF5T4 = FlowActionInfo.BackRoutes.FirstOrDefault(r => r.FromActionId == 5 && r.ToActionId == 4);
            if (backRouteF5T4 != null) {
                backRouteF5T4.RoutePassed += BackRouteF5T4_RoutePassed; ;
            }
        }
        /// <summary>
        /// 从第3步返回第2步触发的事件 
        /// </summary>
        /// <param name="baseInfo"></param>
        /// <param name="linkInfo"></param>
        private void BackRouteF2T1_RoutePassed(BusinessBaseInfoModel baseInfo, BusinessLinkInfoModel linkInfo) {
            //退回委托方，要移除已上传的测绘成果
            var businessId = baseInfo.ID;
            var oracle = new OracleDataService();
            //移除项目附件
            var attachments = oracle.GetList<AttachmentInfo>($"BusinessID='{businessId}' AND AttachmentType = '项目成果附件'");
            if (attachments.Any()) {
                foreach (var item in attachments) {
                    if (item.StateCode != 1) {
                        item.StateCode = 1;
                        oracle.UpdateOne(item);
                    }
                }
            }
            //移除项目的成果信息
            var contentInfo = oracle.GetById<EstateActualSurveyContentInfo>(businessId);
            contentInfo.ProjectResultInfo = null;
            contentInfo.BuildingTableInfo = null;
            contentInfo.DataCheckID = null;
            contentInfo.DataCheckState = 0;
            contentInfo.SurveyMasterName = null;
            contentInfo.SurveyMasterNo = null;
            contentInfo.SurveyMasterSureTime = null;

            oracle.UpdateOne(contentInfo);

            var baseBusinessInfo = BusinessBaseInfo.FromModel(baseInfo);
            baseBusinessInfo.SurveyCompanyNo = null;
            baseBusinessInfo.SurveyCompanyName = null;
            oracle.UpdateOne(baseBusinessInfo);
        }

        /// <summary>
        /// 从第4步返回第2步触发的事件
        /// </summary>
        /// <param name="baseInfo"></param>
        /// <param name="linkInfo"></param>
        private void BackRouteF4T2_RoutePassed(BusinessBaseInfoModel baseInfo, BusinessLinkInfoModel linkInfo) {
            //退回测绘单位，要移除注册测绘师确认的标记
            var businessId = baseInfo.ID;
            var oracle = new OracleDataService();

            //移除项目的成果信息
            var contentInfo = oracle.GetById<EstateActualSurveyContentInfo>(businessId);
            //contentInfo.DataCheckState = 2;//退回时不处理成果检查状态
            contentInfo.SurveyMasterName = null;
            contentInfo.SurveyMasterNo = null;
            contentInfo.SurveyMasterSureTime = null;

            oracle.UpdateOne(contentInfo);
        }
        /// <summary>
        /// 从第5步退回第4步触发的事件 
        /// </summary>
        /// <param name="baseInfo"></param>
        /// <param name="linkInfo"></param>
        private void BackRouteF5T4_RoutePassed(BusinessBaseInfoModel baseInfo, BusinessLinkInfoModel linkInfo) {
            //退回委托方，要移除已上传的测绘成果
            var businessId = baseInfo.ID;
            var oracle = new OracleDataService();

            //实核业务退回的时候不需要移除注册测绘师确认信息，只需要恢复签收前状态即可
            var contentInfo = oracle.GetById<EstateActualSurveyContentInfo>(businessId);
            contentInfo.DataCheckState = 1;

            oracle.UpdateOne(contentInfo);
        }

        #region Overrides of BusinessFlowBase
        /// <summary>
        /// 流程环节定义信息JSON
        /// </summary>
        /// <returns></returns>
        public override string FlowActionJSON {
            get {
                return JsonConvert.SerializeObject(FlowActionInfo.Actions.Select(a => new {
                    a.ID,
                    a.Name
                }));
            }
        }

        /// <summary>
        /// 是否包含测绘作业
        /// </summary>
        public override bool HasSurvey { get; } = true;

        /// <summary>
        /// 流程是否启用
        /// </summary>
        public override bool Enable => BusinessFlowConfig.EnableFlows.Contains(GetType().Name);

        /// <summary>
        /// 该流程是否需要协议备案
        /// </summary>
        public override bool NeedBDCProtocol { get; } = true;

        /// <summary>
        /// 该流程是否在办理业务中可见
        /// </summary>
        public override bool Visible => BusinessFlowConfig.VisibleFlows.Contains(GetType().Name);
        #endregion
    }
}
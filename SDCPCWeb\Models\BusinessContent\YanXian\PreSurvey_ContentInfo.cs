﻿using Newtonsoft.Json;
using SDCPCWeb.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace SDCPCWeb.Models.BusinessContent.YanXian {
    public class PreSurvey_ContentInfoModel {
        /// <summary>
        /// 主键ID，与BusinessBaseInfo表的ID关联的外键
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 宗地代码
        /// </summary>
        public string GroundCode { get; set; }
        /// <summary>
        /// 工程规划许可
        /// </summary>
        public string ProjectPlanPermission { get; set; }

        /// <summary>
        /// 测绘成果汇总信息
        /// </summary>
        public string ProjectResultInfo { get; set; }

        /// <summary>
        /// 楼盘表成果信息
        /// </summary>
        public string BuildingTableInfo { get; set; }

        /// <summary>
        /// 成果检查任务ID
        /// </summary>
        public string DataCheckID { get; set; }
        /// <summary>
        /// 成果检查状态 0 检查中  1 检查通过  2 检查不通过 3 备案审核中  -1 备案审核不通过 -2 已通过备案审核
        /// </summary>
        public int DataCheckState { get; set; }
        /// <summary>
        /// 注册测绘师姓名
        /// </summary>
        public string SurveyMasterName { get; set; }
        /// <summary>
        /// 注册测绘师身份证号
        /// </summary>
        public string SurveyMasterNo { get; set; }
        /// <summary>
        /// 注册测绘师确认时间
        /// </summary>
        public DateTime? SurveyMasterSureTime { get; set; }
        /// <summary>
        /// 测绘备案不通过的原因
        /// </summary>
        public string RejectMessage { get; set; }
        /// <summary>
        /// 竣工用地面积
        /// </summary>
        public string CompletionLandArea { get; set; }
        /// <summary>
        /// 竣工总建筑面积
        /// </summary>
        public string CompletionBuildingArea { get; set; }
    }
    public class PreSurvey_ContentInfo : PreSurvey_ContentInfoModel, IOracleDataTable {
        public PreSurvey_ContentInfoModel ToModel() {
            return JsonConvert.DeserializeObject<PreSurvey_ContentInfoModel>(JsonConvert.SerializeObject(this));
        }
    }
}
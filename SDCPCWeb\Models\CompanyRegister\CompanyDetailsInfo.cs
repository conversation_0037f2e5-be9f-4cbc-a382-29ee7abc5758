﻿using Newtonsoft.Json;
using SDCPCWeb.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace SDCPCWeb.Models.CompanyRegister {
    /// <summary>
    /// 单位详细信息
    /// </summary>
    public class CompanyDetailsInfoModel {
        /// <summary>
        /// ID
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 关联已通过的申请ID，或者变更ID
        /// </summary>
        public string RelationRequestId { get; set; }
        /// <summary>
        /// 单位性质
        /// </summary>
        public string CompanyNature { get; set; }
        /// <summary>
        /// 测绘最高资质等级
        /// </summary>
        public string QualificationLevel { get; set; }
        /// <summary>
        /// 工商注册号
        /// </summary>
        public string RegistrationNumber { get; set; }
        /// <summary>
        /// 办公场所面积
        /// </summary>
        public string OfficeArea { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Notes { get; set; }
        /// <summary>
        /// 附件信息 
        /// </summary>
        public string AttachmentInfo { get; set; }

    }

    public class CompanyDetailsInfo : CompanyDetailsInfoModel, IOracleDataTable {
        public CompanyDetailsInfoModel ToModel() {
            return JsonConvert.DeserializeObject<CompanyDetailsInfoModel>(JsonConvert.SerializeObject(this));
        }
    }
}
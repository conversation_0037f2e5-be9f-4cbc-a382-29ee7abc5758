﻿using Microsoft.VisualBasic;

namespace SDCPCWeb.Extensions {

    public static class StringExtensions {
        /// <summary>
        /// 字符串简体转繁体
        /// </summary>
        /// <param name="strSimple"></param>
        /// <returns></returns>
        public static string ToTraditionalChinese(this string strSimple) {
            string strTraditional = Microsoft.VisualBasic.Strings.StrConv(strSimple, Microsoft.VisualBasic.VbStrConv.TraditionalChinese, 0);
            return strTraditional;
        }

        /// <summary>
        /// 字符串繁体转简体
        /// </summary>
        /// <param name="strTraditional"></param>
        /// <returns></returns>
        public static string ToSimplifiedChinese(this string strTraditional) {
            string strSimple = Microsoft.VisualBasic.Strings.StrConv(strTraditional, VbStrConv.SimplifiedChinese, 0);
            return strSimple;
        }
    }
}
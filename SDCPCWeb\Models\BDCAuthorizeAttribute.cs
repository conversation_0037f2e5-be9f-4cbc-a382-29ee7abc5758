﻿using Microsoft.AspNet.Identity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Web;
using System.Web.Configuration;
using System.Web.Http;
using System.Web.Http.Controllers;
using System.Web.Http.Routing.Constraints;
using log4net;
using Newtonsoft.Json;
using SDCPCWeb.Models.Result;
using SDCPCWeb.Models.System;

namespace SDCPCWeb.Models {
    /// <summary>
    /// 邕e登登录
    /// </summary>
    public class BDCAuthorizeAttribute : AuthorizeAttribute {
        protected override bool IsAuthorized(HttpActionContext actionContext) {
            return Authorized(actionContext);
        }

        internal static bool Authorized(HttpActionContext actionContext) {
            //判断请求Cookie是否已登录邕e登
            Exception cookieException = null;
            Exception tokenException = null;
            var cookies = actionContext.Request.Headers.GetCookies();
            if (cookies.Any()) {
                if (cookies.First().Cookies.Any(c => c.Name == ".AspNet.ApplicationCookie")) {
                    var cookieVal = cookies.First().Cookies.First(c => c.Name == ".AspNet.ApplicationCookie").Value;
                    //存在此Cookie，去主站点获取一次用户信息，验证是否有效
                    using (var web = new WebClient() { Proxy = null, Encoding = Encoding.UTF8 }) {
                        web.Headers.Set(HttpRequestHeader.Cookie, $@".AspNet.ApplicationCookie={cookieVal}");
                        try {
                            var res = web.DownloadString($@"https://{ExternalApiConfig.LoginServer}/suapi/GetUserInfo");
                            if (res != "null") {
                                var userInfo = JObject.Parse(res);
                                actionContext.Request.Headers.Add("SDC-UserId", userInfo["UserId"].Value<string>());
                                actionContext.Request.Headers.Add("SDC-PersonName", userInfo["PersonName"].Value<string>());
                                actionContext.Request.Headers.Add("SDC-PersonNo", userInfo["PersonNo"].Value<string>());
                                actionContext.Request.Headers.Add("SDC-Phone", userInfo["UserName"].Value<string>());
                                actionContext.Request.Headers.Add("SDC-IsRealName", userInfo["IsRealName"].Value<bool>().ToString());
                                var user = userInfo.ToObject<UserInfo>();
                                user.Phone = userInfo["UserName"].Value<string>();
                                user.From = "BDCWeb";
                                actionContext.Request.Properties.Add("SDC-UserInfo", user);
                                return true;
                            }
                        }
                        catch (Exception e) {
                            //接口异常的话跳过返回失败吧
                            cookieException = e;
                        }
                    }
                    //return true; //先默认有效，但是Cookie会过期
                }
            }

            //如果是调试模式或者令牌模式，则会根据token来处理接口的调用判断是否登录邕e登
            var token = actionContext.Request.Headers.Authorization?.Parameter;
            if (!string.IsNullOrWhiteSpace(token)) {
                //调用接口传了token
                using (var web = new WebClient() { Proxy = null, Encoding = Encoding.UTF8 }) {
                    web.Headers.Set(HttpRequestHeader.Authorization, $@"bearer {token}");
                    try {
                        var res = web.DownloadString($@"https://{ExternalApiConfig.LoginServer}/suapi/GetUserInfo");
                        if (res != "null") {
                            var userInfo = JObject.Parse(res);
                            actionContext.Request.Headers.Add("SDC-UserId", userInfo["UserId"].Value<string>());
                            actionContext.Request.Headers.Add("SDC-PersonName", userInfo["PersonName"].Value<string>());
                            actionContext.Request.Headers.Add("SDC-PersonNo", userInfo["PersonNo"].Value<string>());
                            actionContext.Request.Headers.Add("SDC-Phone", userInfo["UserName"].Value<string>());
                            actionContext.Request.Headers.Add("SDC-IsRealName", userInfo["IsRealName"].Value<bool>().ToString());
                            var user = userInfo.ToObject<UserInfo>();
                            user.Phone = userInfo["UserName"].Value<string>();
                            user.From = "BDCWeb";
                            actionContext.Request.Properties.Add("SDC-UserInfo", user);
                            return true;
                        }
                    }
                    catch (Exception e) {
                        //接口异常的话跳过返回失败吧
                        tokenException = e;
                    }
                }
            }

            if (cookieException != null && tokenException != null) {
                throw cookieException;
            }

            return false;
        }

        internal static bool WebAdminAuthorized(HttpActionContext actionContext) {
            //判断请求Cookie是否已登录邕e登
            Exception cookieException = null;
            Exception tokenException = null;
            var cookies = actionContext.Request.Headers.GetCookies();
            if (cookies.Any()) {
                if (cookies.First().Cookies.Any(c => c.Name == ".AspNet.ApplicationCookie")) {
                    var cookieVal = cookies.First().Cookies.First(c => c.Name == ".AspNet.ApplicationCookie").Value;
                    //存在此Cookie，去主站点获取一次用户信息，验证是否有效
                    using (var web = new WebClient() { Proxy = null, Encoding = Encoding.UTF8 }) {
                        web.Headers.Set(HttpRequestHeader.Cookie, $@".AspNet.ApplicationCookie={cookieVal}");
                        try {
                            var res = web.DownloadString($@"https://{ExternalApiConfig.LoginServer}/suapi/GetUserExtention");
                            if (res != "null") {
                                var result = JObject.Parse(res).ToObject<ApiResult>();
                                if (result.StateCode == 1) {
                                    var userExtents = JObject.FromObject(result.Data);
                                    if (userExtents["IsWebAdmin"]?.Value<bool>() ?? false) {
                                        actionContext.Request.Properties.Add("SDC-BDCWebHeader", new {
                                            Method = "Cookies",Value = cookieVal
                                        });
                                        return true;
                                    }
                                }
                            }
                        }
                        catch (Exception e) {
                            cookieException = e;
                        }
                    }
                }
            }

            //如果是调试模式或者令牌模式，则会根据token来处理接口的调用判断是否登录邕e登
            var token = actionContext.Request.Headers.Authorization?.Parameter;
            if (!string.IsNullOrWhiteSpace(token)) {
                //调用接口传了token
                using (var web = new WebClient() { Proxy = null, Encoding = Encoding.UTF8 }) {
                    web.Headers.Set(HttpRequestHeader.Authorization, $@"bearer {token}");
                    try {
                        var res = web.DownloadString($@"https://{ExternalApiConfig.LoginServer}/suapi/GetUserExtention");
                        if (res != "null") {
                            var result = JObject.Parse(res).ToObject<ApiResult>();
                            if (result.StateCode == 1) {
                                var userExtents = JObject.FromObject(result.Data);
                                if (userExtents["IsWebAdmin"]?.Value<bool>() ?? false) {
                                    actionContext.Request.Properties.Add("SDC-BDCWebHeader", new {
                                        Method = "bearerToken",
                                        Value = token
                                    });
                                    return true;
                                }
                            }
                        }
                    }
                    catch (Exception e) {
                        tokenException = e;
                    }
                }
            }

            if (cookieException != null && tokenException != null) {
                throw cookieException;
            }

            return false;
        }
    }
}
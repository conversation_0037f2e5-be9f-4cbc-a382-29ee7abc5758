﻿using Newtonsoft.Json;
using SDCPCWeb.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace SDCPCWeb.Models.BusinessContent.GuiHuaDingDian {
    /// <summary>
    /// 拨地定桩业务内容信息
    /// </summary>
    public class MarkPointSurveyContentInfoModel {
        /// <summary>
        /// 主键ID，与BusinessBaseInfo表的ID关联的外键
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 工程建设项目编号
        /// </summary>
        public string ProjectNumber { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string ProjectName { get; set; }
        /// <summary>
        /// 项目地址
        /// </summary>
        public string ProjectAddress { get; set; }
        /// <summary>
        /// 范围检查任务ID
        /// </summary>
        public string DataCheckID { get; set; }
        /// <summary>
        /// 范围检查状态 0 审核中  1 审核通过  2 审核不通过
        /// </summary>
        public int DataCheckState { get; set; }
        /// <summary>
        /// 范围下载任务ID
        /// </summary>
        public string DownLoadID { get; set; }
        /// <summary>
        /// 范围下载状态 0 审核中  1 下载成功  2 下载失败
        /// </summary>
        public int DownLoadState { get; set; }
        /// <summary>
        /// 检查不通过原因
        /// </summary>
        public string FailedReason { get; set; }
        /// <summary>
        /// 汇交成果检查ID
        /// </summary>
        public string AchievementCheckID { get; set; }
        /// <summary>
        /// 汇交成果检查状态
        /// </summary>
        public int AchievementCheckState { get; set; }
        /// <summary>
        /// 注册测绘师姓名
        /// </summary>
        public string SurveyMasterName { get; set; }
        /// <summary>
        /// 注册测绘师身份证号
        /// </summary>
        public string SurveyMasterNo { get; set; }
        /// <summary>
        /// 注册测绘师确认时间
        /// </summary>
        public DateTime? SurveyMasterSureTime { get; set; }
        /// <summary>
        /// 申请下载的图层
        /// </summary>
        public string ApplyData { get; set; }
    }
    /// <summary>
    /// 
    /// </summary>
    public class MarkPointSurveyContentInfo : MarkPointSurveyContentInfoModel, IOracleDataTable {
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public MarkPointSurveyContentInfoModel ToModel() {
            return JsonConvert.DeserializeObject<MarkPointSurveyContentInfoModel>(JsonConvert.SerializeObject(this));
        }

        /// <summary>
        /// 从基类模型对象转换为可入库的数据对象
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public static MarkPointSurveyContentInfo FromModel(MarkPointSurveyContentInfoModel model) {
            return JsonConvert.DeserializeObject<MarkPointSurveyContentInfo>(JsonConvert.SerializeObject(model));
        }
    }
}
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Configuration;
using System.Web.Mvc;
using Newtonsoft.Json;
using SDCPCWeb.Services;

namespace SDCPCWeb.Controllers {
    public class HomeController : Controller {
        public ActionResult Index() {
            ViewBag.Title = "南宁市多测合一信息系统API服务";
            ViewBag.TestMessage = GetTestMessage();
            return View();
        }

        public async Task<ActionResult> CreateSwaggerMarkdownFile() {
            var url = $"{Request.Url.Scheme}://{Request.Url.Authority}/swagger/docs/v1";
            var bytes = await SwaggerService.CreateMarkdownFile(url);
            return File(bytes, "text/plain", Guid.NewGuid().ToString("N") + ".md");
        }

        private string GetTestMessage() {
            var sb = new StringBuilder();

            //数据链接是否成功

            using (var oracle = new OracleDataService()) {
                var result = oracle.ExecuteQuerySql("Select '数据库连接成功' \"Message\" From dual");
                sb.Append($"数据库连接状态：{result.Rows[0][0]}");
            }

            return Environment.NewLine + sb;
        }
    }
}

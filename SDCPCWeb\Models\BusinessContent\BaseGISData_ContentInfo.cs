﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Newtonsoft.Json;
using SDCPCWeb.Models.BusinessFlow;
using SDCPCWeb.Services;

namespace SDCPCWeb.Models.BusinessContent {
    /// <summary>
    /// 基础地理信息数据业务内容信息
    /// </summary>
    public class BaseGISData_ContentInfoModel {
        /// <summary>
        /// 主键ID，与BusinessBaseInfo表的ID关联的外键
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string ProjectName { get; set; }
        /// <summary>
        /// 立项依据
        /// </summary>
        public string ProjectBasis { get; set; }
        /// <summary>
        /// 项目范围
        /// </summary>
        public string ProjectScope { get; set; }
        /// <summary>
        /// 范围检查任务ID
        /// </summary>
        public string DataCheckID { get; set; }
        /// <summary>
        /// 范围检查状态 0 审核中  1 审核通过  2 审核不通过
        /// </summary>
        public int DataCheckState { get; set; }
        /// <summary>
        /// 基础数据提提取任务ID
        /// </summary>
        public string DownLoadID { get; set; }
        /// <summary>
        /// 基础数据提提取状态 0 未提取/正在提套取  1 数据提取成功  2 下载失败  3 科室审核确认中  4 科室审核不通过
        /// </summary>
        public int DownLoadState { get; set; }
        /// <summary>
        /// 检查不通过原因
        /// </summary>
        public string FailedReason { get; set; }

        /// <summary>
        /// 申请下载的图层
        /// </summary>
        public string ApplyData { get; set; }
    }

    public class BaseGISData_ContentInfo : BaseGISData_ContentInfoModel, IOracleDataTable {
        public BaseGISData_ContentInfoModel ToModel() {
            return JsonConvert.DeserializeObject<BaseGISData_ContentInfoModel>(JsonConvert.SerializeObject(this));
        }

        /// <summary>
        /// 从基类模型对象转换为可入库的数据对象
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public static BaseGISData_ContentInfo FromModel(BaseGISData_ContentInfoModel model) {
            return JsonConvert.DeserializeObject<BaseGISData_ContentInfo>(JsonConvert.SerializeObject(model));
        }
    }
}
﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text.RegularExpressions;
using System.Web;
using Hangfire.Console;
using Hangfire.Server;
using SDCPCWeb.Services;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Oracle.ManagedDataAccess.Client;
using SDCPCWeb.Models.BusinessFlow;

namespace SDCPCWeb.Models.EstateConstruction {
    /// <summary>
    /// 不动产测绘项目管理
    /// </summary>
    public class EstateProjectInfoModel {
        /// <summary>
        /// 唯一标识码
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 建设工程规划许可证号(应与ID一致)
        /// </summary>
        public string PlanCerificateCode { get; set; }
        /// <summary>
        /// 放线测量业务ID
        /// </summary>
        public string PutLineSurveyID { get; set; }
        /// <summary>
        /// 放线测量成果应用状态
        /// </summary>
        public SurveyState PutLineSurveyState { get; set; }
        /// <summary>
        /// 预测绘测量业务ID
        /// </summary>
        public string PreSurveyID { get; set; }
        /// <summary>
        /// 预测绘成果备案状态
        /// </summary>
        public SurveyState PreSurveyState { get; set; }
        /// <summary>
        /// 条件核实测量业务ID
        /// </summary>
        public string CheckConditionSurveyID { get; set; }
        /// <summary>
        /// 规划核实状态
        /// </summary>
        public SurveyState CheckConditionSurveyState { get; set; }
        /// <summary>
        /// 实测绘测量业务ID
        /// </summary>
        public string RealSurveyID { get; set; }
        /// <summary>
        /// 实测绘成果备案状态
        /// </summary>
        public SurveyState RealSurveyState { get; set; }

    }

    /// <summary>
    /// 不动产测绘项目管理
    /// </summary>
    public class EstateProjectInfo : EstateProjectInfoModel, IOracleDataTable {
        /// <summary>
        /// 转换为基类模型对象
        /// </summary>
        /// <returns></returns>
        public EstateProjectInfoModel ToModel() {
            return JsonConvert.DeserializeObject<EstateProjectInfoModel>(JsonConvert.SerializeObject(this));
        }

        /// <summary>
        /// 从基类模型对象转换为可入库的数据对象
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public static EstateProjectInfo FromModel(EstateProjectInfoModel model) {
            return JsonConvert.DeserializeObject<EstateProjectInfo>(JsonConvert.SerializeObject(model));
        }

        /// <summary>
        /// 保存更新项目状态
        /// </summary>
        /// <param name="code"></param>
        /// <param name="surveyClass"></param>
        /// <param name="state"></param>
        /// <param name="surveyId"></param>
        internal static void SaveProjectState(string code, SurveyClass surveyClass, SurveyState state, string surveyId) {
            if (string.IsNullOrWhiteSpace(code)) {
                return;
            }
            /*因新版工规证号格式有变化，此判断去掉
            //工规证是15位纯数字
            if (!Regex.IsMatch(code, @"^\d{15}$")) {
                return;
            }
            */
            using (var oracle = new OracleDataService()) {
                var record = GetEstateProjectInfo(surveyId, code, surveyClass); //oracle.GetById<EstateProjectInfo>(code);
                var forupdate = true;
                if (record == null) {
                    record = new EstateProjectInfo() {
                        //ID = code,
                        PlanCerificateCode = code,
                        PutLineSurveyID = null,
                        PutLineSurveyState = SurveyState.NotSurvey,
                        PreSurveyID = null,
                        PreSurveyState = SurveyState.NotSurvey,
                        CheckConditionSurveyID = null,
                        CheckConditionSurveyState = SurveyState.NotSurvey,
                        RealSurveyID = null,
                        RealSurveyState = SurveyState.NotSurvey
                    };
                    forupdate = false;
                }

                switch (surveyClass) {
                    case SurveyClass.Putline:
                        record.PutLineSurveyID = surveyId;
                        if (record.PutLineSurveyState == SurveyState.CompleteAudit && state == SurveyState.CompleteSurvey) {
                            record.PutLineSurveyState = SurveyState.CompleteCorrection;
                        }
                        else {
                            record.PutLineSurveyState = state;
                        }
                        break;
                    case SurveyClass.PreSurvey:
                        record.PreSurveyID = surveyId;
                        if (record.PreSurveyState == SurveyState.CompleteAudit && state == SurveyState.CompleteSurvey) {
                            record.PreSurveyState = SurveyState.CompleteCorrection;
                        }
                        else {
                            var business = oracle.GetById<BusinessBaseInfo>(surveyId);
                            if (business?.BusinessClass == nameof(RealEstatePreCheckSurveyAutoFlow)) {
                                record.PutLineSurveyState = state;
                            }

                            record.PreSurveyState = state;
                        }
                        break;
                    case SurveyClass.CheckConditionSurvey:
                        record.CheckConditionSurveyID = surveyId;
                        if (record.CheckConditionSurveyState == SurveyState.CompleteAudit && state == SurveyState.CompleteSurvey) {
                            record.CheckConditionSurveyState = SurveyState.CompleteCorrection;
                        }
                        else {
                            record.CheckConditionSurveyState = state;
                        }
                        break;
                    case SurveyClass.RealSurvey:
                        record.RealSurveyID = surveyId;
                        if (record.RealSurveyState == SurveyState.CompleteAudit && state == SurveyState.CompleteSurvey) {
                            record.RealSurveyState = SurveyState.CompleteCorrection;
                        }
                        else {
                            record.RealSurveyState = state;
                        }
                        break;
                }

                if (forupdate) {
                    oracle.UpdateOne(record);
                }
                else {
                    record.ID = CreateEstateProjectInfoId(record.PlanCerificateCode);
                    oracle.InsertOne(record);
                }

            }
        }

        /// <summary>
        /// 重新设置项目状态
        /// </summary>
        /// <param name="code"></param>
        /// <param name="surveyClass"></param>
        /// <param name="state"></param>
        internal static void ReSetProjectState(string businessId, string code, SurveyClass surveyClass, SurveyState state, PerformContext console) {
            if (string.IsNullOrWhiteSpace(businessId)) {
                return;
            }

            if (string.IsNullOrWhiteSpace(code)) {
                return;
            }

            using (var oracle = new OracleDataService()) {
                var estateProjectInfo = GetEstateProjectInfo(businessId, code, surveyClass); //oracle.GetById<EstateProjectInfo>(code);
                if (estateProjectInfo == null) {
                    console?.WriteLine("获取的项目数据为空，无需处理");
                    return;
                }

                switch (surveyClass) {
                    case SurveyClass.Putline: { 
                        //判断EstateProjectInfo表的相关状态并重置状态
                        if (estateProjectInfo.PutLineSurveyState != state) {
                            string updateSql =
                                $"UPDATE EstateProjectInfo SET PutLineSurveyState=:state WHERE PlanCerificateCode=:code and PutLineSurveyID=:businessId";
                            oracle.ExecuteUpdateSql(updateSql,
                                new OracleParameter(":code", OracleDbType.Varchar2) { Value = code },
                                new OracleParameter(":state", OracleDbType.Int32) { Value = state },
                                new OracleParameter(":businessId", OracleDbType.Varchar2) { Value = businessId });
                        }
                        break;
                    }
                    
                    case SurveyClass.PreSurvey: {
                            //判断EstateProjectInfo表的相关状态并重置状态
                            if (estateProjectInfo.PreSurveyState != state) {
                                string updateSql =
                                    $"UPDATE EstateProjectInfo SET PreSurveyState=:state WHERE PlanCerificateCode=:code and PreSurveyID=:businessId";
                                oracle.ExecuteUpdateSql(updateSql,
                                    new OracleParameter(":code", OracleDbType.Varchar2) { Value = code },
                                    new OracleParameter(":state", OracleDbType.Int32) { Value = state },
                                    new OracleParameter(":businessId", OracleDbType.Varchar2) { Value = businessId });
                            }
                            break;
                    }
                    
                    case SurveyClass.CheckConditionSurvey: {
                            //判断EstateProjectInfo表的相关状态并重置状态
                            if (estateProjectInfo.CheckConditionSurveyState != state) {
                                string updateSql =
                                    $"UPDATE EstateProjectInfo SET CheckConditionSurveyState=:state WHERE PlanCerificateCode=:code and CheckConditionSurveyID=:businessId";
                                oracle.ExecuteUpdateSql(updateSql,
                                    new OracleParameter(":code", OracleDbType.Varchar2) { Value = code },
                                    new OracleParameter(":state", OracleDbType.Int32) { Value = state },
                                new OracleParameter(":businessId", OracleDbType.Varchar2) { Value = businessId });
                            }
                            break;
                    }
                        
                    case SurveyClass.RealSurvey: {
                        //判断EstateProjectInfo表的相关状态并重置状态
                        if (estateProjectInfo.RealSurveyState != state) {
                            string updateSql =
                                $"UPDATE EstateProjectInfo SET RealSurveyState=:state WHERE PlanCerificateCode=:code and RealSurveyID=:businessId";
                            oracle.ExecuteUpdateSql(updateSql,
                                new OracleParameter(":code", OracleDbType.Varchar2) { Value = code },
                                new OracleParameter(":state", OracleDbType.Int32) { Value = state },
                                new OracleParameter(":businessId", OracleDbType.Varchar2) { Value = businessId });
                        }
                        break;
                    }
                }
            }
        }


        /// <summary>
        /// 完成放线测量成果验收，更新不动产项目状态
        /// </summary>
        /// <param name="plans"></param>
        /// <param name="surveyId"></param>
        /// <param name="state"></param>
        /// <param name="console"></param>
        [DisplayName("完成放线测量成果验收，更新不动产项目状态")]
        public static void SaveEstateProjectPutlineSurveyStates(List<ProjectPlanPermissionInfo> plans, string surveyId,
            SurveyState state, PerformContext console) {
            if (plans != null && plans.Any() && !string.IsNullOrWhiteSpace(surveyId)) {
                console?.WriteLine($"共更新{plans.Count}条项目的放线测量状态为{state.ToString()}");
                foreach (var projectPlanPermissionInfo in plans) {
                    var planCode = projectPlanPermissionInfo.Code;
                    console.WriteLine($"更新{planCode}");
                    SaveProjectState(planCode, SurveyClass.Putline, state, surveyId);
                    console.WriteLine("更新完成");
                }
            }
        }

        /// <summary>
        /// 完成不动产预测绘成果验收，更新不动产项目状态
        /// </summary>
        /// <param name="plans"></param>
        /// <param name="surveyId"></param>
        /// <param name="state"></param>
        /// <param name="console"></param>
        [DisplayName("完成不动产预测绘成果验收，更新不动产项目状态")]
        public static void SaveEstateProjectPreSurveyStates(List<ProjectPlanPermissionInfo> plans, string surveyId,
            SurveyState state, PerformContext console) {
            if (plans != null && plans.Any() && !string.IsNullOrWhiteSpace(surveyId)) {
                console?.WriteLine($"共更新{plans.Count}条项目的不动产预测绘状态为{state.ToString()}");
                foreach (var projectPlanPermissionInfo in plans) {
                    var planCode = projectPlanPermissionInfo.Code;
                    console.WriteLine($"更新{planCode}");
                    SaveProjectState(planCode, SurveyClass.PreSurvey, state, surveyId);
                    console.WriteLine("更新完成");
                }
            }
        }

        /// <summary>
        /// 完成不动产预测绘成果验收，更新不动产项目状态
        /// </summary>
        /// <param name="plans"></param>
        /// <param name="surveyId"></param>
        /// <param name="state"></param>
        /// <param name="console"></param>
        [DisplayName("完成不动产实核测绘成果验收，更新不动产项目状态")]
        public static void SaveEstateProjectRealSurveyStates(List<ProjectPlanPermissionInfo> plans, string surveyId,
            SurveyState state, PerformContext console) {
            if (plans != null && plans.Any() && !string.IsNullOrWhiteSpace(surveyId)) {
                console?.WriteLine($"共更新{plans.Count}条项目的不动产实核测绘状态为{state.ToString()}");
                foreach (var projectPlanPermissionInfo in plans) {
                    var planCode = projectPlanPermissionInfo.Code;
                    console.WriteLine($"更新{planCode}");
                    SaveProjectState(planCode, SurveyClass.CheckConditionSurvey, state, surveyId);
                    SaveProjectState(planCode, SurveyClass.RealSurvey, state, surveyId);
                    console.WriteLine("更新完成");
                }
            }
        }

        /// <summary>
        /// 批量完成不动产预测绘成果验收，更新不动产项目状态
        /// </summary>
        /// <param name="surveyId"></param>
        /// <param name="plansList"></param>
        [DisplayName("批量完成不动产预测绘成果验收，更新不动产项目状态")]
        public static void BatchSaveEstateProjectSurveyStates(string surveyId, List<Tuple<SurveyClass, List<ProjectPlanPermissionInfo>>> plansList, SurveyState state, PerformContext console) {
            foreach (var plans in plansList) {
                switch (plans.Item1) {
                    case SurveyClass.PreSurvey: {
                        SaveEstateProjectPreSurveyStates(plans.Item2, surveyId, state, console);
                        break;
                    }
                    case SurveyClass.Putline: {
                        SaveEstateProjectPutlineSurveyStates(plans.Item2, surveyId, state, console);
                        break;
                    }
                    case SurveyClass.RealSurvey: {
                        SaveEstateProjectRealSurveyStates(plans.Item2, surveyId, state, console);
                        break;
                    }
                }
            }
        }

        /// <summary>
        /// 云平台退回业务，重置不动产项目状态
        /// </summary>
        /// <param name="projectPlanPermission"></param>
        /// <param name="surveyClass"></param>
        /// <param name="console"></param>
        [DisplayName("云平台退回业务，重置不动产项目状态")]
        public static void ResetEstateProjectStates(string businessId, string projectPlanPermission, SurveyClass surveyClass, PerformContext console) {
            var planInfos = JArray.Parse(projectPlanPermission); //JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(projectPlanPermission);
            foreach (var planInfo in planInfos) {
                var code = planInfo["Code"].ToString();
                console.WriteLine($"更新{code}");
                ReSetProjectState(businessId, code, surveyClass, SurveyState.Invalid, console);
                console.WriteLine("更新完成");
            }
        }

        #region 内部方法

        /// <summary>
        /// 根据工规证号生成id
        /// </summary>
        /// <param name="code">工规证号</param>
        /// <returns></returns>
        public static string CreateEstateProjectInfoId(string code) {
            using (var oracle = new OracleDataService()) {
                string sql =
                    $"SELECT MAX(case when instr(ID, '-') > 0 then TO_NUMBER(substr(ID, instr(ID, '-') + 1, LENGTH(ID) - instr(ID, '-'))) else 0 end) as num FROM ESTATEPROJECTINFO where PLANCERIFICATECODE =:code";
                var dt = oracle.ExecuteQuerySql(sql, new OracleParameter(":code", OracleDbType.Varchar2) { Value = code });
                int num = 0;
                if (dt.Rows.Count > 0) {
                    num = dt.Rows[0][0] == DBNull.Value ? 0 : Convert.ToInt32(dt.Rows[0][0].ToString());
                }

                return $"{code}-{(num + 1).ToString().PadLeft(3, '0')}";
            }
        }

        /// <summary>
        /// 获取工规证与业务绑定表的信息
        /// </summary>
        /// <param name="businessId"></param>
        /// <param name="code"></param>
        /// <param name="surveyClass"></param>
        /// <returns></returns>
        public static EstateProjectInfo GetEstateProjectInfo(string businessId, string code, SurveyClass surveyClass) {
            using (var oracle = new OracleDataService()) {
                string whereSql =
                    $"PLANCERIFICATECODE = :code and ";

                switch (surveyClass) {
                    case SurveyClass.PreSurvey: {
                        whereSql += $"PreSurveyID = :businessId";
                        break;
                    }
                    case SurveyClass.CheckConditionSurvey: {
                        whereSql += $"CheckConditionSurveyID = :businessId";
                        break;
                    }
                    case SurveyClass.Putline: {
                        whereSql += $"PutLineSurveyID = :businessId";
                        break;
                    }
                    case SurveyClass.RealSurvey: {
                        whereSql += $"RealSurveyID = :businessId";
                        break;
                    }
                }

                var list = oracle.GetList<EstateProjectInfo>(whereSql, 
                    new OracleParameter(":code", OracleDbType.Varchar2) { Value = code },
                    new OracleParameter(":businessId", OracleDbType.Varchar2) { Value = businessId });

                return list.FirstOrDefault();
            }
        }

        /// <summary>
        /// 获取工规证号信息
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        public static EstateProjectInfo GetEstateProjectInfo(string code) {
            using (var oracle = new OracleDataService()) {
                string whereSql =
                    $"PLANCERIFICATECODE = :code";

                var list = oracle.GetList<EstateProjectInfo>(whereSql,
                    new OracleParameter(":code", OracleDbType.Varchar2) { Value = code });

                return list.FirstOrDefault();
            }
        }

        /// <summary>
        /// 批量获取工规证号信息
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        public static List<EstateProjectInfo> GetEstateProjectInfos(string code) {
            using (var oracle = new OracleDataService()) {
                string whereSql =
                    $"PLANCERIFICATECODE = :code";

                var list = oracle.GetList<EstateProjectInfo>(whereSql,
                    new OracleParameter(":code", OracleDbType.Varchar2) { Value = code });

                return list;
            }
        }
        #endregion
    }

    /// <summary>
    /// 测绘成果应用状态
    /// </summary>
    public enum SurveyState {
        /// <summary>
        /// 测绘成果尚未完成
        /// </summary>
        NotSurvey = 0,
        /// <summary>
        /// 测绘成果已完成且已验收
        /// </summary>
        CompleteSurvey = 1,
        /// <summary>
        /// 测绘成果已完成审核或备案
        /// </summary>
        CompleteAudit = 2,
        /// <summary>
        /// 测绘成果已完成修正或变更且已验收
        /// </summary>
        CompleteCorrection = 3,
        /// <summary>
        /// 测绘成果已被置为无效
        /// </summary>
        Invalid = -1,
        /// <summary>
        /// 未知状态
        /// </summary>
        Unknown = 99
    }

    /// <summary>
    /// 关联测绘类型
    /// </summary>
    public enum SurveyClass {
        /// <summary>
        /// 放线测量
        /// </summary>
        Putline,
        /// <summary>
        /// 不动产预测绘
        /// </summary>
        PreSurvey,
        /// <summary>
        /// 条件核实测量
        /// </summary>
        CheckConditionSurvey,
        /// <summary>
        /// 不动产实测绘
        /// </summary>
        RealSurvey
    }
}
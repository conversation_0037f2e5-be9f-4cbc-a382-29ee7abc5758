
# 接口1、创建附件

## 接口信息

- **所属控制器：** AttachmentManage

- **接口地址：** /sdcapi/AttachmentManage/CreateAttachmentRequest

- **请求方式：**POST  

- **Content-Type：**application/json

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|request|AttachmentInfo类||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口2、附件上传（单个文件）

## 接口信息

- **所属控制器：** AttachmentManage

- **接口地址：** /sdcapi/AttachmentManage/AttachmentUploadRequest

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口3、根据id查看图片

## 接口信息

- **所属控制器：** AttachmentManage

- **接口地址：** /sdcapi/AttachmentManage/ShowImage

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  object

# 接口4、根据id查看图片缩略图

## 接口信息

- **所属控制器：** AttachmentManage

- **接口地址：** /sdcapi/AttachmentManage/ShowThumbnail

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  object

# 接口5、根据id预览PDF

## 接口信息

- **所属控制器：** AttachmentManage

- **接口地址：** /sdcapi/AttachmentManage/ShowPDF

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  object

# 接口6、附件下载

## 接口信息

- **所属控制器：** AttachmentManage

- **接口地址：** /sdcapi/AttachmentManage/AttachmentDownloadRequest

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  object

# 接口7、获取附件下载Key，使用Key可以调用匿名下载接口，一个Key的有效期是5分钟

## 接口信息

- **所属控制器：** AttachmentManage

- **接口地址：** /sdcapi/AttachmentManage/GetAttachmentDownloadKey

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口8、

## 接口信息

- **所属控制器：** AttachmentManage

- **接口地址：** /sdcapi/AttachmentManage/DownloadAttachmentWithKey/{key}/{filename}.{ext}

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|key|string||True|
|filename|string||True|
|ext|string||True|

- **响应类型**：

  object

# 接口9、基础数据下载前验证

## 接口信息

- **所属控制器：** AttachmentManage

- **接口地址：** /sdcapi/AttachmentManage/GetBaseDataDownloadState

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|
|validateNumber|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口10、获取随机码

## 接口信息

- **所属控制器：** AttachmentManage

- **接口地址：** /sdcapi/AttachmentManage/GetValidateNumber

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口11、基础数据附件下载

## 接口信息

- **所属控制器：** AttachmentManage

- **接口地址：** /sdcapi/AttachmentManage/BaseDataDownloadRequest

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|
|validateNumber|string||True|

- **响应类型**：

  object

# 接口12、附件锁定

## 接口信息

- **所属控制器：** AttachmentManage

- **接口地址：** /sdcapi/AttachmentManage/AttachmentLockRequest

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口13、获取立项依据文件内容

## 接口信息

- **所属控制器：** AttachmentManage

- **接口地址：** /sdcapi/AttachmentManage/GetAttachementFileStr

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口14、附件逻辑删除

## 接口信息

- **所属控制器：** AttachmentManage

- **接口地址：** /sdcapi/AttachmentManage/AttachmentDeleteRequest

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口15、获取附件列表

## 接口信息

- **所属控制器：** AttachmentManage

- **接口地址：** /sdcapi/AttachmentManage/GetAttachmentListRequest

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口16、系统附件下载

## 接口信息

- **所属控制器：** AttachmentManage

- **接口地址：** /sdcapi/AttachmentManage/SystemAttachmentDownload

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|fileName|string||True|

- **响应类型**：

  object

# 接口17、根据单位id查看资质证书副本

## 接口信息

- **所属控制器：** AttachmentManage

- **接口地址：** /sdcapi/AttachmentManage/ShowQualificationImage

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  object

# 接口18、获取审核业务列表，带分页

## 接口信息

- **所属控制器：** BaseGISDataDownloadAudit

- **接口地址：** /internal/BaseGISDataDownloadAudit/GetBusinessList

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|pageIndex|integer||False|
|pageSize|integer||False|

- **响应类型**：

  #/definitions/PageResult[BusinessBaseInfo]

# 接口19、获取业务详情

## 接口信息

- **所属控制器：** BaseGISDataDownloadAudit

- **接口地址：** /internal/BaseGISDataDownloadAudit/GetProjectById

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  #/definitions/BaseSurveyDataDownloadProject

# 接口20、签收审核业务

## 接口信息

- **所属控制器：** BaseGISDataDownloadAudit

- **接口地址：** /internal/BaseGISDataDownloadAudit/SignBusiness

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口21、通过审核

## 接口信息

- **所属控制器：** BaseGISDataDownloadAudit

- **接口地址：** /internal/BaseGISDataDownloadAudit/AcceptBusiness

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口22、驳回申请，关闭业务

## 接口信息

- **所属控制器：** BaseGISDataDownloadAudit

- **接口地址：** /internal/BaseGISDataDownloadAudit/RejectBusiness

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|
|reason|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口23、获取拨地定桩项目范围审核列表

## 接口信息

- **所属控制器：** BaseGISDataDownloadAudit

- **接口地址：** /internal/BaseGISDataDownloadAudit/GetMarkPointCheckScopeList

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|pageIndex|integer||False|
|pageSize|integer||False|

- **响应类型**：

  #/definitions/PageResult[BusinessBaseInfo]

# 接口24、项目范围通过审核

## 接口信息

- **所属控制器：** BaseGISDataDownloadAudit

- **接口地址：** /internal/BaseGISDataDownloadAudit/AcceptProjectScope

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口25、驳回项目范围

## 接口信息

- **所属控制器：** BaseGISDataDownloadAudit

- **接口地址：** /internal/BaseGISDataDownloadAudit/RejectProjectScope

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|
|reason|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口26、业务创建

## 接口信息

- **所属控制器：** BusinessFlowManage

- **接口地址：** /sdcapi/BusinessFlow/CreateNewBusiness

- **请求方式：**POST  

- **Content-Type：**application/json

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|request|BusinessBaseInfo类||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口27、业务保存

## 接口信息

- **所属控制器：** BusinessFlowManage

- **接口地址：** /sdcapi/BusinessFlow/SaveBusiness

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|
|model|||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口28、获取工程规划许可证信息

## 接口信息

- **所属控制器：** BusinessFlowManage

- **接口地址：** /sdcapi/BusinessFlow/GetProjectPlanPermissionInfo

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|businessId|string||True|
|businessClass|string||True|
|code|string|许可证号或者项目编号|True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口29、获取已排序的测绘单位列表

## 接口信息

- **所属控制器：** BusinessFlowManage

- **接口地址：** /sdcapi/BusinessFlow/GetSurveyCompanyList

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|businessClass|string||True|
|companyname|string||True|
|pageIndex|integer||False|
|pageSize|integer||False|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口30、委托测绘单位并提交业务

## 接口信息

- **所属控制器：** BusinessFlowManage

- **接口地址：** /sdcapi/BusinessFlow/EntrustSurveyCompany

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|businessId|string||True|
|actionId|string||True|
|creditCode|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口31、重新委托测绘单位，回到委托测绘单位环节

## 接口信息

- **所属控制器：** BusinessFlowManage

- **接口地址：** /sdcapi/BusinessFlow/EntrustSurveyCompanyAgain

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|businessId|string||True|
|actionId|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口32、项目创建者主动关闭业务

## 接口信息

- **所属控制器：** BusinessFlowManage

- **接口地址：** /sdcapi/BusinessFlow/CloseBusinessProject

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|
|closeReason|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口33、测绘成果确认验收

## 接口信息

- **所属控制器：** BusinessFlowManage

- **接口地址：** /sdcapi/BusinessFlow/AcceptSurveyProjectResult

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口34、根据宗地号获取楼盘表信息

## 接口信息

- **所属控制器：** BusinessFlowManage

- **接口地址：** /sdcapi/BusinessFlow/GetBuildingTableByZDH

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|zdh|string|宗地号|True|
|chzt|integer|测绘状态：1预测绘；2实测绘|False|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口35、蓝线图、拨地定桩汇交成果

## 接口信息

- **所属控制器：** BusinessFlowManage

- **接口地址：** /sdcapi/BusinessFlow/PostAchievementResult

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口36、提交测绘成果到多测合一系统

## 接口信息

- **所属控制器：** BusinessFlowManage

- **接口地址：** /sdcapi/BusinessFlow/PostSurveyProjectResult

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string|业务ID|True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口37、上传项目范围

## 接口信息

- **所属控制器：** BusinessFlowManage

- **接口地址：** /sdcapi/BusinessFlow/PostProjectScopeResult

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|
|applyData|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口38、启动EPS进行数据切图

## 接口信息

- **所属控制器：** BusinessFlowManage

- **接口地址：** /sdcapi/BusinessFlow/PostEpsDownLoadResult

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口39、获取成果检查状态

## 接口信息

- **所属控制器：** BusinessFlowManage

- **接口地址：** /sdcapi/BusinessFlow/GetSurveyResultCheckState

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|checkid|string||True|
|businessClass|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口40、获取项目范围检查状态

## 接口信息

- **所属控制器：** BusinessFlowManage

- **接口地址：** /sdcapi/BusinessFlow/GetProjectScopeCheckState

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|checkid|string||True|
|businessClass|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口41、获取EPS下载状态

## 接口信息

- **所属控制器：** BusinessFlowManage

- **接口地址：** /sdcapi/BusinessFlow/GetEPSDownLoadState

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|downloadid|string||True|
|businessClass|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口42、获取汇交成果检查状态

## 接口信息

- **所属控制器：** BusinessFlowManage

- **接口地址：** /sdcapi/BusinessFlow/GetAchievementCheckState

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|checkid|string||True|
|businessClass|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口43、注册测绘师刷脸确认测绘成果

## 接口信息

- **所属控制器：** BusinessFlowManage

- **接口地址：** /sdcapi/BusinessFlow/ConfirmSurveyResult

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|
|actionId|string||True|
|certifyId|string|刷脸ID|False|
|certifyType|integer|刷脸类型，1公安认证，2支付宝认证|False|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口44、下载错误报告

## 接口信息

- **所属控制器：** BusinessFlowManage

- **接口地址：** /sdcapi/BusinessFlow/DownloadErrorReport

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|dataCheckId|string||True|

- **响应类型**：

  object

# 接口45、判断当前用户是否是测绘单位的注册测绘师

## 接口信息

- **所属控制器：** BusinessFlowManage

- **接口地址：** /sdcapi/BusinessFlow/IsSurveyMaster

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：


- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口46、获取测绘成果确认列表

## 接口信息

- **所属控制器：** BusinessFlowManage

- **接口地址：** /sdcapi/BusinessFlow/GetSurveyProjectResultConfirmList

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|pageIndex|integer||False|
|pageSize|integer||False|

- **响应类型**：

  #/definitions/PageResult[SurveyProjectResultListItem]

# 接口47、根据ID获取测绘项目详情

## 接口信息

- **所属控制器：** BusinessFlowManage

- **接口地址：** /sdcapi/BusinessFlow/GetSurveyProjectResultByID

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口48、获取PDF附件

## 接口信息

- **所属控制器：** BusinessFlowManage

- **接口地址：** /sdcapi/BusinessFlow/GetPDFAttachment

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  object

# 接口49、使用注册测绘师授权确认测绘成果，只有本单位的人员才能调用此接口

## 接口信息

- **所属控制器：** BusinessFlowManage

- **接口地址：** /sdcapi/BusinessFlow/ConfirmSurveyResultUsingSurveyMasterAuth

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|
|actionId|string||True|
|authid|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口50、业务签收

## 接口信息

- **所属控制器：** BusinessFlowManage

- **接口地址：** /sdcapi/BusinessFlow/AcceptBusiness

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口51、业务提交

## 接口信息

- **所属控制器：** BusinessFlowManage

- **接口地址：** /sdcapi/BusinessFlow/SubmitBusiness

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|
|actionId|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口52、业务回退

## 接口信息

- **所属控制器：** BusinessFlowManage

- **接口地址：** /sdcapi/BusinessFlow/BackBusiness

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|
|actionId|string||True|
|backReason|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口53、根据业务ID获取业务信息

## 接口信息

- **所属控制器：** BusinessFlowManage

- **接口地址：** /sdcapi/BusinessFlow/GetBusinessByID

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口54、查询我的业务列表

## 接口信息

- **所属控制器：** BusinessFlowManage

- **接口地址：** /sdcapi/BusinessFlow/GetMyBusinessList

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|q|string||False|
|stateCode|integer|细化状态值，-1为当前类型的全部，&gt;=0时为筛选值|False|
|type|integer|0位当前业务，1为已办业务|False|
|pageIndex|integer|当前页索引|False|
|pageSize|integer|页码大小|False|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口55、根据业务逻辑信息判断用户是否能创建业务

## 接口信息

- **所属控制器：** BusinessFlowManage

- **接口地址：** /sdcapi/BusinessFlow/IsFlowCanCreate

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|businessClass|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口56、获取业务类型列表

## 接口信息

- **所属控制器：** BusinessFlowManage

- **接口地址：** /sdcapi/BusinessFlow/GetFlowList

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：


- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口57、保存文件

## 接口信息

- **所属控制器：** BusinessFlowManage

- **接口地址：** /api/BusinessFlowManage/{id}

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|
|newId|string||True|
|attachType|string||True|
|file|HttpPostedFile类||True|

- **响应类型**：

  

# 接口58、get请求(xml结果)

## 接口信息

- **所属控制器：** BusinessFlowManage

- **接口地址：** /api/BusinessFlowManage

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|url|string||True|

- **响应类型**：

  string

# 接口59、获取单位列表

## 接口信息

- **所属控制器：** CompanyManage

- **接口地址：** /interal/CompanyManage/GetCompanyList

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|type|string|1,测绘单位；2,开发商；3,机关单位；4,一般企业|True|
|companyName|string||False|
|pageIndex|integer||False|
|pageSize|integer||False|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口60、获取单位人员列表

## 接口信息

- **所属控制器：** CompanyManage

- **接口地址：** /interal/CompanyManage/GetCompanyMemberList

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|companyId|string||True|
|pageIndex|integer||False|
|pageSize|integer||False|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口61、新建或者保存单位信息

## 接口信息

- **所属控制器：** CompanyManage

- **接口地址：** /interal/CompanyManage/CreateOrSaveCompanyInfo

- **请求方式：**POST  

- **Content-Type：**application/json

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|model|CompanyBaseInfoModel类||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口62、删除单位信息

## 接口信息

- **所属控制器：** CompanyManage

- **接口地址：** /interal/CompanyManage/DeleteCompanyInfo

- **请求方式：**POST  

- **Content-Type：**application/json

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|input|DeleteCompanyInfoInput类||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口63、添加单位人员信息

## 接口信息

- **所属控制器：** CompanyManage

- **接口地址：** /interal/CompanyManage/CreateOrSaveCompanyMember

- **请求方式：**POST  

- **Content-Type：**application/json

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|model|DeveloperEmployeeModel类||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口64、删除单位人员

## 接口信息

- **所属控制器：** CompanyManage

- **接口地址：** /interal/CompanyManage/DeleteCompanyMember

- **请求方式：**POST  

- **Content-Type：**application/json

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|input|DeleteCompanyMemberInput类||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口65、获取多测合一所有业务类型

## 接口信息

- **所属控制器：** CompanyManage

- **接口地址：** /interal/CompanyManage/GetAllFlows

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：


- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口66、更新测绘单位的多测合一业务范围

## 接口信息

- **所属控制器：** CompanyManage

- **接口地址：** /interal/CompanyManage/UpdateSurveyCompanyBusinessRange

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|companyId|string||True|
|businessClasses|||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口67、获取正在申请的单位注册信息

## 接口信息

- **所属控制器：** CompanyRegister

- **接口地址：** /sdcapi/CompanyRegister/GetRequesting

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：


- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口68、创建新的单位注册申请

## 接口信息

- **所属控制器：** CompanyRegister

- **接口地址：** /sdcapi/CompanyRegister/CreateNewCompanyRequest

- **请求方式：**POST  

- **Content-Type：**application/json

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|request|CompanyRegisterRequest类||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口69、保存单位申请信息

## 接口信息

- **所属控制器：** CompanyRegister

- **接口地址：** /sdcapi/CompanyRegister/SaveCompanyRequest

- **请求方式：**POST  

- **Content-Type：**application/json

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|request|CompanyRegisterRequest类||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口70、逻辑删除单位申请信息

## 接口信息

- **所属控制器：** CompanyRegister

- **接口地址：** /sdcapi/CompanyRegister/DeleteCompanyRequest

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口71、提交单位信息审核

## 接口信息

- **所属控制器：** CompanyRegister

- **接口地址：** /sdcapi/CompanyRegister/SubmitCompanyAuditRequest

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口72、关闭单位信息审核

## 接口信息

- **所属控制器：** CompanyRegister

- **接口地址：** /sdcapi/CompanyRegister/CloseCompanyAuditRequest

- **请求方式：**POST  

- **Content-Type：**application/json

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|request|CompanyRegisterRequest类||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口73、判断单位是否审核通过

## 接口信息

- **所属控制器：** CompanyRegister

- **接口地址：** /sdcapi/CompanyRegister/JudgeExamineRequest

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口74、获取单位信息

## 接口信息

- **所属控制器：** CompanyRegister

- **接口地址：** /sdcapi/CompanyRegister/GetCompanyDetailsInfo

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口75、根据用户信息获取单位信息

## 接口信息

- **所属控制器：** CompanyRegister

- **接口地址：** /sdcapi/CompanyRegister/GetCompanyInfoByUserInfo

- **请求方式：**POST  

- **Content-Type：**application/json

- **请求参数**：


- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口76、获取单位信息列表

## 接口信息

- **所属控制器：** CompanyRegister

- **接口地址：** /sdcapi/CompanyRegister/GetCompanyListInfo

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|StateCode|integer||True|
|CompanyName|string||True|
|PageIndex|integer||True|
|PageSize|integer||True|
|Sort|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口77、保存建设单位或者测绘单位的人员信息

## 接口信息

- **所属控制器：** CompanyRegister

- **接口地址：** /sdcapi/CompanyRegister/SaveDeveloperUserInfo

- **请求方式：**POST  

- **Content-Type：**application/json

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|request|CompanyEmployees类||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口78、删除建设单位和测绘单位的人员信息

## 接口信息

- **所属控制器：** CompanyRegister

- **接口地址：** /sdcapi/CompanyRegister/DeleteDeveloperUserInfo

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口79、离开自己所在单位

## 接口信息

- **所属控制器：** CompanyRegister

- **接口地址：** /sdcapi/CompanyRegister/LeaveTheCompany

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|companyId|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口80、根据单位ID获取单位人员信息列表

## 接口信息

- **所属控制器：** CompanyRegister

- **接口地址：** /sdcapi/CompanyRegister/GetDeveloperUserList

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|
|PageIndex|integer||True|
|PageSize|integer||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口81、根据开发商的统一社会信用代码，拉取最新的单位信息和管理员信息

## 接口信息

- **所属控制器：** CompanyRegister

- **接口地址：** /sdcapi/CompanyRegister/CreateOrReplaceRealEstateDevelopeCompany

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|companyNo|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口82、根据单位ID获取单位注册测绘师列表

## 接口信息

- **所属控制器：** CompanyRegister

- **接口地址：** /sdcapi/CompanyRegister/GetSurveyMasterList

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|
|PageIndex|integer||True|
|PageSize|integer||True|
|q|string||False|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口83、根据单位ID获取单位注册测绘师列表和授权情况

## 接口信息

- **所属控制器：** CompanyRegister

- **接口地址：** /sdcapi/CompanyRegister/GetSurveyMasterWithAuthInfoList

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|PageIndex|integer||True|
|PageSize|integer||True|
|q|string||False|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口84、提交授权申请、修改授权申请信息

## 接口信息

- **所属控制器：** CompanyRegister

- **接口地址：** /sdcapi/CompanyRegister/SaveSurveyMasterAuth

- **请求方式：**POST  

- **Content-Type：**application/json

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|request|SurveyMasterAuthRequest类||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口85、测绘师未确认的时候，撤销关闭

## 接口信息

- **所属控制器：** CompanyRegister

- **接口地址：** /sdcapi/CompanyRegister/CancelSurveyMasterAuth

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口86、测绘师已授权或者已过期的，关闭此授权，该操作只能由注册测绘师本人操作

## 接口信息

- **所属控制器：** CompanyRegister

- **接口地址：** /sdcapi/CompanyRegister/CloseSurveyMasterAuth

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口87、根据业务ID，获取已授权的注册测绘师

## 接口信息

- **所属控制器：** CompanyRegister

- **接口地址：** /sdcapi/CompanyRegister/GetAuthorizedSurveyMasters

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口88、注册测绘师本人获取授权列表，包括待确认、已确认、已过期和已失效的

## 接口信息

- **所属控制器：** CompanyRegister

- **接口地址：** /sdcapi/CompanyRegister/GetMySurveyMasterAuthList

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：


- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口89、注册测绘师刷脸确认授权

## 接口信息

- **所属控制器：** CompanyRegister

- **接口地址：** /sdcapi/CompanyRegister/ConfirmSurveyMasterAuth

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|authId|string||True|
|faceId|string||False|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口90、获取多测合一业务范围

## 接口信息

- **所属控制器：** CompanyRegister

- **接口地址：** /sdcapi/CompanyRegister/GetSurveyFlows

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：


- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口91、获取单位信息审核接口，默认测绘单位

## 接口信息

- **所属控制器：** CompanyRegisterAudit

- **接口地址：** /internal/CompanyRegisterAudit/GetCompanyAuditList

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|stateCode|integer||False|
|companyName|string||False|
|companyType|string||False|
|pageIndex|integer||False|
|pageSize|integer||False|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口92、返回特定状态的单位注册信息数

## 接口信息

- **所属控制器：** CompanyRegisterAudit

- **接口地址：** /internal/CompanyRegisterAudit/GetCompanyAuditCount

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|stateCode|integer||False|
|companyType|string||False|

- **响应类型**：

  integer

# 接口93、根据ID获取单位注册申请信息

## 接口信息

- **所属控制器：** CompanyRegisterAudit

- **接口地址：** /internal/CompanyRegisterAudit/GetCompanyRegistry

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|companyRegId|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口94、签收单位注册审核信息

## 接口信息

- **所属控制器：** CompanyRegisterAudit

- **接口地址：** /internal/CompanyRegisterAudit/SignCompanyRegistry

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|companyRegId|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口95、退回测绘单位注册申请

## 接口信息

- **所属控制器：** CompanyRegisterAudit

- **接口地址：** /internal/CompanyRegisterAudit/RejectCompanyRegistry

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|companyRegId|string||True|
|reason|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口96、测绘单位通过资质审核

## 接口信息

- **所属控制器：** CompanyRegisterAudit

- **接口地址：** /internal/CompanyRegisterAudit/AcceptSurveyCompanyRegistry

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|companyRegId|string|申请ID|True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口97、单位注册申请已完成协议备案

## 接口信息

- **所属控制器：** CompanyRegisterAudit

- **接口地址：** /internal/CompanyRegisterAudit/CompleteCompanyProtocolInfo

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|companyRegId|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口98、判断用户是否是单位其它单位的工作人员

## 接口信息

- **所属控制器：** CompanyRegisterAudit

- **接口地址：** /internal/CompanyRegisterAudit/CheckPersonIsRealEmployee

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|personNo|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口99、获取已注册单位列表

## 接口信息

- **所属控制器：** CompanyRegisterAudit

- **接口地址：** /internal/CompanyRegisterAudit/GetRegistedCompanyList

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|find|string||False|
|companyType|string|单位类型，默认测绘单位，取值范围有：测绘单位、开发商、机关单位、一般企业|False|
|pageIndex|integer||False|
|pageSize|integer||False|

- **响应类型**：

  #/definitions/PageResult

# 接口100、根据ID获取已注册单位的信息

## 接口信息

- **所属控制器：** CompanyRegisterAudit

- **接口地址：** /internal/CompanyRegisterAudit/GetRegistedCompanyInfo

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口101、更新指定测绘单位业务范围关联表

## 接口信息

- **所属控制器：** CompanyRegisterAudit

- **接口地址：** /internal/CompanyRegisterAudit/StartUpdateSurveyCompanyBusinessClass

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|companyId|string||True|

- **响应类型**：

  

# 接口102、根据分类名称获取普通或树结构集合

## 接口信息

- **所属控制器：** DataDirectory

- **接口地址：** /sdcapi/DataDirectory/GetDirectoryListOrTree

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|name|string|分类名称|True|
|type|string|返回类型|True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口103、获取预测绘项目相关信息

## 接口信息

- **所属控制器：** EPSApi

- **接口地址：** /internal/epsapi/GetPreSurveyProjectInfo

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口104、获取实核测绘项目相关信息

## 接口信息

- **所属控制器：** EPSApi

- **接口地址：** /internal/epsapi/GetRealActualSurveyProjectInfo

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口105、启动EPS进行数据切图

## 接口信息

- **所属控制器：** EPSApi

- **接口地址：** /internal/epsapi/PostEpsDownLoadResult

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口106、获取EPS下载状态

## 接口信息

- **所属控制器：** EPSApi

- **接口地址：** /internal/epsapi/GetEPSDownLoadState

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|downloadid|string||True|
|businessClass|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口107、保存宗地信息

## 接口信息

- **所属控制器：** EstateConstruction

- **接口地址：** /sdcapi/EstateConstruction/SaveCadastreInfo

- **请求方式：**POST  

- **Content-Type：**application/json

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|request|CadastreInfo类||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口108、删除宗地信息

## 接口信息

- **所属控制器：** EstateConstruction

- **接口地址：** /sdcapi/EstateConstruction/DeleteCadastreInfo

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口109、获取宗地列表

## 接口信息

- **所属控制器：** EstateConstruction

- **接口地址：** /sdcapi/EstateConstruction/GetCadastreList

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：


- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口110、保存自然幢信息

## 接口信息

- **所属控制器：** EstateConstruction

- **接口地址：** /sdcapi/EstateConstruction/SaveNatureBuildInfo

- **请求方式：**POST  

- **Content-Type：**application/json

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|request|NatureBuildInfo类||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口111、删除自然幢信息

## 接口信息

- **所属控制器：** EstateConstruction

- **接口地址：** /sdcapi/EstateConstruction/DeleteNatureBuildInfo

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口112、获取自然幢列表

## 接口信息

- **所属控制器：** EstateConstruction

- **接口地址：** /sdcapi/EstateConstruction/GetNatureBuildList

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口113、自然幢提交审核

## 接口信息

- **所属控制器：** EstateConstruction

- **接口地址：** /sdcapi/EstateConstruction/SubmmitNatureBuildAudit

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口114、自然幢审核签收

## 接口信息

- **所属控制器：** EstateConstruction

- **接口地址：** /sdcapi/EstateConstruction/AcceptNatureBuildAudit

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口115、自然幢审核通过

## 接口信息

- **所属控制器：** EstateConstruction

- **接口地址：** /sdcapi/EstateConstruction/ThroughNatureBuildAudit

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口116、自然幢审核退回

## 接口信息

- **所属控制器：** EstateConstruction

- **接口地址：** /sdcapi/EstateConstruction/BackNatureBuildAudit

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口117、获取自然幢审核列表

## 接口信息

- **所属控制器：** EstateConstruction

- **接口地址：** /sdcapi/EstateConstruction/GetNatureBuildAuditList

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：


- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口118、业务审核通过
包括：规划验线、不动产预测绘成果备案、规划核实、不动产实测绘成果备案

## 接口信息

- **所属控制器：** EstateProjectAudit

- **接口地址：** /internal/EstateProjectAudit/CompleteAudit

- **请求方式：**POST  

- **Content-Type：**application/json

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|argument|||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口119、业务审核不通过
包括：规划验线、不动产预测绘成果备案、规划核实、不动产实测绘成果备案

## 接口信息

- **所属控制器：** EstateProjectAudit

- **接口地址：** /internal/EstateProjectAudit/RejectAudit

- **请求方式：**POST  

- **Content-Type：**application/json

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|argument|||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口120、根据业务ID获取业务信息

## 接口信息

- **所属控制器：** EstateProjectAudit

- **接口地址：** /internal/EstateProjectAudit/GetBusinessByID

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口121、附件下载

## 接口信息

- **所属控制器：** EstateProjectAudit

- **接口地址：** /internal/EstateProjectAudit/AttachmentDownloadRequest

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  object

# 接口122、反馈不动产预测绘成果备案信息MDB

## 接口信息

- **所属控制器：** EstateProjectAudit

- **接口地址：** /internal/EstateProjectAudit/RejectPreSurveyMDBUpload

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string|多测合一业务ID|True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口123、反馈不动产实核测绘成果备案信息MDB

## 接口信息

- **所属控制器：** EstateProjectAudit

- **接口地址：** /internal/EstateProjectAudit/RejectRealActualSurveyMDBUpload

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string|多测合一业务ID|True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口124、获取测绘成果审核业务列表

## 接口信息

- **所属控制器：** EstateProjectAudit

- **接口地址：** /internal/EstateProjectAudit/GetProjectAuditList

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|classType|string|业务类型，当前仅支持RealEstateActualSurveyFlow不动产实核业务和RealEstatePreCheckSurveyFlow不动产预核业务|True|
|includeNotSign|boolean|包含未签收，如果includeSigned为false，此参数强制true|False|
|includeSigned|boolean|包含已签收|False|
|pageIndex|integer||False|
|pageSize|integer||False|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口125、签收审核业务

## 接口信息

- **所属控制器：** EstateProjectAudit

- **接口地址：** /internal/EstateProjectAudit/SignProjectAudit

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口126、获取已完成验收的测绘列表

## 接口信息

- **所属控制器：** ExternalForBDC

- **接口地址：** /sdcapi/external_bdc/LoadSurveyProjects

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|companyId|string||False|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口127、获取项目详情

## 接口信息

- **所属控制器：** ExternalForBDC

- **接口地址：** /sdcapi/external_bdc/GetPreSurveyProjectInfo

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口128、下载项目附件

## 接口信息

- **所属控制器：** ExternalForBDC

- **接口地址：** /sdcapi/external_bdc/DownloadAttachment

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  object

# 接口129、作废预测绘项目成果

## 接口信息

- **所属控制器：** ExternalForBDC

- **接口地址：** /sdcapi/external_bdc/RejectToSurvey

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口130、获取关联的放线测量成果状态

## 接口信息

- **所属控制器：** ExternalForBDC

- **接口地址：** /sdcapi/external_bdc/GetRelatedPutLineSurveyState

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|preSurveyBusinessID|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口131、根据放线测量业务ID下载放线测量成果报告

## 接口信息

- **所属控制器：** ExternalForBDC

- **接口地址：** /sdcapi/external_bdc/DownloadPutlineReportBySurveyID

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  object

# 接口132、获取最新的测绘成果状态

## 接口信息

- **所属控制器：** ExternalForBDC

- **接口地址：** /sdcapi/external_bdc/GetLatestSurveyStates

- **请求方式：**POST  

- **Content-Type：**application/json

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|estateProject|EstateProjectInfoModel类||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口133、下载已完成备案的测绘成果备案凭证

## 接口信息

- **所属控制器：** ExternalForBDC

- **接口地址：** /sdcapi/external_bdc/DownloadBAZMPDF

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|appno|string||False|
|code|string||False|

- **响应类型**：

  object

# 接口134、查看已完成备案的测绘成果备案凭证

## 接口信息

- **所属控制器：** ExternalForBDC

- **接口地址：** /sdcapi/external_bdc/ViewBAZMPDF/不动产测绘成果备案通知

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|appno|string||False|
|code|string||False|

- **响应类型**：

  object

# 接口135、下载已完成备案的测绘成果备案凭证

## 接口信息

- **所属控制器：** ExternalForBDC

- **接口地址：** /sdcapi/external_bdc/DownloadSCBAZMPDF

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  object

# 接口136、查看已完成备案的测绘成果备案凭证

## 接口信息

- **所属控制器：** ExternalForBDC

- **接口地址：** /sdcapi/external_bdc/ViewSCBAZMPDF/不动产测绘成果备案通知

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  object

# 接口137、根据实测绘的ID获取不动产项目信息

## 接口信息

- **所属控制器：** ExternalForBDC

- **接口地址：** /sdcapi/external_bdc/GetEstateProjectInfoByRealActualSurveyId

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口138、根据业务ID下载楼盘信息表

## 接口信息

- **所属控制器：** ExternalForBDC

- **接口地址：** /sdcapi/external_bdc/DownloadBuildingInfoPDFByBusinessId

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  object

# 接口139、根据业务ID下载竣工规划条件核实信息表

## 接口信息

- **所属控制器：** ExternalForBDC

- **接口地址：** /sdcapi/external_bdc/DownloadPlanCheckInfoPDFByBusinessId

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  object

# 接口140、统一共享服务入口请求

## 接口信息

- **所属控制器：** Share

- **接口地址：** /sdcapi/share

- **请求方式：**POST  

- **Content-Type：**application/json

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|input|SDCEncryptedData类||True|

- **响应类型**：

  object

# 接口141、解密测试

## 接口信息

- **所属控制器：** Share

- **接口地址：** /sdcapi/share/Decrypt

- **请求方式：**POST  

- **Content-Type：**application/json

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|input|SDCEncryptedData类||True|

- **响应类型**：

  object

# 接口142、创建并提交变更申请

## 接口信息

- **所属控制器：** SurveyCompanyInfoModify

- **接口地址：** /sdcapi/SurveyCompanyInfoModify/CreateModifyRequest

- **请求方式：**POST  

- **Content-Type：**application/json

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|modifyModelObj|||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口143、获取正在申请的单位信息变更申请

## 接口信息

- **所属控制器：** SurveyCompanyInfoModify

- **接口地址：** /sdcapi/SurveyCompanyInfoModify/GetRequesting

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：


- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口144、获取变更信息详情

## 接口信息

- **所属控制器：** SurveyCompanyInfoModify

- **接口地址：** /sdcapi/SurveyCompanyInfoModify/GetModifyRequest

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口145、获取单位申请变更的列表

## 接口信息

- **所属控制器：** SurveyCompanyInfoModify

- **接口地址：** /sdcapi/SurveyCompanyInfoModify/GetRequestList

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|pageIndex|integer||False|
|pageSize|integer||False|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口146、获取审核列表，带分页

## 接口信息

- **所属控制器：** SurveyCompanyInfoModifyAudit

- **接口地址：** /internal/SurveyCompanyInfoModifyAudit/GetCompanyInfoModifyAuditList

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|includeNotSign|boolean|是否包含未签收|False|
|includeSigned|boolean|是否包含已签收|False|
|pageIndex|integer||False|
|pageSize|integer||False|

- **响应类型**：

  #/definitions/PageResult[Object]

# 接口147、获取变更信息详情

## 接口信息

- **所属控制器：** SurveyCompanyInfoModifyAudit

- **接口地址：** /internal/SurveyCompanyInfoModifyAudit/GetModifyRequest

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口148、签收变更申请

## 接口信息

- **所属控制器：** SurveyCompanyInfoModifyAudit

- **接口地址：** /internal/SurveyCompanyInfoModifyAudit/SignModifyRequest

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口149、变更审核通过

## 接口信息

- **所属控制器：** SurveyCompanyInfoModifyAudit

- **接口地址：** /internal/SurveyCompanyInfoModifyAudit/AcceptModify

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```

# 接口150、变更驳回

## 接口信息

- **所属控制器：** SurveyCompanyInfoModifyAudit

- **接口地址：** /internal/SurveyCompanyInfoModifyAudit/RejectModify

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

|参数名|数据类型|说明|是否必填|
|-|-|-|-|
|id|string||True|
|reason|string||True|

- **响应类型**：

  JSON

返回结构：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
   "Data": {}
}
```



# 类定义 

## AttachmentInfo
|属性名|数据类型|说明|
|-|-|-|
|ID|string|附件唯一标识码|
|BusinessType|string|附件关联类型|
|BusinessID|string|附件关联ID|
|AttachmentName|string|附件名称|
|AttachmentExt|string|附件后缀名|
|AttachmentLength|integer|附件大小|
|AttachmentType|string|附件类型|
|AttachmentCategories|string|附件类别|
|UploadTime|string|附件上传时间|
|AttachmentPath|string|附件路径|
|AttachmentThumbPath|string|附件缩略图路径|
|StateCode|integer|附件状态（逻辑删除）0未删除，1已删除|
|ValidateNumber|string|附件提取码|
|ExpirationTime|string|过期时间|
|LiftTime|string|锁定解除时间|

## ApiResult(统一API接口返回模型)
|属性名|数据类型|说明|
|-|-|-|
|StateCode|integer|接口状态值|
|Message|string|返回信息|
|Data|object|返回数据|

## PageResult[BusinessBaseInfo]
|属性名|数据类型|说明|
|-|-|-|
|Page|||
|DataTable|array||

## Page
|属性名|数据类型|说明|
|-|-|-|
|PageIndex|integer||
|PageSize|integer||
|Total|integer||

## BusinessBaseInfo
|属性名|数据类型|说明|
|-|-|-|
|ID|string|业务ID|
|BusinessNumber|string|业务编号|
|BusinessName|string|业务名称|
|BusinessType|string|业务类型|
|BusinessClass|string|业务逻辑类|
|StateCode|integer|业务状态，0待签收，1办理中，2已完成，3已退回，4已关闭|
|CreateUserId|string|创建业务的用户ID|
|CreatePersonName|string|创建业务的用户姓名|
|CreatePersonNo|string|创建业务的用户身份证号|
|CreatePersonPhone|string|创建业务的用户联系电话|
|DeveloperName|string|建设单位名称|
|DeveloperNo|string|建设单位社会统一信用代码|
|SurveyCompanyName|string|测绘单位名称|
|SurveyCompanyNo|string|测绘单位社会统一信用代码|
|ExtendInfo|string|预留的扩展信息，Blob类型|
|CreateTime|string|创建时间|
|FinishTime|string|结束时间|

## BaseSurveyDataDownloadProject(基础测绘数据下载业务信息)
|属性名|数据类型|说明|
|-|-|-|
|BaseInfo||基本信息|
|ContentInfo||内容信息|
|Attachments|array|附件信息|
|ActionsInfos|array|当前经过的流转环节信息|
|CurrentAction||最新的环节，当前环节|
|FlowInfo||工作流定义信息|

## BusinessBaseInfoModel(业务基本信息)
|属性名|数据类型|说明|
|-|-|-|
|ID|string|业务ID|
|BusinessNumber|string|业务编号|
|BusinessName|string|业务名称|
|BusinessType|string|业务类型|
|BusinessClass|string|业务逻辑类|
|StateCode|integer|业务状态，0待签收，1办理中，2已完成，3已退回，4已关闭|
|CreateUserId|string|创建业务的用户ID|
|CreatePersonName|string|创建业务的用户姓名|
|CreatePersonNo|string|创建业务的用户身份证号|
|CreatePersonPhone|string|创建业务的用户联系电话|
|DeveloperName|string|建设单位名称|
|DeveloperNo|string|建设单位社会统一信用代码|
|SurveyCompanyName|string|测绘单位名称|
|SurveyCompanyNo|string|测绘单位社会统一信用代码|
|ExtendInfo|string|预留的扩展信息，Blob类型|
|CreateTime|string|创建时间|
|FinishTime|string|结束时间|

## BaseGISData_ContentInfoModel(基础地理信息数据业务内容信息)
|属性名|数据类型|说明|
|-|-|-|
|ID|string|主键ID，与BusinessBaseInfo表的ID关联的外键|
|ProjectName|string|项目名称|
|ProjectBasis|string|立项依据|
|ProjectScope|string|项目范围|
|DataCheckID|string|范围检查任务ID|
|DataCheckState|integer|范围检查状态 0 审核中  1 审核通过  2 审核不通过|
|DownLoadID|string|基础数据提提取任务ID|
|DownLoadState|integer|基础数据提提取状态 0 未提取/正在提套取  1 数据提取成功  2 下载失败  3 科室审核确认中  4 科室审核不通过|
|FailedReason|string|检查不通过原因|
|ApplyData|string|申请下载的图层|

## AttachmentInfoModel(附件信息表)
|属性名|数据类型|说明|
|-|-|-|
|ID|string|附件唯一标识码|
|BusinessType|string|附件关联类型|
|BusinessID|string|附件关联ID|
|AttachmentName|string|附件名称|
|AttachmentExt|string|附件后缀名|
|AttachmentLength|integer|附件大小|
|AttachmentType|string|附件类型|
|AttachmentCategories|string|附件类别|
|UploadTime|string|附件上传时间|
|AttachmentPath|string|附件路径|
|AttachmentThumbPath|string|附件缩略图路径|
|StateCode|integer|附件状态（逻辑删除）0未删除，1已删除|
|ValidateNumber|string|附件提取码|
|ExpirationTime|string|过期时间|
|LiftTime|string|锁定解除时间|

## BusinessLinkInfoModel(业务流转信息)
|属性名|数据类型|说明|
|-|-|-|
|ID|string|环节ID|
|BusinessId|string|业务ID|
|LinkName|string|环节名称|
|StateCode|integer|环节状态，0待签收，1办理中，2已完成，3已退回，4已关闭|
|StartTime|string|环节开始时间|
|SignatureTime|string|签收时间|
|EndTime|string|环节完成时间|
|CurrentUserID|string|环节执行用户ID|
|ActionId|integer|流程环节ID|
|ActionUserRole|string|流程用户权限|
|FlowNotice|string|流转意见、退回原因等|

## BusinessFlowBase(基础业务流程定义基类型)
|属性名|数据类型|说明|
|-|-|-|
|FlowName|string|流程名称|
|Catalog|string|流程大类|
|FlowActionInfo||流程环节信息|
|FlowActionJSON|string|流程环节信息JSON|
|HasSurvey|boolean|是否包含测绘作业|
|Enable|boolean|流程是否启用|
|NeedBDCProtocol|boolean|该流程是否需要协议备案|
|Visible|boolean|该流程是否在办理业务中可见|

## BusinessFlowActionInfo(流程环节信息)
|属性名|数据类型|说明|
|-|-|-|
|ValidateActionPost|||
|Actions|array|环节集合|
|StartActionId|integer|开始环节Id|
|EndActionIds|array|结束环节，多路由时可以拥有多个结束环节|
|Routes|array|环节流转路由集合|
|BackRoutes|array|回退环节路由集合|
|FinishWhenIntoEndAction|boolean|获取或者设置一个值，表示提交到流程最后一步时是否自动完成|

## ValidateFlowActionPostHandler(环节提交验证的方法代理)
|属性名|数据类型|说明|
|-|-|-|
|Method|||
|Target|object||

## BusinessFlowAction(流程环节)
|属性名|数据类型|说明|
|-|-|-|
|ID|integer|环节标识，环节集合内唯一即可|
|Index|integer|环节索引，用于列表排序|
|Name|string|环节名称|
|ActionRoles|array|环节执行者角色，此环节由什么角色来执行|

## BusinessFlowRoute(路由，用于连接两个环节)
|属性名|数据类型|说明|
|-|-|-|
|RouteLabel|string|路由标签|
|FromActionId|integer|路由开始环节Id|
|ToActionId|integer|路由结束环节Id|

## BusinessFlowBackRoute(退回设定专用路由)
|属性名|数据类型|说明|
|-|-|-|
|NeedResponseReason|boolean|退回时是否需要填写退回原因|
|IsBackToMyself|boolean|是否直接退回给自己|
|RouteLabel|string|路由标签|
|FromActionId|integer|路由开始环节Id|
|ToActionId|integer|路由结束环节Id|

## MethodInfo

## PageResult[SurveyProjectResultListItem]
|属性名|数据类型|说明|
|-|-|-|
|Page|||
|DataTable|array||

## SurveyProjectResultListItem(测绘成果确认列表)
|属性名|数据类型|说明|
|-|-|-|
|ID|string|多测合一业务ID|
|BusinessNo|string|业务编号|
|ActionId|string|业务当前环节ID|
|BusinessName|string|业务名称|
|BusinessType|string|业务类型|
|ConfirmTime|string|确认时间|

## HttpPostedFile
|属性名|数据类型|说明|
|-|-|-|
|FileName|string||
|ContentType|string||
|ContentLength|integer||
|InputStream|||

## Stream
|属性名|数据类型|说明|
|-|-|-|
|__identity|object||

## CompanyBaseInfoModel(单位基本信息)
|属性名|数据类型|说明|
|-|-|-|
|ID|string|ID|
|RelationRequestId|string|关联已通过的申请ID，或者变更ID|
|CompanyType|string|单位类型|
|CompanyName|string|单位名称|
|CreditCode|string|统一社会信用代码|
|LegalPersonName|string|法定代表人姓名|
|LegalPersonNumber|string|法定代表人身份证号|
|CompanyAddress|string|单位地址|
|Contacter|string|联系人|
|ContacterPhone|string|联系电话|

## DeleteCompanyInfoInput
|属性名|数据类型|说明|
|-|-|-|
|CompanyId|string|单位ID|
|DeleteReason|string|删除原因|

## DeveloperEmployeeModel(单位人员信息)
|属性名|数据类型|说明|
|-|-|-|
|ID|string|ID|
|RelationRequestId|string|关联已通过的申请ID，或者变更ID|
|PersonName|string|姓名|
|PersonNumber|string|身份证号|
|PersonPhone|string|手机号|
|PersonRole|string|角色|
|AttachmentInfo|string|附件信息|

## DeleteCompanyMemberInput
|属性名|数据类型|说明|
|-|-|-|
|MemberId|string|单位ID|
|DeleteReason|string|删除原因|

## CompanyRegisterRequest
|属性名|数据类型|说明|
|-|-|-|
|ID|string|ID|
|CreateUserID|string|用户ID|
|CreateUserName|string|申请用户姓名|
|CreateUserPersonNo|string|申请用户证件号|
|CompanyType|string|申请单位类型，取值范围：开发商、测绘单位、机关单位、一般企业|
|CompanyName|string|注册单位名称|
|CompanyNo|string|注册单位统一社会信用代码|
|CreateTime|string|创建时间|
|PostTime|string|提交审核时间|
|AcceptAuditTime|string|审核签收时间|
|SuccessTime|string|批准时间/审核通过时间|
|CloseTime|string|失效时间|
|StateCode|integer|申请状态，0未提交，1已提交/待审核，2审核中，3完成注册，4退回修改，5已失效，6待协议备案|
|DetailInfo|string|详细信息JSON|
|ResponseMessage|string|反馈信息|
|ResponsePerson|string|审核人员|

## CompanyEmployees
|属性名|数据类型|说明|
|-|-|-|
|ID|string|ID|
|RelationRequestId|string|关联已通过的申请ID，或者变更ID|
|PersonName|string|姓名|
|PersonNumber|string|身份证号|
|PersonPhone|string|手机号|
|PersonRole|string|角色|
|RegisteredSurveyorNo|string|注册测绘师证书编号|
|ValidityTime|string|注册测绘师证书有效日期|
|AttachmentInfo|string|附件信息|

## SurveyMasterAuthRequest(注册测绘师授权申请信息)
|属性名|数据类型|说明|
|-|-|-|
|BusinessClasses|array||
|ID|string||
|CompanyID|string|单位ID|
|SurveyMasterID|string|注册测绘师ID|
|SurveyMasterPersonNo|string|注册测绘师身份证号|
|AuthEndDate|string|授权结束时间|
|CreateDate|string|创建时间|
|CreatorID|string|创建者用户Id|
|Creator|string|创建者|
|AcceptDate|string|测绘师确认授权时间|
|StateCode|integer|状态，0待确认，1已确认，2已过期，-1已撤销|

## SurveyMasterAuthClassModel(注册测绘师授权业务类型表)
|属性名|数据类型|说明|
|-|-|-|
|ID|string||
|AuthID|string|授权ID|
|BusinessClass|string|注册测绘师ID|

## PageResult
|属性名|数据类型|说明|
|-|-|-|
|PageIndex|integer||
|PageSize|integer||
|TotalCount|integer||
|List|object||

## CadastreInfo
|属性名|数据类型|说明|
|-|-|-|
|ID|string|ID|
|CadastreCode|string|宗地代码|
|EstateNo|string|宗地不动产权证号|
|QuartersName|string|小区名称|
|CreditCode|string|统一社会信用代码|

## NatureBuildInfo
|属性名|数据类型|说明|
|-|-|-|
|ID|string|ID|
|RelationId|string|关联宗地ID|
|WorkRegulationNo|string|工规证号|
|StateCode|string|审核状态 0 未审核 1 审核中 2 已审核|

## EstateProjectInfoModel(不动产测绘项目管理)
|属性名|数据类型|说明|
|-|-|-|
|ID|string|唯一标识码|
|PlanCerificateCode|string|建设工程规划许可证号(应与ID一致)|
|PutLineSurveyID|string|放线测量业务ID|
|PutLineSurveyState|string|放线测量成果应用状态|
|PreSurveyID|string|预测绘测量业务ID|
|PreSurveyState|string|预测绘成果备案状态|
|CheckConditionSurveyID|string|条件核实测量业务ID|
|CheckConditionSurveyState|string|规划核实状态|
|RealSurveyID|string|实测绘测量业务ID|
|RealSurveyState|string|实测绘成果备案状态|

## SDCEncryptedData(加密数据对象)
|属性名|数据类型|说明|
|-|-|-|
|Data|string|数据密文|
|Sign|string|数字签名|

## PageResult[Object]
|属性名|数据类型|说明|
|-|-|-|
|Page|||
|DataTable|array||

﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using SDCPCWeb.Jobs;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using SDCPCWeb.Models.CompanyRegister;
using SDCPCWeb.Models.System;
using SDCPCWeb.Services;

namespace SDCPCWeb.Jobs.Tests {
    [TestClass()]
    public class SurveyCompanyJobTests {
        [TestMethod()]
        public void UpdateSurveyCompanyBusinessClassTest() {
            var oracle = new OracleDataService();
            var quaList = oracle.GetList<CompanyQualification>("0=0");
            BusinessFlowConfig.Initialize();
            foreach (var qualification in quaList) {
                if (!qualification.BusinessRange.StartsWith("[[")) {
                    continue;
                }
                var typeList = JsonConvert.DeserializeObject<List<List<string>>>(qualification.BusinessRange);
                var businessClass = new List<string>();
                var flows = BusinessFlowConfig.AllFlows;
                foreach (var type in typeList) {
                    var name = type.Last();
                    if (flows.Any(flow => flow.FlowName == name)) {
                        var flowObj = flows.First(flow => flow.FlowName == name);
                        var className = flowObj.GetType().Name;
                        businessClass.Add(className);
                    }
                }

                qualification.BusinessRange = JsonConvert.SerializeObject(businessClass);
                oracle.UpdateOne(qualification);
            }
        }
        [TestMethod()]
        public void UpdateSurveyCompanyBusinessClassInRequestsTest() {
            var oracle = new OracleDataService();
            var regList = oracle.GetList<CompanyRegisterRequest>("0=0");
            BusinessFlowConfig.Initialize();
            foreach (var request in regList) {
                if (request.CompanyType != "测绘单位") {
                    continue;
                }
                var detailsInfo = JsonConvert.DeserializeObject<CompanyRegisterDetailInfo>(request.DetailInfo);
                if (detailsInfo?.CompanyQualification == null) {
                    continue;
                }

                if (detailsInfo?.CompanyQualification?.BusinessRange == "[]") {
                    Console.WriteLine($"{request.CompanyName}:{request.StateCode}:{detailsInfo.CompanyQualification.BusinessRange}");
                    continue;
                }
                if (detailsInfo?.CompanyQualification?.BusinessRange.StartsWith("[[") == false) {
                    if (detailsInfo?.CompanyQualification?.BusinessRange.StartsWith("[\"") == true) {
                        var typeList = JsonConvert.DeserializeObject<List<string>>(detailsInfo.CompanyQualification.BusinessRange);
                        var businessClass = new List<string>();
                        var flows = BusinessFlowConfig.AllFlows;
                        foreach (var typeName in typeList) {
                            if (flows.Any(flow => flow.FlowName == typeName)) {
                                var flowObj = flows.First(flow => flow.FlowName == typeName);
                                var className = flowObj.GetType().Name;
                                businessClass.Add(className);
                                //Console.WriteLine($"{request.CompanyName}:{detailsInfo.CompanyQualification.BusinessRange}");
                            }
                        }

                        if (businessClass.Any()) {
                            detailsInfo.CompanyQualification.BusinessRange = JsonConvert.SerializeObject(businessClass);
                            request.DetailInfo = JsonConvert.SerializeObject(detailsInfo, SystemConfig.JsonDateTimeConverter);
                            oracle.UpdateOne(request);
                        }

                    }
                }
                else {
                    var typeList = JsonConvert.DeserializeObject<List<List<string>>>(detailsInfo.CompanyQualification.BusinessRange);
                    var businessClass = new List<string>();
                    var flows = BusinessFlowConfig.AllFlows;
                    foreach (var type in typeList) {
                        var name = type.Last();
                        if (flows.Any(flow => flow.FlowName == name)) {
                            var flowObj = flows.First(flow => flow.FlowName == name);
                            var className = flowObj.GetType().Name;
                            businessClass.Add(className);
                            //Console.WriteLine($"{request.CompanyName}:{detailsInfo.CompanyQualification.BusinessRange}");
                        }
                    }

                    if (businessClass.Any()) {
                        detailsInfo.CompanyQualification.BusinessRange = JsonConvert.SerializeObject(businessClass);
                        request.DetailInfo =
                            JsonConvert.SerializeObject(detailsInfo, SystemConfig.JsonDateTimeConverter);
                        oracle.UpdateOne(request);
                    }
                }

            }

            return;

            var modifyList = oracle.GetList<CompanyInfoModifyRequest>("0=0");
            foreach (var request in modifyList) {
                var detailsInfo = JsonConvert.DeserializeObject<CompanyRegisterDetailInfo>(request.OldContent);
                if (detailsInfo.CompanyQualification?.BusinessRange.StartsWith("[[") == true) {
                    var typeList = JsonConvert.DeserializeObject<List<List<string>>>(detailsInfo.CompanyQualification.BusinessRange);
                    var businessClass = new List<string>();
                    var flows = BusinessFlowConfig.AllFlows;
                    foreach (var type in typeList) {
                        var name = type.Last();
                        if (flows.Any(flow => flow.FlowName == name)) {
                            var flowObj = flows.First(flow => flow.FlowName == name);
                            var className = flowObj.GetType().Name;
                            businessClass.Add(className);
                        }
                        else if (name == "不动产预核测绘") {
                            businessClass.Add(BusinessFlowConfig.RealEstatePreCheckSurveyFlow.GetType().Name);
                        }
                        else if (name == "不动产实核测绘") {
                            businessClass.Add(BusinessFlowConfig.RealEstateActualSurveyFlow.GetType().Name);
                        }
                    }
                    detailsInfo.CompanyQualification.BusinessRange = JsonConvert.SerializeObject(businessClass);
                    request.OldContent = JsonConvert.SerializeObject(detailsInfo, SystemConfig.JsonDateTimeConverter);
                }

                detailsInfo = JsonConvert.DeserializeObject<CompanyRegisterDetailInfo>(request.NewContent);
                if (detailsInfo.CompanyQualification?.BusinessRange.StartsWith("[[") == true) {
                    var typeList = JsonConvert.DeserializeObject<List<List<string>>>(detailsInfo.CompanyQualification.BusinessRange);
                    var businessClass = new List<string>();
                    var flows = BusinessFlowConfig.AllFlows;
                    foreach (var type in typeList) {
                        var name = type.Last();
                        if (flows.Any(flow => flow.FlowName == name)) {
                            var flowObj = flows.First(flow => flow.FlowName == name);
                            var className = flowObj.GetType().Name;
                            businessClass.Add(className);
                        }
                        else if (name == "不动产预核测绘") {
                            businessClass.Add(BusinessFlowConfig.RealEstatePreCheckSurveyFlow.GetType().Name);
                        }
                        else if (name == "不动产实核测绘") {
                            businessClass.Add(BusinessFlowConfig.RealEstateActualSurveyFlow.GetType().Name);
                        }

                    }
                    detailsInfo.CompanyQualification.BusinessRange = JsonConvert.SerializeObject(businessClass);
                    request.NewContent = JsonConvert.SerializeObject(detailsInfo, SystemConfig.JsonDateTimeConverter);
                }

                oracle.UpdateOne(request);
            }
        }

        [TestMethod()]
        public void UpdateSurveyCompanyBusinessClassFromClassesTest() {
            var oracle = new OracleDataService();
            var quaList = oracle.GetList<CompanyQualification>("0=0");
            foreach (var qualification in quaList) {
                var dt = oracle.ExecuteQuerySql(
                    $"SELECT BUSINESSCLASS FROM SURVEYCOMPANYBUSINESSCLASS WHERE SURVEYCOMPANYID='{qualification.ID}'");
                var businessClass = dt.Rows.Cast<DataRow>().Select(r => r[0].ToString()).ToList();

                qualification.BusinessRange = JsonConvert.SerializeObject(businessClass);
                oracle.UpdateOne(qualification);
            }
        }
    }
}
﻿using System;
using Newtonsoft.Json;
using SDCPCWeb.Services;

namespace SDCPCWeb.Models.CompanyRegister {
    /// <summary>
    /// 单位同步到综合服务平台表
    /// </summary>
    public class CompanySyncInfoModel {
        /// <summary>
        /// 单位id
        /// </summary>
        public string ID { get; set; }

        /// <summary>
        /// 同步时间
        /// </summary>
        public DateTime CreateTime { get; set; }

    }

    public class CompanySyncInfo : CompanySyncInfoModel, IOracleDataTable {
        public CompanyBaseInfoModel ToModel() {
            return JsonConvert.DeserializeObject<CompanyBaseInfoModel>(JsonConvert.SerializeObject(this));
        }
    }
}
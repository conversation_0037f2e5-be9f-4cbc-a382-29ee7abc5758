﻿using Hangfire;
using Hangfire.Server;
using Newtonsoft.Json;
using SDCPCWeb.Encrypts;
using SDCPCWeb.Models.CompanyRegister;
using SDCPCWeb.Models.Result;
using SDCPCWeb.Models.System;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Hangfire.Console;

namespace SDCPCWeb.Services {
    /// <summary>
    /// 单位服务类
    /// </summary>
    public class CompanyService {

        /// <summary>
        /// 同步单个单位信息到综合服务平台
        /// </summary>
        /// <param name="console"></param>
        /// <returns></returns>
        [DisplayName("同步单个单位信息到综合服务平台"), AutomaticRetry(Attempts = 0)]
        public static async Task SyncCompanyInfo(PerformContext console) {
            using (OracleDataService service = new OracleDataService()) {
                var where = $"ID not in (select Id from CompanySyncInfo)";
                var companyBaseInfo = service.GetList<CompanyBaseInfo>(where).FirstOrDefault();
                if (companyBaseInfo != null)
                    Hangfire.BackgroundJob.Enqueue(() => SyncCompanyInfo(companyBaseInfo, null));
            }
        }

        /// <summary>
        /// 同步所有未同步的单位信息到综合服务平台
        /// </summary>
        /// <param name="console"></param>
        /// <returns></returns>
        [DisplayName("同步所有未同步的单位信息到综合服务平台"), AutomaticRetry(Attempts = 0)]
        public static async Task SyncAllCompanyInfo(PerformContext console) {
            using (OracleDataService service = new OracleDataService()) {
                var where = $"ID not in (select Id from CompanySyncInfo)";
                var list = service.GetList<CompanyBaseInfo>(where);
                foreach (var companyBaseInfo in list) {
                    Hangfire.BackgroundJob.Enqueue(() => SyncCompanyInfo(companyBaseInfo, null));
                    Thread.Sleep(1000);
                }
            }
        }

        /// <summary>
        /// 同步单个单位信息到综合服务平台
        /// </summary>
        /// <param name="companyBaseInfo"></param>
        /// <param name="console"></param>
        /// <returns></returns>
        [DisplayName("同步单个单位信息到综合服务平台"), AutomaticRetry(Attempts = 0)]
        public static async Task SyncCompanyInfo(CompanyBaseInfo companyBaseInfo, PerformContext console) {

            //推送单个单位信息到综合服务平台
            var result = await SyncCompanyInfo(companyBaseInfo);
            console?.WriteLine(result);
            if (result.StateCode != 1) {
                throw new Exception($"推送单位信息失败：{result.Message}");
            }

            
            using (OracleDataService service = new OracleDataService()) {
                //如果推送成功，就写入记录
                var companySyncInfo = service.GetById<CompanySyncInfo>(companyBaseInfo.ID);
                if (companySyncInfo == null) {
                    companySyncInfo = new CompanySyncInfo() {
                        ID = companyBaseInfo.ID,
                        CreateTime = DateTime.Now
                    };

                    service.InsertOne(companySyncInfo);
                }

                if (companyBaseInfo.CompanyType == "开发商") {
                    //判断是否有以企业名称注册的企业管理员，如果没有就补上一个
                    string where =
                        $"RelationRequestId = '{companySyncInfo.ID}' AND PersonNumber = '{companyBaseInfo.CreditCode}'";
                    var list = service.GetList<DeveloperEmployee>(where);
                    if (list.Any() == false) {
                        var developerEmployee = new DeveloperEmployee() {
                            ID = Guid.NewGuid().ToString("N"),
                            RelationRequestId = companyBaseInfo.ID,
                            PersonName = companyBaseInfo.CompanyName,
                            PersonNumber = companyBaseInfo.CreditCode,
                            PersonPhone = companyBaseInfo.ContacterPhone,
                            PersonRole = "单位管理员",
                            UserType = "1"
                        };

                        service.InsertOne(developerEmployee);
                    }
                }
                else {
                    //判断是否有以企业名称注册的企业管理员，如果没有就补上一个
                    string where =
                        $"RelationRequestId = '{companySyncInfo.ID}' AND PersonNumber = '{companyBaseInfo.CreditCode}'";
                    var list = service.GetList<CompanyEmployees>(where);
                    if (list.Any() == false) {
                        var companyEmployees = new CompanyEmployees() {
                            ID = Guid.NewGuid().ToString("N"),
                            RelationRequestId = companyBaseInfo.ID,
                            PersonName = companyBaseInfo.CompanyName,
                            PersonNumber = companyBaseInfo.CreditCode,
                            PersonPhone = companyBaseInfo.ContacterPhone,
                            PersonRole = "单位管理员",
                            UserType = "1"
                        };

                        service.InsertOne(companyEmployees);
                    }
                }
            }
        }

        /// <summary>
        /// 检查是否有注册测绘师即将到期并提醒单位管理员
        /// </summary>
        /// <param name="console"></param>
        /// <returns></returns>
        [DisplayName("检查是否有注册测绘师即将到期并提醒单位管理员"), AutomaticRetry(Attempts = 0)]
        public static async Task CheckZCCKSExpire(PerformContext console) {

            string sql = "select A.ID AS COMPANYID, A.COMPANYNAME, ";
            sql += "(select listagg(PERSONPHONE, ',') within group (order by 1) PHONES from COMPANYEMPLOYEES where PERSONROLE = '单位管理员' and (USERTYPE is null or USERTYPE != 1) and RELATIONREQUESTID = A.ID) AS PHONES, ";
            sql += " B.PERSONNAME, B.PERSONNUMBER, B.VALIDITYTIME from COMPANYBASEINFO A ";
            sql += "INNER JOIN COMPANYEMPLOYEES B on A.ID = B.RELATIONREQUESTID ";
            sql += "where A.COMPANYTYPE = '测绘单位' and B.PERSONROLE = '注册测绘师' ";
            sql += $"and (B.VALIDITYTIME - SYSDATE) < 30 ";
            sql +=
                "and NOT EXISTS (select 1 from COMPANYREMINDLOG where PERSONNAME = B.PERSONNAME and PERSONNUMBER = B.PERSONNUMBER and VALIDITYTIME = B.VALIDITYTIME) ";

            List<CompanyRemindLog> list = new List<CompanyRemindLog>();
            using (OracleDataService service = new OracleDataService()) {
                var dt = service.ExecuteQuerySql(sql);
                if (dt.Rows.Count > 0) {
                    foreach (DataRow row in dt.Rows) {
                        list.Add(new CompanyRemindLog {
                            ID = Guid.NewGuid().ToString("N"),
                            CompanyId = row["COMPANYID"].ToString(),
                            CompanyName = row["COMPANYNAME"].ToString(), 
                            RemindPhones = row["PHONES"].ToString(),
                            PersonName = row["PERSONNAME"].ToString(),
                            PersonNumber = row["PERSONNUMBER"].ToString(),
                            ValidityTime = Convert.ToDateTime(row["VALIDITYTIME"])
                        });
                    }

                    //发送短信和写入数据库
                    var companyInfoList = list.Where(s => !string.IsNullOrWhiteSpace(s.RemindPhones))
                        .GroupBy(s => new{ s.CompanyId, s.CompanyName })
                        .Select(s => s.Key)
                        .ToList();
                    console?.WriteLine($"共有{companyInfoList.Count}条需要提醒的数据");
                    foreach (var companyInfo in companyInfoList) {
                        var remindLogs = list.Where(s => s.CompanyId == companyInfo.CompanyId);
                        Hangfire.BackgroundJob.Enqueue(() => RemindCompanyAdmin(remindLogs, null));
                    }
                }
                else {
                    console?.WriteLine("没有需要提醒的数据");
                }
            }
        }

        /// <summary>
        /// 短信提醒单位管理员即将有到期的注册测绘师
        /// </summary>
        /// <param name="remindLogs"></param>
        /// <param name="console"></param>
        /// <returns></returns>
        [DisplayName("短信提醒单位管理员即将有到期的注册测绘师"), AutomaticRetry(Attempts = 0)]
        public static async Task RemindCompanyAdmin(IEnumerable<CompanyRemindLog> remindLogs, PerformContext console) {
            //发送短信
            string str = "已";
            if (remindLogs.Any(s => s.ValidityTime.HasValue && s.ValidityTime.Value.Date >= DateTime.Now.Date)) {
                str = "即将";
            }
            var remindPhones = remindLogs.FirstOrDefault().RemindPhones;
            var remindPhoneArr = remindPhones.Split(',');
            foreach (var phone in remindPhoneArr) {
                string personNames = string.Join("、", remindLogs.Select(s => s.PersonName));
                string smsContent = 
                    $"{remindLogs.FirstOrDefault().CompanyName}：系统检测到贵单位绑定的注册测绘师{personNames}注册有效期{str}过期，建议贵单位及时核对注册测绘师注册有效期，证书过期后注册测绘师将无法在南宁市多测合一系统上对测绘单位提交的测绘成果进行授权。（南宁市不动产登记中心，联系电话：0771-4306662）";
                Hangfire.BackgroundJob.Enqueue(() =>
                    SmsService.HangfireSendSms(phone, smsContent, null));
            }

            console?.WriteLine("写入提醒日志表");
            //写入提醒日志表
            using (OracleDataService service = new OracleDataService()) {
                foreach (var remindLog in remindLogs) {
                    remindLog.CreateTime = DateTime.Now;
                    service.InsertOne(remindLog);
                }
            }
        }



        /// <summary>
        /// 推送单个单位信息到综合服务平台
        /// </summary>
        /// <param name="companyBaseInfo"></param>
        /// <returns></returns>
        private static async Task<ApiResult> SyncCompanyInfo(CompanyBaseInfo companyBaseInfo) {
            var httpClient = HttpClientFactory.Create();
            httpClient.DefaultRequestHeaders.Add("AuthCode", "SDCCommonAuthorize");

            var data = new {
                @Type = companyBaseInfo.CompanyType, @Name = companyBaseInfo.CompanyName,
                @Number = AESEncrypt.Encrypt(companyBaseInfo.CreditCode), @Phone = companyBaseInfo.ContacterPhone
            };
            string url = $"{ExternalApiConfig.BDCWebServiceUrl}/suapi/SyncCompanyInfo";
            using (StringContent content = new StringContent(JsonConvert.SerializeObject(data), Encoding.UTF8, "application/json")) {
                var responseMessage = await httpClient.PostAsync(url, content);
                if (responseMessage.IsSuccessStatusCode) {
                    var responseTxt = await responseMessage.Content.ReadAsStringAsync();
                    var response = JsonConvert.DeserializeObject<ApiResult>(responseTxt);
                    return response;
                }
                else {
                    throw new Exception($"调用综合服务平台接口返回错误状态码：{(int) responseMessage.StatusCode}");
                }
            }
        }
    }
}
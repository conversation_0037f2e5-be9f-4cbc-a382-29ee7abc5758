﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace SDCPCWeb.Models.External {
    /// <summary>
    /// 已确认的不动产预测绘成果列表
    /// </summary>
    public class SurveyProjectItem {
        /// <summary>
        /// 业务ID
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 多测合一业务号
        /// </summary>
        public string BusinessNo { get; set; }
        /// <summary>
        /// 多测合一项目名称
        /// </summary>
        public string ProjectName { get; set; }
        /// <summary>
        /// 工程建设规划许可证号
        /// </summary>
        public string PlanCertificateCode { get; set; }
        /// <summary>
        /// 宗地代码
        /// </summary>
        public string ParcelNo { get; set; }
        /// <summary>
        /// 建设单位
        /// </summary>
        public string ConstructCompany { get; set; }
        /// <summary>
        /// 测绘单位
        /// </summary>
        public string SurveyCompany { get; set; }
        /// <summary>
        /// 规划许可信息
        /// </summary>
        public string PlanCertificateInfo { get; set; }

        /// <summary>
        /// 主查询语句
        /// </summary>
        /// <remarks>参数顺序：userid，companyNo</remarks>
        public static string MainSql =>
            "SELECT BASE.ID,BASE.BUSINESSNUMBER \"BusinessNo\",BASE.BUSINESSNAME \"ProjectName\",SUBSTR(TO_CHAR(CONTENT.PROJECTPLANPERMISSION), 11, 15) \"PlanCertificateCode\"\n" +
            ",CONTENT.GROUNDCODE \"ParcelNo\",BASE.DEVELOPERNAME \"ConstructCompany\",BASE.SURVEYCOMPANYNAME \"SurveyCompany\"\n" +
            "FROM BUSINESSBASEINFO BASE\n" +
            "INNER JOIN PRESURVEY_CONTENTINFO CONTENT ON BASE.ID=CONTENT.ID\n" +
            "INNER JOIN ESTATEPROJECTINFO PROJECT ON BASE.ID=PROJECT.PRESURVEYID AND PROJECT.PRESURVEYSTATE IN(1,3)\n" +
            "WHERE BASE.STATECODE=2 AND CONTENT.DATACHECKSTATE=1\n" +
            "AND BASE.DEVELOPERNO='{1}' AND BASE.CREATEUSERID='{0}'";

    }
}
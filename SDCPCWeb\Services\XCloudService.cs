﻿using Hangfire.Console;
using Hangfire.Server;
using SDCPCWeb.Models.System;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Hangfire;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SDCPCWeb.Controllers;
using SDCPCWeb.Extensions;
using SDCPCWeb.Models.BusinessFlow;

namespace SDCPCWeb.Services {
    /// <summary>
    /// 云平台服务类
    /// </summary>
    public class XCloudService {
        private static string xCloudApiUrl = ExternalApiConfig.xCloudApiUrl;
        private static string CH_xCloudApiUrl = ExternalApiConfig.CH_xCloudApiUrl;

        /// <summary>
        /// 推送到云平台自动办理业务
        /// </summary>
        /// <param name="id">业务id</param>
        /// <param name="console"></param>
        /// <returns></returns>
        [DisplayName("通知云平台创建业务")]
        public static async Task<string> ToXCloudCreateBusiness(string id, string businessClass, PerformContext console) {
            string path = "";
            switch (businessClass) {
                case nameof(RealEstatePreCheckSurveyAutoFlow): {
                        path = "zdyhch";
                        break;
                    }
                case nameof(CouncilMeasurePutLinePreCheckFlow): {
                        path = "zdszfx";
                        break;
                    }
            }

            string url = $"{xCloudApiUrl}/dc/{path}/automatic?id={id}";
            var httpClient = HttpClientFactory.Create();
            using (StringContent content = new StringContent("", Encoding.UTF8, "application/x-www-form-urlencoded")) {
                var responseMessage = await httpClient.PostAsync(url, content);
                if (responseMessage.IsSuccessStatusCode) {
                    var responseContent = await responseMessage.Content.ReadAsStringAsync();
                    console?.WriteLine(console);
                    if (responseContent != "true") {
                        throw new Exception(responseContent);
                    }

                    return responseContent;
                } else {
                    throw new Exception($"StatusCode：{(int)responseMessage.StatusCode}");
                }
            }
        }

        /// <summary>
        /// 通知云平台办结业务
        /// </summary>
        /// <param name="id">业务id</param>
        /// <param name="console"></param>
        /// <returns></returns>
        [DisplayName("通知云平台办结业务")]
        public static async Task<string> ToXCloudCompletedBusiness(string id, string businessClass, PerformContext console) {
            string path = "";
            switch (businessClass) {
                case nameof(RealEstatePreCheckSurveyAutoFlow): {
                        path = "zdyhch";
                        break;
                    }
            }

            string url = $"{xCloudApiUrl}/dc/{path}/SaveEnd?businessID={id}";
            var httpClient = HttpClientFactory.Create();
            using (StringContent content = new StringContent("", Encoding.UTF8, "application/x-www-form-urlencoded")) {
                var responseMessage = await httpClient.PostAsync(url, content);
                if (responseMessage.IsSuccessStatusCode) {
                    var responseContent = await responseMessage.Content.ReadAsStringAsync();
                    console?.WriteLine(console);
                    if (responseContent != "true") {
                        throw new Exception(responseContent);
                    }

                    return responseContent;
                } else {
                    throw new Exception($"StatusCode：{(int)responseMessage.StatusCode}");
                }
            }
        }

        /// <summary>
        /// 检查是否是退回替换MDB
        /// </summary>
        /// <param name="id">业务id</param>
        /// <returns></returns>
        public static async Task<bool> CheckIsReplaceMDB(string id, string businessClass) {
            string path = "";
            switch (businessClass) {
                case nameof(RealEstateActualSurveyFlow): {
                        path = "shch";
                        break;
                    }
                case nameof(CouncilPlanCheckFlow): {
                        path = "szgc";
                        break;
                    }
                case nameof(RealEstateOverallActualSurveyFlow): {
                        path = "qmch";
                        break;
                    }
            }
            string url = $"{xCloudApiUrl}/dc/{path}/IsReplaceMDB?xsywid={id}";
            var httpClient = HttpClientFactory.Create();
            using (StringContent content = new StringContent("", Encoding.UTF8, "application/x-www-form-urlencoded")) {
                var responseMessage = await httpClient.PostAsync(url, content);
                if (responseMessage.IsSuccessStatusCode) {
                    var responseContent = await responseMessage.Content.ReadAsStringAsync();
                    return Convert.ToBoolean(responseContent);
                } else {
                    throw new Exception("检查是否是退回替换MDB接口返回异常，请联系管理员");
                }
            }
        }

        /// <summary>
        /// 设置完成替换MDB
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [DisplayName("通知云平台完成替换MDB")]
        public static async Task<string> SetFinishReplaceMDB(string id, string businessClass, PerformContext console) {

            //判断是否是替换MDB
            console?.WriteLine("判断是否是替换MDB");
            var result = await CheckIsReplaceMDB(id, businessClass);
            console?.WriteLine($"判断是否是替换MDB返回：{result}");

            if (result == false) {
                return "不是替换MDB";
            }

            //自动签收
            console?.WriteLine("自动签收");
            EstateProjectAuditController controller = new EstateProjectAuditController();
            var signResult = controller.SignProjectAudit(id);
            if (signResult.StateCode != 1) {
                throw new Exception($"自动签收失败：{signResult.Message}");
            }

            string path = "";
            switch (businessClass) {
                case nameof(RealEstateActualSurveyFlow): {
                        path = "shch";
                        break;
                    }
                case nameof(CouncilPlanCheckFlow): {
                        path = "szgc";
                        break;
                    }
                case nameof(RealEstateOverallActualSurveyFlow): {
                        path = "qmch";
                        break;
                    }
            }

            console?.WriteLine("通知云平台完成替换MDB");
            string url = $"{xCloudApiUrl}/dc/{path}/IsFinishMDB?xsywid={id}";
            var httpClient = HttpClientFactory.Create();
            using (StringContent content = new StringContent("", Encoding.UTF8, "application/x-www-form-urlencoded")) {
                var responseMessage = await httpClient.PostAsync(url, content);
                if (responseMessage.IsSuccessStatusCode) {
                    var responseContent = await responseMessage.Content.ReadAsStringAsync();
                    console?.WriteLine(responseContent);
                    if (responseContent != "true") {
                        throw new Exception(responseContent);
                    }

                    return responseContent;
                } else {
                    throw new Exception($"StatusCode：{(int)responseMessage.StatusCode}");
                }
            }
        }

        /// <summary>
        /// 获取市政核实业务竣工红线图
        /// </summary>
        /// <param name="id"></param>
        /// <returns>Item1：文件流；Item2：文件名；Item3：MediaType</returns>
        public static async Task<Tuple<Stream, string, string>> GetJGHXT(string id) {
            string url = $"{xCloudApiUrl}/dc/szgc/GetJGHXImage?xsywid={id}";
            var httpClient = HttpClientFactory.Create();
            var responseMessage = await httpClient.GetAsync(url);
            if (responseMessage.IsSuccessStatusCode) {
                var responseContent = await responseMessage.Content.ReadAsStringAsync();
                if (responseContent.Contains("null")) {
                    return null;
                } else {
                    string getStreamUrl = responseContent.Replace("\"", "");
                    var streamResponseMessage = await httpClient.GetAsync(getStreamUrl);
                    if (streamResponseMessage.IsSuccessStatusCode) {
                        string fileName = streamResponseMessage.Content.Headers.ContentDisposition.FileName;
                        string contentType = streamResponseMessage.Content.Headers.ContentType.MediaType;
                        var stream = await streamResponseMessage.Content.ReadAsStreamAsync();
                        return new Tuple<Stream, string, string>(stream, fileName, contentType);
                    }
                }
            }


            return null;
        }

        /// <summary>
        /// 获取审批意见
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static async Task<JObject> GetSpyjByBusinessId(string id, string businessClass) {
            //修改为不获取审批意见
            return JObject.FromObject(new { Message = "", Data = JArray.FromObject(Array.Empty<string>()) });

            int type = 1;
            switch (businessClass) {
                case nameof(RealEstateActualSurveyFlow): {
                        type = 1;
                        break;
                    }
                case nameof(CouncilPlanCheckFlow): {
                        type = 2;
                        break;
                    }
                case nameof(RealEstatePreSurveyBuildingTableChangeFlow):
                case nameof(RealEstatePreSurveyResultChangeFlow):
                case nameof(RealEstateActualResultChangeFlow): {
                        type = 5;
                        break;
                    }
            }

            string url = $"{xCloudApiUrl}/dc/cmm/GetSpyjByXsywId?xsywid={id}&type={type}";
            var httpClient = HttpClientFactory.Create();
            var responseMessage = await httpClient.GetAsync(url);
            if (responseMessage.IsSuccessStatusCode) {
                var responseContent = await responseMessage.Content.ReadAsStringAsync();
                var arr = JArray.Parse(responseContent);
                if (arr.Any()) {
                    var data = arr.OrderByDescending(s => s["UpdateTime"].ToObject<DateTime>())
                        .Select(s => new { CSYJ = s["CSYJ"].ToString(), SHYJ = s["SHYJ"].ToString() })
                        .Take(1);

                    //两个意见不为空才返回
                    data = data.Where(s =>
                        !string.IsNullOrWhiteSpace(s.CSYJ) ||
                        !string.IsNullOrWhiteSpace(s.SHYJ));

                    return JObject.FromObject(new { Message = "", Data = JArray.FromObject(data) });
                } else {
                    return JObject.FromObject(new { Message = "", Data = arr });
                }
            } else {
                return JObject.FromObject(new { Message = "获取审批意见接口返回异常，请联系管理员" });
            }
        }

        /// <summary>
        /// 获取流程审批记录
        /// </summary>
        /// <param name="id"></param>
        /// <param name="businessClass"></param>
        /// <returns></returns>
        public static async Task<Tuple<string, JArray>> GetFlowStepRecordsByBusinessId(string id, string businessClass) {
            int type = 1;
            switch (businessClass) {
                case nameof(RealEstateActualSurveyFlow): {
                        type = 1;
                        break;
                    }
                case nameof(CouncilPlanCheckFlow): {
                        type = 2;
                        break;
                    }
                case nameof(RealEstatePreSurveyBuildingTableChangeFlow):
                case nameof(RealEstatePreSurveyResultChangeFlow):
                case nameof(RealEstateActualResultChangeFlow): {
                        type = 5;
                        break;
                    }
                case nameof(RealEstateOverallActualSurveyFlow): {
                        type = 7;
                        break;
                    }
            }

            string url = $"{xCloudApiUrl}/dc/cmm/GetFlowByXSYWID?xsywid={id}&type={type}";
            var httpClient = HttpClientFactory.Create();
            var responseMessage = await httpClient.GetAsync(url);
            if (responseMessage.IsSuccessStatusCode) {
                var responseContent = await responseMessage.Content.ReadAsStringAsync();
                var arr = JArray.Parse(responseContent);
                if (arr.Any()) {
                    var data = JArray.FromObject(arr.OrderByDescending(s => s["StartDate"].ToObject<DateTime>()));

                    return new Tuple<string, JArray>(null, data);
                } else {
                    return new Tuple<string, JArray>(null, arr);
                }
            } else {
                return new Tuple<string, JArray>("获取审批进度接口返回异常，请联系管理员", null);
            }
        }

        /// <summary>
        /// 判断实核业务是否只做实核测绘业务，返回true表示只做实核
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static async Task<bool> CheckIsOnlySC(string id, string businessClass) {
            string url = $"{xCloudApiUrl}/dc/shch/IsOnlySC?xsywid={id}";

            switch (businessClass) {
                default: break;
                case nameof(RealEstateActualResultChangeFlow): {
                        url = $"{xCloudApiUrl}/dc/shcgbg/IsOnlySC?xsywid={id}";
                        break;
                    }
            }

            var httpClient = HttpClientFactory.Create();
            using (StringContent content = new StringContent("", Encoding.UTF8, "application/x-www-form-urlencoded")) {
                var responseMessage = await httpClient.PostAsync(url, content);
                if (responseMessage.IsSuccessStatusCode) {
                    var responseContent = await responseMessage.Content.ReadAsStringAsync();

                    if (responseContent == "true") {
                        return true;
                    }

                    return false;
                } else {
                    throw new Exception($"StatusCode：{(int)responseMessage.StatusCode}");
                }
            }
        }

        /// <summary>
        /// 获取云平台挂起信息
        /// </summary>
        /// <param name="id">业务id</param>
        /// <param name="businessClass">业务类型</param>
        /// <returns>Item1：是否挂起；Item2：挂起时间；Item3：挂起原因</returns>
        public static async Task<JArray> GetSuspendInfo(string id, string businessClass) {
            int type = 1;
            switch (businessClass) {
                case nameof(RealEstateActualSurveyFlow): {
                        type = 1;
                        break;
                    }
                case nameof(CouncilPlanCheckFlow): {
                        type = 2;
                        break;
                    }
                case nameof(RealEstatePreSurveyBuildingTableChangeFlow):
                case nameof(RealEstatePreSurveyResultChangeFlow):
                case nameof(RealEstateActualResultChangeFlow): {
                        type = 5;
                        break;
                    }
                case nameof(MeasurePutLinePreCheckFlow): {
                        type = 6;
                        break;
                    }
                case nameof(RealEstateOverallActualSurveyFlow): {
                        type = 7;
                        break;
                    }
            }

            string url = $"{xCloudApiUrl}/dc/cmm/GetSuspend?id={id}&type={type}";
            var httpClient = HttpClientFactory.Create();
            var responseMessage = await httpClient.GetAsync(url);
            if (responseMessage.IsSuccessStatusCode) {
                var responseContent = await responseMessage.Content.ReadAsStringAsync();

                if (string.IsNullOrWhiteSpace(responseContent?.Replace("\"", "")) || responseContent == "null") {

                    return new JArray();
                }

                //挂起时间
                string time = responseContent.Split('：')[0].Split('[')[1].TrimEnd(']');

                //挂起原因
                string reason = responseContent.Split('：')[1].TrimEnd('"');

                List<dynamic> list = new List<dynamic>();
                list.Add(new { suspendTime = time, suspendReason = reason });

                return JArray.FromObject(list);

            } else {
                throw new Exception($"StatusCode：{(int)responseMessage.StatusCode}");
            }

        }

        /// <summary>
        /// 获取规划核实证明
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static async Task<Tuple<Stream, string, string>> GetGHHSZM(string id, PerformContext console = null) {
            string url = $"{xCloudApiUrl}/dc/shch/GetGhhszmByWWID?wwid={id}";

            console?.WriteLine(url);

            var httpClient = HttpClientFactory.Create();
            var streamResponseMessage = await httpClient.GetAsync(url);
            if (streamResponseMessage.IsSuccessStatusCode) {
                string fileName = streamResponseMessage.Content.Headers.ContentDisposition.FileName;
                string contentType = streamResponseMessage.Content.Headers.ContentType.MediaType;
                var stream = await streamResponseMessage.Content.ReadAsStreamAsync();
                return new Tuple<Stream, string, string>(stream, fileName, contentType);
            } else {
                throw new Exception($"接口返回状态失败：{(int)streamResponseMessage.StatusCode}");
            }
        }

        /// <summary>
        /// 批量判断工规证号是否已办理规划核实业务
        /// </summary>
        /// <param name="nos">工程规划许可证号数组</param>
        /// <returns>Item1：返回消息；Item2：是否已办理</returns>
        public static async Task<Tuple<string, bool>> CheckIsCompletedGHHS(string[] nos) {
            try {
                string url = $"{xCloudApiUrl}/dc/shch/GetWxghsByGGZH";

                string postData = JsonConvert.SerializeObject(nos);
                var httpClient = HttpClientFactory.Create();
                using (StringContent content = new StringContent(postData, Encoding.UTF8, "application/json")) {
                    var responseMessage = await httpClient.PostAsync(url, content);
                    if (responseMessage.IsSuccessStatusCode) {
                        var responseContent = await responseMessage.Content.ReadAsStringAsync();
                        if (responseContent.Contains("null")) {
                            return new Tuple<string, bool>("调用接口方出现异常", false);
                        } else {
                            var items = JArray.Parse(responseContent);
                            return new Tuple<string, bool>(null, items.Any(s => s["state"].ToObject<bool?>() == false) == false);
                        }
                    } else {
                        return new Tuple<string, bool>($"调用接口返回状态失败：{(int)responseMessage.StatusCode}", false);
                    }
                }
            } catch {
                return new Tuple<string, bool>($"调用接口出现异常", false);
            }

        }

        /// <summary>
        /// 从缓存获取土地用途列表
        /// </summary>
        /// <param name="cache"></param>
        /// <returns></returns>
        public static async Task<Tuple<string, List<LandUseInfo>>> GetLandUseListCache(bool cache = true) {
            if (cache) {
                var cacheData = MongoCache.Get("cache_landuselist");
                if (!string.IsNullOrWhiteSpace(cacheData)) {
                    var list = JsonConvert.DeserializeObject<List<LandUseInfo>>(cacheData);
                    return new Tuple<string, List<LandUseInfo>>(null, list);
                }
                
            }

            var result = await GetLandUseList();
            if (string.IsNullOrWhiteSpace(result.Item1)) {
                MongoCache.Set("cache_landuselist", JsonConvert.SerializeObject(result.Item2), TimeSpan.FromHours(3));
                return new Tuple<string, List<LandUseInfo>>(null, result.Item2);
            } else {
                return new Tuple<string, List<LandUseInfo>>(result.Item1, null);
            }
        }


        /// <summary>
        /// 获取土地用途列表并更新缓存
        /// </summary>
        /// <param name="console"></param>
        /// <returns></returns>
        [DisplayName("获取土地用途列表并更新缓存"), AutomaticRetry(Attempts = 0)]
        public static async Task UpdateLandUseCache(PerformContext console = null) {
            var result = await GetLandUseList(console: console);
            
            if (string.IsNullOrWhiteSpace(result.Item1)) {
                MongoCache.Set("cache_landuselist", JsonConvert.SerializeObject(result.Item2), TimeSpan.FromHours(1));
            }

            console?.WriteLine(JsonConvert.SerializeObject(result));
        }

        /// <summary>
        /// 测绘单位注销推送云平台日志
        /// </summary>
        /// <param name="xsywid">业务id</param>
        /// <param name="sqdw">申请单位</param>
        /// <param name="dwdm">单位代码</param>
        /// <param name="dwlx">单位类型</param>
        /// <param name="sqr">申请人</param>
        /// <param name="sqrq">申请日期</param>
        /// <param name="sqrno">申请人证件号码</param>
        /// <param name="zxly">注销理由</param>
        /// <param name="zxsm">注销说明</param>
        /// <param name="bz">备注</param>
        /// <param name="console"></param>
        /// <returns></returns>
        [DisplayName("测绘单位注销推送云平台日志"), AutomaticRetry(Attempts = 0)]
        public static async Task<string> CompanyWithDrawToXCloudLog(string xsywid, string sqdw, string dwdm, string dwlx, string sqr, DateTime sqrq, string sqrno, string zxly, string zxsm, string bz = null, PerformContext console = null) {
            string url = $"{CH_xCloudApiUrl}/cehui/chzzsh/Sign_ZZZX";

            string postData = JsonConvert.SerializeObject(new {xsywid, sqdw, dwdm, dwlx, sqr, @sqrq = sqrq.ToString("yyyy-MM-dd HH:mm:ss"), sqrno, zxly, zxsm, bz});
            var httpClient = HttpClientFactory.Create();
            using (StringContent content = new StringContent(postData, Encoding.UTF8, "application/json")) {
                var responseMessage = await httpClient.PostAsync(url, content);
                if (responseMessage.IsSuccessStatusCode) {
                    var responseContent = await responseMessage.Content.ReadAsStringAsync();
                    console?.WriteLine(responseContent);
                    var response = JObject.Parse(responseContent);
                    if (response["stateCode"]?.ToString() != "1") {
                        throw new Exception($"调用接口返回失败：{response["message"]?.ToString()}");
                    }

                    return responseContent;
                } else {
                    throw new Exception($"调用接口返回状态失败：{(int)responseMessage.StatusCode}");
                }
            }
        }

        /// <summary>
        /// 获取土地用途列表
        /// </summary>
        /// <param name="parentTag">父级标识</param>
        /// <param name="cache">是否从缓存读取</param>
        /// <returns></returns>
        private static async Task<Tuple<string, List<LandUseInfo>>> GetLandUseList(string parentTag = null, PerformContext console = null) {
            try {
                if (string.IsNullOrWhiteSpace(parentTag))
                    parentTag = "GTKJYDFL_QT";
                string url = $"{xCloudApiUrl}/dc/cmm/GetChildListByTag?tag={parentTag}";

                var httpClient = HttpClientFactory.Create();
                var responseMessage = await httpClient.GetAsync(url);
                if (responseMessage.IsSuccessStatusCode) {
                    var responseContent = await responseMessage.Content.ReadAsStringAsync();
                    var items = JsonConvert.DeserializeObject<List<LandUseInfo>>(responseContent);
                    UpdateLandUseInfoChilds(items);
                    return new Tuple<string, List<LandUseInfo>>(null, items);
                } else {
                    return new Tuple<string, List<LandUseInfo>>($"调用接口返回状态失败：{(int)responseMessage.StatusCode}", null);
                }

            } catch (Exception e){
                return new Tuple<string, List<LandUseInfo>>($"调用接口出现异常：{e.GetStackTraces()}", null);
            }
        }

        /// <summary>
        /// 更新土地用途子级
        /// </summary>
        /// <param name="list"></param>
        private static void UpdateLandUseInfoChilds(List<LandUseInfo> list) {
            foreach (var landUseInfo in list) {
                if (landUseInfo.children.Any() == false) {
                    landUseInfo.children = null;
                }
                else {
                    UpdateLandUseInfoChilds(landUseInfo.children);
                }
            }
        }
    }

    /// <summary>
    /// 土地用途信息类
    /// </summary>
    public class LandUseInfo {
        /// <summary>
        /// id
        /// </summary>
        public string id { get; set; }

        /// <summary>
        /// 上级id
        /// </summary>
        public string parent_ID { get; set; }

        /// <summary>
        /// 土地用途名称
        /// </summary>
        public string name { get; set; }

        /// <summary>
        /// 编码
        /// </summary>
        public string code { get; set; }

        /// <summary>
        /// 标识
        /// </summary>
        public string tag { get; set; }

        /// <summary>
        /// 前端专用
        /// </summary>
        public string value => id;

        /// <summary>
        /// 前端专用
        /// </summary>
        public string label => name;

        /// <summary>
        /// 前端专用
        /// </summary>
        public List<LandUseInfo> children { get; set; } = null;
    }
}
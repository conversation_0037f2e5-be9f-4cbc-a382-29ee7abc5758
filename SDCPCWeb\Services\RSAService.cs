﻿using Org.BouncyCastle.Crypto.Parameters;
using Org.BouncyCastle.OpenSsl;
using SDCPCWeb.Models.System;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Web;

namespace SDCPCWeb.Services {
    /// <summary>
    /// RSA非对称加解密和加签验签服务
    /// </summary>
    public class RSAService {
        /// <summary>
        /// 公钥
        /// </summary>
        private string PublicKey { get; set; }
        /// <summary>
        /// 私钥
        /// </summary>
        private string PrivateKey { get; set; }

        /// <summary>
        /// 使用指定公钥和私钥实例化对象
        /// </summary>
        /// <param name="publicKey"></param>
        /// <param name="privateKey"></param>
        public RSAService(string publicKey, string privateKey) {
            PublicKey = publicKey;
            PrivateKey = privateKey;
        }

        /// <summary>
        /// 使用公钥实例化对象，私钥通过配置文件读取
        /// </summary>
        /// <param name="publicKey"></param>
        public RSAService(string publicKey) {
            PublicKey = publicKey;
            PrivateKey = File.ReadAllText(SystemConfig.PrivateKeyFile);
        }

        /// <summary>
        /// 实例化对象，仅通过配置文件读取私钥
        /// </summary>
        public RSAService() {
            PrivateKey = File.ReadAllText(SystemConfig.PrivateKeyFile);
        }

        /// <summary>
        /// 设置公钥，传入公钥字符串
        /// </summary>
        /// <param name="publicKey"></param>
        public void SetPublicKey(string publicKey) {
            PublicKey = publicKey;
        }

        /// <summary>
        /// 根据公钥文件设置公钥
        /// </summary>
        /// <param name="publicKeyFilePath"></param>
        public void LoadPublicKeyFile(string publicKeyFilePath) {
            if (File.Exists(publicKeyFilePath)) {
                PublicKey = File.ReadAllText(publicKeyFilePath);
                return;
            }
            throw new FileNotFoundException("文件不存在", publicKeyFilePath);
        }


        /// <summary>
        /// 根据公钥加密字符串数据，得到Base64加密字符串
        /// </summary>
        /// <param name="data">待加密字符串信息</param>
        /// <returns></returns>
        public string Encrypt(string data) {
            var publicKeyString = PublicKey;
            var header = "-----BEGIN CERTIFICATE-----";
            if (publicKeyString.Contains(header)) {
                return RSAEncryptUseCerFile(PublicKey, data);
            }

            header = "-----BEGIN PUBLIC KEY-----";
            if (publicKeyString.Contains(header)) {
                return RSAEncryptUsePEM(PublicKey, data);
            }
            using (RSACryptoServiceProvider rsa = new RSACryptoServiceProvider()) {
                rsa.PersistKeyInCsp = false;
                rsa.FromXmlString(PublicKey);
                var byteData = Encoding.UTF8.GetBytes(data);
                return RsaEncryptToBase64String(rsa, byteData);
            }
        }

        /// <summary>
        /// 使用公钥数字证书验证返回的数字签名
        /// </summary>
        /// <param name="data">验签数据</param>
        /// <param name="sign">数字签名</param>
        /// <returns></returns>
        public bool VerifyData(string data, string sign) {
            var publicKeyString = PublicKey;
            var header = "-----BEGIN CERTIFICATE-----";
            if (publicKeyString.Contains(header)) {
                return VerifyDataUseCerFile(PublicKey, data, sign);
            }

            header = "-----BEGIN PUBLIC KEY-----";
            if (publicKeyString.Contains(header)) {
                return VerifyDataUsePEM(PublicKey, data, sign);
            }
            using (RSACryptoServiceProvider rsa = new RSACryptoServiceProvider()) {
                rsa.PersistKeyInCsp = false;
                rsa.FromXmlString(PublicKey);
                byte[] byteData = Encoding.UTF8.GetBytes(data);
                byte[] signed = Convert.FromBase64String(sign);
                return rsa.VerifyData(byteData, "SHA256", signed);
            }
        }

        /// <summary>
        /// 使用XML私钥对加密字符串进行解密
        /// </summary>
        /// <param name="data">待解密密文字符串</param>
        /// <returns></returns>
        public string Decrypt(string data) {
            var privateKeyString = PrivateKey;
            var header1 = "-----BEGIN RSA PRIVATE KEY-----";
            var header2 = "-----BEGIN PRIVATE KEY-----";
            try {
                if (privateKeyString.Contains(header1) || privateKeyString.Contains(header2)) {
                    return RSADecryptUsePEM(PrivateKey, data);
                }
                using (RSACryptoServiceProvider privateRsa = new RSACryptoServiceProvider()) {
                    privateRsa.PersistKeyInCsp = false;
                    privateRsa.FromXmlString(PrivateKey);
                    return RsaDecryptToUTF8String(privateRsa, Convert.FromBase64String(data));
                }
            }
            catch (Exception) {
                //解密失败则统一返回null
                return null;
            }
        }

        /// <summary>
        /// 使用私钥对字符串进行数字签名
        /// </summary>
        /// <param name="dataToSign">待签名数据</param>
        /// <returns></returns>
        public string SignData(string dataToSign) {
            var privateKeyString = PrivateKey;
            var header1 = "-----BEGIN RSA PRIVATE KEY-----";
            var header2 = "-----BEGIN PRIVATE KEY-----";
            if (privateKeyString.Contains(header1) || privateKeyString.Contains(header2)) {
                return SignDataUsePEM(PrivateKey, dataToSign);
            }
            using (var privateRsa = new RSACryptoServiceProvider()) {
                privateRsa.PersistKeyInCsp = false;
                privateRsa.FromXmlString(PrivateKey);
                var signature = Convert.ToBase64String(privateRsa.SignData(Encoding.UTF8.GetBytes(dataToSign), HashAlgorithmName.SHA256,
                    RSASignaturePadding.Pkcs1));
                return signature;
            }
        }

        #region 加密、解密、加签、验签的支撑方法

        #region 私钥方法，加签、解密

        /// <summary>
        /// 使用私钥对字符串进行数字签名
        /// </summary>
        /// <param name="privateKey">PEM格式的私钥文件路径</param>
        /// <param name="dataToSign">待签名数据</param>
        /// <returns></returns>
        private static string SignDataUsePEM(string privateKey, string dataToSign) {
            var privateRsa = PemToRSAKey(privateKey, true);
            var signature = Convert.ToBase64String(privateRsa.SignData(Encoding.UTF8.GetBytes(dataToSign), HashAlgorithmName.SHA256,
                RSASignaturePadding.Pkcs1));
            return signature;
        }

        /// <summary>
        /// 使用PEM私钥解密字符串，如果密钥配对，则解密成功，密钥错误，则异常
        /// </summary>
        /// <param name="privateKey">私钥PEM文件路径</param>
        /// <param name="data">待解密是Base64字符串</param>
        /// <returns></returns>
        private static string RSADecryptUsePEM(string privateKey, string data) {
            var privateRsa = PemToRSAKey(privateKey, true);
            return RsaDecryptToUTF8String(privateRsa, Convert.FromBase64String(data));
        }

        #endregion

        #region 公钥方法，加密、验签

        /// <summary>
        /// 使用PEM公钥加密字符串数据，得到Base64加密字符串
        /// </summary>
        /// <param name="publicKey">公钥PEM文件路径</param>
        /// <param name="data">待加密字符串信息</param>
        /// <returns></returns>
        private static string RSAEncryptUsePEM(string publicKey, string data) {
            var rsa = PemToRSAKey(publicKey, false);
            rsa.PersistKeyInCsp = false;
            var byteData = Encoding.UTF8.GetBytes(data);
            return RsaEncryptToBase64String(rsa, byteData);
        }

        /// <summary>
        /// 根据公钥数字证书加密字符串数据，得到Base64加密字符串
        /// </summary>
        /// <param name="publicKey">公钥数字证书路径，一般为cer文件</param>
        /// <param name="data">待加密字符串信息</param>
        /// <returns></returns>
        private static string RSAEncryptUseCerFile(string publicKey, string data) {
            X509Certificate2 cer = new X509Certificate2();
            string header = "-----BEGIN CERTIFICATE-----";
            string footer = "-----END CERTIFICATE-----";
            int start = publicKey.IndexOf(header) + header.Length;
            int end = publicKey.IndexOf(footer, start) - start;
            var rawData = Convert.FromBase64String(publicKey.Substring(start, end));
            cer.Import(rawData);
            using (RSACryptoServiceProvider rsa = new RSACryptoServiceProvider()) {
                rsa.PersistKeyInCsp = false;
                rsa.FromXmlString(cer.PublicKey.Key.ToXmlString(false));
                var byteData = Encoding.UTF8.GetBytes(data);
                return RsaEncryptToBase64String(rsa, byteData);
            }
        }

        /// <summary>
        /// 使用公钥数字证书验证返回的数字签名
        /// </summary>
        /// <param name="publicKey">公钥数字证书文件路径，一般为cer文件</param>
        /// <param name="data">验签数据</param>
        /// <param name="sign">数字签名</param>
        /// <returns></returns>
        private static bool VerifyDataUseCerFile(string publicKey, string data, string sign) {
            X509Certificate2 cer = new X509Certificate2();
            string header = "-----BEGIN CERTIFICATE-----";
            string footer = "-----END CERTIFICATE-----";
            int start = publicKey.IndexOf(header) + header.Length;
            int end = publicKey.IndexOf(footer, start) - start;
            var rawData = Convert.FromBase64String(publicKey.Substring(start, end));
            cer.Import(rawData);

            using (RSACryptoServiceProvider rsa = new RSACryptoServiceProvider()) {
                rsa.PersistKeyInCsp = false;
                rsa.FromXmlString(cer.PublicKey.Key.ToXmlString(false));
                byte[] byteData = Encoding.UTF8.GetBytes(data);
                byte[] signed = Convert.FromBase64String(sign);
                return rsa.VerifyData(byteData, "SHA256", signed);
            }
        }

        /// <summary>
        /// 使用PEM公钥文件验证返回的数字签名
        /// </summary>
        /// <param name="publicKey">PEM公钥文件路径</param>
        /// <param name="data">验签数据</param>
        /// <param name="sign">数字签名</param>
        /// <returns></returns>
        private static bool VerifyDataUsePEM(string publicKey, string data, string sign) {
            var rsa = PemToRSAKey(publicKey, false);
            rsa.PersistKeyInCsp = false;
            byte[] byteData = Encoding.UTF8.GetBytes(data);
            byte[] signed = Convert.FromBase64String(sign);
            return rsa.VerifyData(byteData, "SHA256", signed);
        }

        /// <summary>
        /// 公钥加密方法，支持长数据分段加密
        /// </summary>
        /// <param name="rsa"></param>
        /// <param name="allToEncryptDataBytes"></param>
        /// <returns></returns>
        private static string RsaEncryptToBase64String(RSACryptoServiceProvider rsa, byte[] allToEncryptDataBytes) {
            //加密长度太长，进行分段加密
            // ReSharper disable once IdentifierTypo
            var maxblocksize = rsa.KeySize / 8 - 11;
            if (allToEncryptDataBytes.Length > maxblocksize) {
                using (var plainStream = new MemoryStream(allToEncryptDataBytes))
                using (var cryptStream = new MemoryStream()) {
                    var buffer = new byte[maxblocksize];
                    var readBlockSize = plainStream.Read(buffer, 0, maxblocksize);
                    while (readBlockSize > 0) {
                        var toEncrypt = new byte[readBlockSize];
                        Array.Copy(buffer, 0, toEncrypt, 0, readBlockSize);
                        var encrypted = rsa.Encrypt(toEncrypt, false);
                        cryptStream.Write(encrypted, 0, encrypted.Length);

                        readBlockSize = plainStream.Read(buffer, 0, maxblocksize);
                    }

                    return Convert.ToBase64String(cryptStream.ToArray(), Base64FormattingOptions.None);
                }
            }

            var cryptData = rsa.Encrypt(allToEncryptDataBytes, false);
            return Convert.ToBase64String(cryptData);
        }

        /// <summary>
        /// 公钥解密方法，支持长数据分段解密
        /// </summary>
        /// <param name="rsa"></param>
        /// <param name="allToDecryptDataBytes"></param>
        /// <returns></returns>
        private static string RsaDecryptToUTF8String(RSACryptoServiceProvider rsa, byte[] allToDecryptDataBytes) {
            //加密长度太长，进行分段加密
            // ReSharper disable once IdentifierTypo
            var maxblocksize = rsa.KeySize / 8;
            if (allToDecryptDataBytes.Length > maxblocksize) {
                using (var cryptStream = new MemoryStream(allToDecryptDataBytes))
                using (var plainStream = new MemoryStream()) {
                    var buffer = new byte[maxblocksize];
                    var readBlockSize = cryptStream.Read(buffer, 0, maxblocksize);
                    while (readBlockSize > 0) {
                        var toDecrypt = new byte[readBlockSize];
                        Array.Copy(buffer, 0, toDecrypt, 0, readBlockSize);
                        var plainBytes = rsa.Decrypt(toDecrypt, false);
                        plainStream.Write(plainBytes, 0, plainBytes.Length);

                        readBlockSize = cryptStream.Read(buffer, 0, maxblocksize);
                    }

                    return Encoding.UTF8.GetString(plainStream.ToArray());
                }
            }

            var cryptData = rsa.Decrypt(allToDecryptDataBytes, false);
            return Encoding.UTF8.GetString(cryptData);
        }


        #endregion

        #region 密钥转换支撑方法

        /// <summary>
        /// 通过Pem密钥字符串创建RSA密钥<see cref="RSACryptoServiceProvider"/>对象
        /// </summary>
        /// <param name="pemKey">Pem密钥内容</param>
        /// <param name="isPrivateKey">是否是私钥</param>
        /// <returns>RSA密钥</returns>
        private static RSACryptoServiceProvider PemToRSAKey(string pemKey, bool isPrivateKey) {
            string header = "-----BEGIN RSA PRIVATE KEY-----";
            if (pemKey.Contains(header)) {
                return DecodeRsaPrivateKey(pemKey);
            }
            object pemObject;
            RSAParameters rsaPara = new RSAParameters();
            using (StringReader sReader = new StringReader(pemKey)) {
                var pemReader = new PemReader(sReader);
                pemObject = pemReader.ReadObject();
            }
            //RSA私钥
            if (isPrivateKey) {
                RsaPrivateCrtKeyParameters key = (RsaPrivateCrtKeyParameters)pemObject;
                rsaPara = new RSAParameters {
                    Modulus = key.Modulus.ToByteArrayUnsigned(),
                    Exponent = key.PublicExponent.ToByteArrayUnsigned(),
                    D = key.Exponent.ToByteArrayUnsigned(),
                    P = key.P.ToByteArrayUnsigned(),
                    Q = key.Q.ToByteArrayUnsigned(),
                    DP = key.DP.ToByteArrayUnsigned(),
                    DQ = key.DQ.ToByteArrayUnsigned(),
                    InverseQ = key.QInv.ToByteArrayUnsigned(),
                };
            }
            //RSA公钥
            else {
                RsaKeyParameters key = (RsaKeyParameters)pemObject;
                rsaPara = new RSAParameters {
                    Modulus = key.Modulus.ToByteArrayUnsigned(),
                    Exponent = key.Exponent.ToByteArrayUnsigned(),
                };
            }
            RSACryptoServiceProvider rsa = new RSACryptoServiceProvider();
            rsa.ImportParameters(rsaPara);
            return rsa;
        }

        /// <summary>
        /// 将Rsa私钥转换为RSACryptoServiceProvider对象
        /// </summary>
        /// <param name="pemRsaPrivateKey"></param>
        /// <returns></returns>
        private static RSACryptoServiceProvider DecodeRsaPrivateKey(string pemRsaPrivateKey) {

            string header = "-----BEGIN RSA PRIVATE KEY-----";
            string footer = "-----END RSA PRIVATE KEY-----";

            int start = pemRsaPrivateKey.IndexOf(header, StringComparison.Ordinal) + header.Length;
            int end = pemRsaPrivateKey.IndexOf(footer, start, StringComparison.Ordinal) - start;

            var privateKeyBytes = Convert.FromBase64String(pemRsaPrivateKey.Substring(start, end));

            MemoryStream ms = new MemoryStream(privateKeyBytes);
            BinaryReader rd = new BinaryReader(ms);

            try {
                byte byteValue;
                ushort shortValue;

                shortValue = rd.ReadUInt16();

                switch (shortValue) {
                    case 0x8130:
                        // If true, data is little endian since the proper logical seq is 0x30 0x81
                        rd.ReadByte(); //advance 1 byte
                        break;
                    case 0x8230:
                        rd.ReadInt16();  //advance 2 bytes
                        break;
                    default:
                        Debug.Assert(false);     // Improper ASN.1 format
                        return null;
                }

                shortValue = rd.ReadUInt16();
                if (shortValue != 0x0102) // (version number)
                {
                    Debug.Assert(false);     // Improper ASN.1 format, unexpected version number
                    return null;
                }

                byteValue = rd.ReadByte();
                if (byteValue != 0x00) {
                    Debug.Assert(false);     // Improper ASN.1 format
                    return null;
                }

                // The data following the version will be the ASN.1 data itself, which in our case
                // are a sequence of integers.

                // In order to solve a problem with instancing RSACryptoServiceProvider
                // via default constructor on .net 4.0 this is a hack
                CspParameters parms = new CspParameters();
                parms.Flags = CspProviderFlags.NoFlags;
                parms.KeyContainerName = Guid.NewGuid().ToString().ToUpperInvariant();
                parms.ProviderType = ((Environment.OSVersion.Version.Major > 5) || ((Environment.OSVersion.Version.Major == 5) && (Environment.OSVersion.Version.Minor >= 1))) ? 0x18 : 1;

                RSACryptoServiceProvider rsa = new RSACryptoServiceProvider(parms);
                RSAParameters rsAparams = new RSAParameters();

                rsAparams.Modulus = rd.ReadBytes(DecodeIntegerSize(rd));

                // Argh, this is a pain.  From emperical testing it appears to be that RSAParameters doesn't like byte buffers that
                // have their leading zeros removed.  The RFC doesn't address this area that I can see, so it's hard to say that this
                // is a bug, but it sure would be helpful if it allowed that. So, there's some extra code here that knows what the
                // sizes of the various components are supposed to be.  Using these sizes we can ensure the buffer sizes are exactly
                // what the RSAParameters expect.  Thanks, Microsoft.
                RSAParameterTraits traits = new RSAParameterTraits(rsAparams.Modulus.Length * 8);

                rsAparams.Modulus = AlignBytes(rsAparams.Modulus, traits.size_Mod);
                rsAparams.Exponent = AlignBytes(rd.ReadBytes(DecodeIntegerSize(rd)), traits.size_Exp);
                rsAparams.D = AlignBytes(rd.ReadBytes(DecodeIntegerSize(rd)), traits.size_D);
                rsAparams.P = AlignBytes(rd.ReadBytes(DecodeIntegerSize(rd)), traits.size_P);
                rsAparams.Q = AlignBytes(rd.ReadBytes(DecodeIntegerSize(rd)), traits.size_Q);
                rsAparams.DP = AlignBytes(rd.ReadBytes(DecodeIntegerSize(rd)), traits.size_DP);
                rsAparams.DQ = AlignBytes(rd.ReadBytes(DecodeIntegerSize(rd)), traits.size_DQ);
                rsAparams.InverseQ = AlignBytes(rd.ReadBytes(DecodeIntegerSize(rd)), traits.size_InvQ);

                rsa.ImportParameters(rsAparams);
                return rsa;
            }
            catch (Exception) {
                Debug.Assert(false);
                return null;
            }
            finally {
                rd.Close();
            }
        }

        /// <summary>
        /// 二进制流解码
        /// </summary>
        /// <param name="rd"></param>
        /// <returns></returns>
        private static int DecodeIntegerSize(BinaryReader rd) {
            byte byteValue;
            int count;

            byteValue = rd.ReadByte();
            if (byteValue != 0x02)        // indicates an ASN.1 integer value follows
                return 0;

            byteValue = rd.ReadByte();
            if (byteValue == 0x81) {
                count = rd.ReadByte();    // data size is the following byte
            }
            else if (byteValue == 0x82) {
                byte hi = rd.ReadByte();  // data size in next 2 bytes
                byte lo = rd.ReadByte();
                count = BitConverter.ToUInt16(new[] { lo, hi }, 0);
            }
            else {
                count = byteValue;        // we already have the data size
            }

            //remove high order zeros in data
            while (rd.ReadByte() == 0x00) {
                count -= 1;
            }
            rd.BaseStream.Seek(-1, SeekOrigin.Current);

            return count;
        }

        /// <summary>
        /// 对齐字节
        /// </summary>
        /// <param name="inputBytes"></param>
        /// <param name="alignSize"></param>
        /// <returns></returns>
        public static byte[] AlignBytes(byte[] inputBytes, int alignSize) {
            int inputBytesSize = inputBytes.Length;

            if ((alignSize != -1) && (inputBytesSize < alignSize)) {
                byte[] buf = new byte[alignSize];
                for (int i = 0; i < inputBytesSize; ++i) {
                    buf[i + (alignSize - inputBytesSize)] = inputBytes[i];
                }
                return buf;
            }
            else {
                return inputBytes;      // Already aligned, or doesn't need alignment
            }
        }

        #endregion

        #endregion
    }

    /// <summary>
    /// RSA参数
    /// </summary>
    internal class RSAParameterTraits {
        public RSAParameterTraits(int modulusLengthInBits) {
            // The modulus length is supposed to be one of the common lengths, which is the commonly referred to strength of the key,
            // like 1024 bit, 2048 bit, etc.  It might be a few bits off though, since if the modulus has leading zeros it could show
            // up as 1016 bits or something like that.
            int assumedLength = -1;
            double logbase = Math.Log(modulusLengthInBits, 2);
            if (logbase == (int)logbase) {
                // It's already an even power of 2
                assumedLength = modulusLengthInBits;
            }
            else {
                // It's not an even power of 2, so round it up to the nearest power of 2.
                assumedLength = (int)(logbase + 1.0);
                assumedLength = (int)(Math.Pow(2, assumedLength));
                System.Diagnostics.Debug.Assert(false);  // Can this really happen in the field?  I've never seen it, so if it happens
                                                         // you should verify that this really does the 'right' thing!
            }

            switch (assumedLength) {
                case 1024:
                    this.size_Mod = 0x80;
                    this.size_Exp = -1;
                    this.size_D = 0x80;
                    this.size_P = 0x40;
                    this.size_Q = 0x40;
                    this.size_DP = 0x40;
                    this.size_DQ = 0x40;
                    this.size_InvQ = 0x40;
                    break;
                case 2048:
                    this.size_Mod = 0x100;
                    this.size_Exp = -1;
                    this.size_D = 0x100;
                    this.size_P = 0x80;
                    this.size_Q = 0x80;
                    this.size_DP = 0x80;
                    this.size_DQ = 0x80;
                    this.size_InvQ = 0x80;
                    break;
                case 4096:
                    this.size_Mod = 0x200;
                    this.size_Exp = -1;
                    this.size_D = 0x200;
                    this.size_P = 0x100;
                    this.size_Q = 0x100;
                    this.size_DP = 0x100;
                    this.size_DQ = 0x100;
                    this.size_InvQ = 0x100;
                    break;
                default:
                    System.Diagnostics.Debug.Assert(false); // Unknown key size?
                    break;
            }
        }

        public int size_Mod = -1;
        public int size_Exp = -1;
        public int size_D = -1;
        public int size_P = -1;
        public int size_Q = -1;
        public int size_DP = -1;
        public int size_DQ = -1;
        public int size_InvQ = -1;
    }


}
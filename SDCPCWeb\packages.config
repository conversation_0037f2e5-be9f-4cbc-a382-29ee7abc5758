﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Antlr" version="*******" targetFramework="net462" />
  <package id="bootstrap" version="3.3.7" targetFramework="net462" />
  <package id="BouncyCastle" version="1.8.9" targetFramework="net462" />
  <package id="Crc32C.NET" version="*******" targetFramework="net462" />
  <package id="DnsClient" version="1.2.0" targetFramework="net462" />
  <package id="DotNetZip" version="1.16.0" targetFramework="net462" />
  <package id="EntityFramework" version="6.1.0" targetFramework="net462" />
  <package id="Hangfire.Console" version="1.4.2" targetFramework="net462" />
  <package id="Hangfire.Core" version="1.7.10" targetFramework="net462" />
  <package id="Hangfire.Mongo" version="0.6.7" targetFramework="net462" />
  <package id="jQuery" version="3.3.1" targetFramework="net462" />
  <package id="log4net" version="2.0.8" targetFramework="net462" />
  <package id="Microsoft.AspNet.Cors" version="5.0.0" targetFramework="net462" />
  <package id="Microsoft.AspNet.Identity.Core" version="2.2.3" targetFramework="net462" />
  <package id="Microsoft.AspNet.Identity.EntityFramework" version="2.2.3" targetFramework="net462" />
  <package id="Microsoft.AspNet.Identity.Owin" version="2.2.3" targetFramework="net462" />
  <package id="Microsoft.AspNet.Mvc" version="5.2.4" targetFramework="net462" />
  <package id="Microsoft.AspNet.Mvc.zh-Hans" version="5.2.4" targetFramework="net462" />
  <package id="Microsoft.AspNet.Razor" version="3.2.4" targetFramework="net462" />
  <package id="Microsoft.AspNet.Razor.zh-Hans" version="3.2.4" targetFramework="net462" />
  <package id="Microsoft.AspNet.Web.Optimization" version="1.1.3" targetFramework="net462" />
  <package id="Microsoft.AspNet.Web.Optimization.zh-Hans" version="1.1.3" targetFramework="net462" />
  <package id="Microsoft.AspNet.WebApi" version="5.2.4" targetFramework="net462" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.4" targetFramework="net462" />
  <package id="Microsoft.AspNet.WebApi.Client.zh-Hans" version="5.2.4" targetFramework="net462" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.4" targetFramework="net462" />
  <package id="Microsoft.AspNet.WebApi.Core.zh-Hans" version="5.2.4" targetFramework="net462" />
  <package id="Microsoft.AspNet.WebApi.HelpPage" version="5.2.4" targetFramework="net462" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.2.4" targetFramework="net462" />
  <package id="Microsoft.AspNet.WebApi.WebHost.zh-Hans" version="5.2.4" targetFramework="net462" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.4" targetFramework="net462" />
  <package id="Microsoft.AspNet.WebPages.zh-Hans" version="3.2.4" targetFramework="net462" />
  <package id="Microsoft.CodeDom.Providers.DotNetCompilerPlatform" version="2.0.0" targetFramework="net462" />
  <package id="Microsoft.Owin" version="4.0.1" targetFramework="net462" />
  <package id="Microsoft.Owin.Cors" version="4.0.0" targetFramework="net462" />
  <package id="Microsoft.Owin.Host.SystemWeb" version="4.0.1" targetFramework="net462" />
  <package id="Microsoft.Owin.Security" version="4.0.0" targetFramework="net462" />
  <package id="Microsoft.Owin.Security.Cookies" version="4.0.0" targetFramework="net462" />
  <package id="Microsoft.Owin.Security.OAuth" version="3.0.1" targetFramework="net462" />
  <package id="Microsoft.Web.Infrastructure" version="*******" targetFramework="net462" />
  <package id="Modernizr" version="2.8.3" targetFramework="net462" />
  <package id="MongoDB.Bson" version="2.10.2" targetFramework="net462" />
  <package id="MongoDB.Driver" version="2.10.2" targetFramework="net462" />
  <package id="MongoDB.Driver.Core" version="2.10.2" targetFramework="net462" />
  <package id="MongoDB.Libmongocrypt" version="1.0.0" targetFramework="net462" />
  <package id="Newtonsoft.Json" version="12.0.3" targetFramework="net462" />
  <package id="Oracle.ManagedDataAccess" version="19.7.0" targetFramework="net462" />
  <package id="Oracle.ManagedDataAccess.EntityFramework" version="19.7.0" targetFramework="net462" />
  <package id="Owin" version="1.0" targetFramework="net462" />
  <package id="SharpCompress" version="0.23.0" targetFramework="net462" />
  <package id="Snappy.NET" version="1.1.1.8" targetFramework="net462" />
  <package id="Swashbuckle" version="5.6.0" targetFramework="net462" />
  <package id="Swashbuckle.Core" version="5.6.0" targetFramework="net462" />
  <package id="System.Buffers" version="4.4.0" targetFramework="net462" />
  <package id="System.Runtime.InteropServices.RuntimeInformation" version="4.3.0" targetFramework="net462" />
  <package id="WebActivatorEx" version="2.0" targetFramework="net462" />
  <package id="WebGrease" version="1.6.0" targetFramework="net462" />
</packages>
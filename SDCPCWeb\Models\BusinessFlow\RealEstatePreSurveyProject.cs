﻿using Newtonsoft.Json;
using SDCPCWeb.Models.Attachment;
using SDCPCWeb.Models.BusinessContent.YanXian;
using SDCPCWeb.Models.System;
using SDCPCWeb.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace SDCPCWeb.Models.BusinessFlow {
    public class RealEstatePreSurveyProject : BaseProject {
        //private static OracleDataService service = new OracleDataService();
        /// <summary>
        /// 基本信息
        /// </summary>
        public override BusinessBaseInfoModel BaseInfo { get; set; }
        /// <summary>
        /// 内容信息
        /// </summary>
        public PreSurvey_ContentInfo ContentInfo { get; set; }
        /// <summary>
        /// 附件信息
        /// </summary>
        public override List<AttachmentInfoModel> Attachments { get; set; }
        /// <summary>
        /// 当前经过的流转环节信息
        /// </summary>
        public override List<BusinessLinkInfoModel> ActionsInfos { get; set; }
        /// <summary>
        /// 最新的环节，当前环节
        /// </summary>
        public override BusinessLinkInfoModel CurrentAction { get; set; }
        /// <summary>
        /// 工作流定义信息
        /// </summary>
        public override BusinessFlowBase FlowInfo { get; } = BusinessFlowConfig.RealEstatePreSurveyFlow;
        /// <summary>
        /// 根据ID获取信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static RealEstatePreSurveyProject GetByBusinessID(string id) {
            RealEstatePreSurveyProject project = new RealEstatePreSurveyProject();
            using (OracleDataService service = new OracleDataService()) {
                project.BaseInfo = service.GetById<BusinessBaseInfo>(id);
                project.ContentInfo = service.GetById<PreSurvey_ContentInfo>(id);
                project.Attachments = service.GetList<AttachmentInfo>("BusinessID='" + id + "' and StateCode=0")?.Select(a => a.ToModel()).ToList();
                project.ActionsInfos = service.GetList<BusinessLinkInfo>("BusinessID='" + id + "'")?.Select(a => a.ToModel()).OrderBy(a => a.StartTime).ToList();
                project.CurrentAction = project.ActionsInfos?.OrderByDescending(a => a.StartTime).FirstOrDefault();
                return project;
            }
        }
        /// <summary>
        /// 保存信息
        /// </summary>
        /// <param name="project"></param>
        /// <returns></returns>
        public static string Save(RealEstatePreSurveyProject project) {
            string result = "";
            using (OracleDataService service = new OracleDataService()) {
                try {
                    var record = service.GetById<BusinessBaseInfo>(project.BaseInfo.ID);
                    //更新业务基本信息表
                    record.BusinessName = project.BaseInfo.BusinessName;
                    record.ExtendInfo = project.BaseInfo.ExtendInfo;
                    service.UpdateOne(record);
                    //判断业务内容信息是否存在
                    PreSurvey_ContentInfo content = service.GetById<PreSurvey_ContentInfo>(project.BaseInfo.ID);
                    if (content == null) {
                        content = project.ContentInfo;
                        content.ID = content.ID ?? Guid.NewGuid().ToString("N");
                        service.InsertOne(content);
                    }
                    else {
                        content.GroundCode = project.ContentInfo.GroundCode;
                        content.ProjectPlanPermission = project.ContentInfo.ProjectPlanPermission;
                        service.UpdateOne(content);
                    }
                }
                catch (Exception e) {
                    result = e.Message;
                }
            }
            return result;
        }
    }
    /// <summary>
    /// 不动产预测绘流程定义
    /// </summary>
    public sealed class RealEstatePreSurveyFlow : BusinessFlowBase {
        public RealEstatePreSurveyFlow() {
            //实例化时，在构造函数定义工作流程
            FlowName = "不动产预测绘";
            Catalog = "规划验线";
            FlowActionInfo = new BusinessFlowActionInfo() {
                Actions = new[] {
                    new BusinessFlowAction() {
                        ID = 0,
                        Index = 0,
                        ActionRoles = new[] {UserRole.BusinessAdmin, UserRole.BusinessNormal},
                        Name = "填写申请信息"
                    },
                    new BusinessFlowAction() {
                        ID = 1,
                        Index = 1,
                        ActionRoles = new[] {UserRole.Myself},
                        Name = "委托测绘单位"
                    },
                    new BusinessFlowAction() {
                        ID = 2,
                        Index = 2,
                        ActionRoles = new[] {UserRole.SurveyMaster},
                        Name = "汇交测绘成果"
                    },
                    new BusinessFlowAction() {
                        ID = 4,
                        Index = 4,
                        ActionRoles = new[] {UserRole.ProjectCreator},
                        Name = "成果验收"
                    }
                },
                StartActionId = 0,
                EndActionIds = new[] { 4 },
                Routes = new[] {
                    new BusinessFlowRoute() {FromActionId = 0,ToActionId = 1},
                    new BusinessFlowRoute() {FromActionId = 1,ToActionId = 2},
                    new BusinessFlowRoute() {FromActionId = 2,ToActionId = 4},
                    //new BusinessFlowRoute() {FromActionId = 3,ToActionId = 4}
                },
                BackRoutes = new[] {
                    new BusinessFlowBackRoute() {FromActionId = 4,ToActionId = 2,NeedResponseReason = true,IsBackToMyself = false },
                    //new BusinessFlowBackRoute() {FromActionId = 3,ToActionId = 2,NeedResponseReason = true,IsBackToMyself = false },
                    new BusinessFlowBackRoute() {FromActionId = 2,ToActionId = 1,NeedResponseReason = true,IsBackToMyself = false },
                    new BusinessFlowBackRoute() {FromActionId = 1,ToActionId = 0,NeedResponseReason = false,IsBackToMyself = true }
                },
                FinishWhenIntoEndAction = false
            };

            //增加路由事件
            var backRouteF2T1 = FlowActionInfo.BackRoutes.FirstOrDefault(r => r.FromActionId == 2 && r.ToActionId == 1);
            if (backRouteF2T1 != null) {
                backRouteF2T1.RoutePassed += BackRouteF2T1_RoutePassed; ;
            }
            var backRouteF4T2 = FlowActionInfo.BackRoutes.FirstOrDefault(r => r.FromActionId == 4 && r.ToActionId == 2);
            if (backRouteF4T2 != null) {
                backRouteF4T2.RoutePassed += BackRouteF4T2_RoutePassed; ;
            }
        }

        /// <summary>
        /// 从第3步返回第2步触发的事件
        /// </summary>
        /// <param name="baseInfo"></param>
        /// <param name="linkInfo"></param>
        private void BackRouteF2T1_RoutePassed(BusinessBaseInfoModel baseInfo, BusinessLinkInfoModel linkInfo) {
            //退回委托方，要移除已上传的测绘成果
            var businessId = baseInfo.ID;
            var oracle = new OracleDataService();
            //移除项目附件
            var attachments = oracle.GetList<AttachmentInfo>($"BusinessID='{businessId}' AND AttachmentType = '项目成果附件'");
            if (attachments.Any()) {
                foreach (var item in attachments) {
                    if (item.StateCode != 1) {
                        item.StateCode = 1;
                        oracle.UpdateOne(item);
                    }
                }
            }
            //移除项目的成果信息
            var contentInfo = oracle.GetById<PreSurvey_ContentInfo>(businessId);
            contentInfo.ProjectResultInfo = null;
            contentInfo.BuildingTableInfo = null;
            contentInfo.DataCheckID = null;
            contentInfo.DataCheckState = 0;
            contentInfo.SurveyMasterName = null;
            contentInfo.SurveyMasterNo = null;
            contentInfo.SurveyMasterSureTime = null;

            oracle.UpdateOne(contentInfo);

            var baseBusinessInfo = BusinessBaseInfo.FromModel(baseInfo);
            baseBusinessInfo.SurveyCompanyNo = null;
            baseBusinessInfo.SurveyCompanyName = null;
            oracle.UpdateOne(baseBusinessInfo);
        }


        /// <summary>
        /// 从第4步返回第2步触发的事件
        /// </summary>
        /// <param name="baseInfo"></param>
        /// <param name="linkInfo"></param>
        private void BackRouteF4T2_RoutePassed(BusinessBaseInfoModel baseInfo, BusinessLinkInfoModel linkInfo) {
            //退回测绘单位，要移除注册测绘师确认的标记
            var businessId = baseInfo.ID;
            var oracle = new OracleDataService();

            //移除项目的成果信息
            var contentInfo = oracle.GetById<PreSurvey_ContentInfo>(businessId);
            contentInfo.SurveyMasterName = null;
            contentInfo.SurveyMasterNo = null;
            contentInfo.SurveyMasterSureTime = null;

            oracle.UpdateOne(contentInfo);
        }

        #region Overrides of BusinessFlowBase
        /// <summary>
        /// 流程环节定义信息JSON
        /// </summary>
        /// <returns></returns>
        public override string FlowActionJSON {
            get {
                return JsonConvert.SerializeObject(FlowActionInfo.Actions.Select(a => new {
                    a.ID,
                    a.Name
                }));
            }
        }

        /// <summary>
        /// 是否包含测绘作业
        /// </summary>
        public override bool HasSurvey { get; } = true;

        /// <summary>
        /// 流程是否启用
        /// </summary>
        public override bool Enable => BusinessFlowConfig.EnableFlows.Contains(GetType().Name);

        /// <summary>
        /// 该流程是否需要协议备案
        /// </summary>
        public override bool NeedBDCProtocol { get; } = true;

        /// <summary>
        /// 该流程是否在办理业务中可见
        /// </summary>
        public override bool Visible => BusinessFlowConfig.VisibleFlows.Contains(GetType().Name);

        #endregion
    }
}
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using SDCPCWeb.Models.System;

namespace SDCPCWeb.Models.BusinessFlow {
    /// <summary>
    /// 基础业务流程定义基类型
    /// </summary>
    public abstract class BusinessFlowBase {
        /// <summary>
        /// 流程名称
        /// </summary>
        public string FlowName { get; set; }
        /// <summary>
        /// 流程大类
        /// </summary>
        public string Catalog { get; set; }
        /// <summary>
        /// 流程环节信息
        /// </summary>
        public BusinessFlowActionInfo FlowActionInfo { get; set; }
        /// <summary>
        /// 流程环节信息JSON
        /// </summary>
        /// <returns></returns>
        public abstract string FlowActionJSON { get; }

        /// <summary>
        /// 是否包含测绘作业
        /// </summary>
        public abstract bool HasSurvey { get; }

        /// <summary>
        /// 流程是否启用
        /// </summary>
        public abstract bool Enable { get; }

        /// <summary>
        /// 该流程是否需要协议备案
        /// </summary>
        public abstract bool NeedBDCProtocol { get; }

        /// <summary>
        /// 该流程是否在办理业务中可见
        /// </summary>
        public abstract bool Visible { get; }
    }

    /// <summary>
    /// 流程环节信息
    /// </summary>
    public class BusinessFlowActionInfo {
        /// <summary>
        /// 环节集合
        /// </summary>
        public IEnumerable<BusinessFlowAction> Actions { get; set; }

        /// <summary>
        /// 开始环节Id
        /// </summary>
        public int StartActionId { get; set; }

        /// <summary>
        /// 结束环节，多路由时可以拥有多个结束环节
        /// </summary>
        public IEnumerable<int> EndActionIds { get; set; }

        /// <summary>
        /// 环节流转路由集合
        /// </summary>
        public IEnumerable<BusinessFlowRoute> Routes { get; set; }

        /// <summary>
        /// 回退环节路由集合
        /// </summary>
        public IEnumerable<BusinessFlowBackRoute> BackRoutes { get; set; }

        /// <summary>
        /// 获取或者设置一个值，表示提交到流程最后一步时是否自动完成
        /// </summary>
        public bool FinishWhenIntoEndAction { get; set; }

        /// <summary>
        /// 提交验证
        /// </summary>
        public ValidateFlowActionPostHandler ValidateActionPost;

        /// <summary>
        /// 流程提交事件——提交前触发
        /// </summary>
        public event FlowActionPostHandler BeforeActionPost;

        /// <summary>
        /// 流程提交事件——提交后触发
        /// </summary>
        public event FlowActionPostHandler ActionPosted;

        internal virtual void OnBeforeActionPost(BusinessBaseInfoModel baseinfo, BusinessLinkInfoModel linkinfo) {
            BeforeActionPost?.Invoke(baseinfo, linkinfo);
        }

        internal virtual void OnActionPosted(BusinessBaseInfoModel baseinfo, BusinessLinkInfoModel linkinfo) {
            ActionPosted?.Invoke(baseinfo, linkinfo);
        }
    }

    /// <summary>
    /// 流程环节
    /// </summary>
    public class BusinessFlowAction {
        /// <summary>
        /// 环节标识，环节集合内唯一即可
        /// </summary>
        public int ID { get; set; }

        /// <summary>
        /// 环节索引，用于列表排序
        /// </summary>
        public int Index { get; set; }

        /// <summary>
        /// 环节名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 环节执行者角色，此环节由什么角色来执行
        /// </summary>
        public IEnumerable<UserRole> ActionRoles { get; set; }
    }

    /// <summary>
    /// 路由，用于连接两个环节
    /// </summary>
    public class BusinessFlowRoute {
        /// <summary>
        /// 路由标签
        /// </summary>
        public string RouteLabel { get; set; }

        /// <summary>
        /// 路由开始环节Id
        /// </summary>

        public int FromActionId { get; set; }

        /// <summary>
        /// 路由结束环节Id
        /// </summary>

        public int ToActionId { get; set; }

        /// <summary>
        /// 路由事件--流转经过此路由时触发
        /// </summary>
        public event FlowActionPostHandler RoutePassed;

        internal virtual void OnRoutePassed(BusinessBaseInfoModel baseinfo, BusinessLinkInfoModel linkinfo) {
            RoutePassed?.Invoke(baseinfo, linkinfo);
        }
    }

    /// <summary>
    /// 退回设定专用路由
    /// </summary>
    public class BusinessFlowBackRoute : BusinessFlowRoute {
        /// <summary>
        /// 退回时是否需要填写退回原因
        /// </summary>
        public bool NeedResponseReason { get; set; }

        /// <summary>
        /// 是否直接退回给自己
        /// </summary>
        public bool IsBackToMyself { get; set; }
    }
}
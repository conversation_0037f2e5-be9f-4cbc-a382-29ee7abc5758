﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Hangfire.Common;
using Hangfire.States;
using Hangfire.Storage;

namespace SDCPCWeb.Jobs {
    public class NoRecordJobAttribute : JobFilterAttribute, IApplyStateFilter {
        /// <summary>
        /// Called after the specified state was applied
        /// to the job within the given transaction.
        /// </summary>
        public void OnStateApplied(ApplyStateContext context, IWriteOnlyTransaction transaction) {
            context.JobExpirationTimeout = TimeSpan.Zero;
        }

        /// <summary>
        /// Called when the state with specified state was
        /// unapplied from the job within the given transaction.
        /// </summary>
        public void OnStateUnapplied(ApplyStateContext context, IWriteOnlyTransaction transaction) {
            context.JobExpirationTimeout = TimeSpan.Zero;
        }
    }
}
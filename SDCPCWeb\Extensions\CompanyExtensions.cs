﻿#region 文件信息描述
// /* ===============================================
// * 功能描述：
// * 创 建 人：吕俊宏
// * 创建日期：2021年01月20日 21:24
// * 项目名称：SDCPCWeb
// * 文件名称：CompanyExtensions.cs
// * 创建用户名：Lvjunhong
// * ================================================*/
#endregion

using System;
using SDCPCWeb.Models.CompanyRegister;

namespace SDCPCWeb.Extensions {
    /// <summary>
    /// 单位管理扩展方法
    /// </summary>
    public static class CompanyExtensions {

        /// <summary>
        /// 转为中文名称
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public static string ToName(this CompanyBaseType type) {
            switch (type) {
                case CompanyBaseType.SurveyCompany:
                    return "测绘单位";
                case CompanyBaseType.ConstructionCompany:
                    return "建设单位";
                default:
                    throw new ArgumentOutOfRangeException(nameof(type), type, null);
            }
        }

        /// <summary>
        /// 转为中文名称
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public static string ToName(this CompanyType type) {
            switch (type) {
                case CompanyType.SurveyCompany:
                    return "测绘单位";
                case CompanyType.RealEstateCompany:
                    return "开发商";
                case CompanyType.Institute:
                    return "机关单位";
                case CompanyType.NormalCompany:
                    return "一般企业";
                default:
                    throw new ArgumentOutOfRangeException(nameof(type), type, null);
            }
        }

        /// <summary>
        /// 从中文转换为枚举
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public static CompanyBaseType CompanyBaseTypeFromName(this string name) {
            switch (name) {
                case "测绘单位": return CompanyBaseType.SurveyCompany;
                case "建设单位":
                default:
                    return CompanyBaseType.ConstructionCompany;
            }
        }

        /// <summary>
        /// 从中文转换为枚举
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public static CompanyType CompanyTypeFromName(this string name) {
            switch (name) {
                case "测绘单位": return CompanyType.SurveyCompany;
                case "开发商": return CompanyType.RealEstateCompany;
                case "机关单位": return CompanyType.Institute;
                case "一般企业": return CompanyType.NormalCompany;
                default:
                    return (CompanyType) (-1);
            }
        }
    }
}
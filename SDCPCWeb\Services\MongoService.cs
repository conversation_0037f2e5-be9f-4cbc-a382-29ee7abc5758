﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web;
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Driver;

namespace SDCPCWeb.Services {
    public class MongoService<T> where T : class, IMongoEntity {
        public IMongoCollection<T> Collection { get; protected set; }

        public MongoService(string configurationName = "HangfireMongoDB") {
            string mongoConnString = null;
            mongoConnString = ConfigurationManager.ConnectionStrings[configurationName].ConnectionString;
            var mongoUrl = new MongoUrl(mongoConnString);
            if (string.IsNullOrWhiteSpace(mongoUrl.DatabaseName)) {
                throw new Exception($"数据库连接配置{configurationName}指定的连接字符串“{mongoConnString}”必须指定数据库名");
            }
            var client = new MongoClient(mongoUrl);
            Collection = client.GetDatabase(mongoUrl.DatabaseName).GetCollection<T>(typeof(T).Name);
        }


        public T GetById(string Id) {
            var result = Collection.Find(model => model.Id == Id);
            return result.Count() == 0 ? default(T) : result.FirstOrDefault();
        }

        public IEnumerable<T> Get(System.Linq.Expressions.Expression<Func<T, bool>> filter) {
            return Collection.Find(filter).ToEnumerable();
        }

        public void Insert(T model) {
            Collection.InsertOne(model);
        }

        public void Update(T entity) {
            Collection.FindOneAndReplace(model => model.Id == entity.Id, entity);
        }

        public void DeleteById(string Id) {
            Collection.FindOneAndDelete(model => model.Id == Id);
        }

        public void Delete(T entity) {
            Collection.FindOneAndDelete(model => model.Id == entity.Id);
        }

        public void Save(T entity) {
            var old = Collection.FindOneAndReplace(model => model.Id == entity.Id, entity);
            if (old == default(T)) {
                Collection.InsertOne(entity);
            }
        }

    }

    public interface IMongoEntity {
        [BsonId] string Id { get; set; }
    }
}
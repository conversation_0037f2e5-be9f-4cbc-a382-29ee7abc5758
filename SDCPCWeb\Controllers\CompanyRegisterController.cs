﻿using Microsoft.AspNet.Identity;
using Newtonsoft.Json;
using SDCPCWeb.Models;
using SDCPCWeb.Models.CompanyRegister;
using SDCPCWeb.Models.Result;
using SDCPCWeb.Services;
using System;
using System.Collections.Generic;
using System.Data.Entity.Infrastructure;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Web.Configuration;
using System.Web.Http;
using Newtonsoft.Json.Linq;
using SDCPCWeb.Models.System;
using System.Web;
using Oracle.ManagedDataAccess.Client;
using SDCPCWeb.Extensions;
using SDCPCWeb.Models.BusinessFlow;

namespace SDCPCWeb.Controllers {
    /// <summary>
    /// 单位注册和管理控制器组
    /// </summary>
    [RoutePrefix("sdcapi/CompanyRegister"), BDCAuthorize]
    public class CompanyRegisterController : ApiController {
        private readonly OracleDataService service = new OracleDataService();
        private UserInfo UserInfo => (UserInfo)Request.Properties["SDC-UserInfo"];
        /// <summary>
        /// 获取正在申请的单位注册信息
        /// </summary>
        /// <returns></returns>
        [HttpGet, Route("GetRequesting")]
        public ApiResult GetRequesting() {
            var userId = UserInfo.UserId;
            var where = $"CreateUserID='{userId}' AND StateCode in(0,1,2,4,6)";
            var list = service.GetList<CompanyRegisterRequest>(where);
            if (list.Any()) {
                list = list.OrderBy(c => c.CreateTime).ToList();
            }
            return new ApiResult { StateCode = 1, Data = list.FirstOrDefault()?.ToModel() };
        }
        /// <summary>
        /// 创建新的单位注册申请
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost, Route("CreateNewCompanyRequest")]
        public ApiResult CreateNewCompanyRequest(CompanyRegisterRequest request) {
            if (request == null) {
                return new ApiResult { StateCode = 0, Message = "请输入正确的信息" };
            }
            //传入参数判断
            //必填值不能为空
            if (string.IsNullOrWhiteSpace(request.CompanyName)) {
                return new ApiResult { StateCode = 0, Message = "单位名称不能为空" };
            }
            if (string.IsNullOrWhiteSpace(request.CompanyNo)) {
                return new ApiResult { StateCode = 0, Message = "单位统一社会信用代码不能为空" };
            }
            if (request.CompanyType.CompanyTypeFromName() != CompanyType.SurveyCompany) {
                return new ApiResult { StateCode = 0, Message = "单位类型只能为【测绘单位】" };
            }

            var clist = service.GetList<CompanyRegisterRequest>(
                "CompanyName=:cname and CompanyType=:ctype"
                , new OracleParameter(":cname", OracleDbType.Varchar2) { Value = request.CompanyName }
                , new OracleParameter(":ctype", OracleDbType.Varchar2) { Value = request.CompanyType }
            );
            if (clist.Count(c => c.StateCode == 3) > 0) {
                return new ApiResult { StateCode = 0, Message = "此单位已经注册，请勿重复注册" };
            }
            if (clist.Count(c => c.StateCode != 3 && c.StateCode != 5) > 0) {
                return new ApiResult { StateCode = 0, Message = "此单位正在申请注册，请勿重复申请注册" };
            }
            //赋初始值
            request.ID = Guid.NewGuid().ToString("N");
            request.CreateUserID = UserInfo.UserId;
            request.CreateUserName = UserInfo.PersonName;
            request.CreateUserPersonNo = UserInfo.PersonNo;
            request.CreateTime = DateTime.Now;
            request.PostTime = null;
            request.AcceptAuditTime = null;
            request.CloseTime = null;
            request.SuccessTime = null;
            request.ResponseMessage = null;
            request.ResponsePerson = null;
            request.StateCode = 0;

            try {
                service.InsertOne(request);
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 0, Message = "创建失败，请稍后再试，错误信息：" + e.Message + "" };
            }

            return new ApiResult { StateCode = 1, Data = request.ToModel() };
        }
        /// <summary>
        /// 保存单位申请信息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost, Route("SaveCompanyRequest")]
        public ApiResult SaveCompanyRequest(CompanyRegisterRequest request) {
            if (request == null) {
                return new ApiResult { StateCode = 0, Message = "请输入正确的信息" };
            }
            if (string.IsNullOrWhiteSpace(request.ID)) {
                return new ApiResult { StateCode = 0, Message = "ID不能为空，请传入正确的参数" };
            }
            //ID必须存在对应的记录
            var record = service.GetById<CompanyRegisterRequest>(request.ID);
            if (record == null) {
                return new ApiResult { StateCode = 0, Message = "无法找到单位注册申请信息" };
            }
            //记录的状态值必须是能够保存的状态
            if (record.StateCode != 0 && record.StateCode != 4) {
                return new ApiResult { StateCode = 0, Message = "当前状态不可修改" };
            }
            //判断单位名称或社会统一信用代码不可重复
            List<CompanyRegisterRequest> List = service.GetList<CompanyRegisterRequest>("(CompanyNo='" + request.CompanyNo + "' or CompanyName='" + request.CompanyName + "') and CompanyType='" + request.CompanyType + "' and ID !='" + request.ID + "' and StateCode!=5");
            if (List.Count > 0) {
                return new ApiResult { StateCode = 0, Message = "单位已经注册过" };
            }
            //进入保存逻辑
            //部分信息不能修改
            request.CreateUserID = record.CreateUserID;
            request.CreateUserName = record.CreateUserName;
            request.CreateUserPersonNo = record.CreateUserPersonNo;
            request.CreateTime = record.CreateTime;
            request.PostTime = record.PostTime;
            request.AcceptAuditTime = record.AcceptAuditTime;
            request.CloseTime = record.CloseTime;
            request.SuccessTime = record.SuccessTime;
            request.ResponseMessage = record.ResponseMessage;
            request.ResponsePerson = record.ResponsePerson;
            request.CompanyType = record.CompanyType;
            request.StateCode = record.StateCode;
            try {
                service.UpdateOne(request);
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 0, Message = "保存失败，错误信息：" + e.Message + "" };
            }

            return new ApiResult { StateCode = 1, Message = "保存成功" };
        }
        /// <summary>
        /// 逻辑删除单位申请信息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost, Route("DeleteCompanyRequest")]
        public ApiResult DeleteCompanyRequest(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult { StateCode = 0, Message = "ID不能为空，请传入正确的参数" };
            }
            //ID必须存在对应的记录
            var record = service.GetById<CompanyRegisterRequest>(id);
            if (record == null) {
                return new ApiResult { StateCode = 0, Message = "无法找到单位注册申请信息" };
            }
            //逻辑删除
            record.StateCode = 5;
            try {
                service.UpdateOne(record);
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 0, Message = "错误信息：" + e.Message + "" };
            }

            return new ApiResult { StateCode = 1, Message = "删除成功" };
        }
        /// <summary>
        /// 提交单位信息审核
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost, Route("SubmitCompanyAuditRequest")]
        public ApiResult SubmitCompanyAuditRequest(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult { StateCode = 0, Message = "ID不能为空，请传入正确的参数" };
            }
            //ID必须存在对应的记录
            var record = service.GetById<CompanyRegisterRequest>(id);
            if (record == null) {
                return new ApiResult { StateCode = 0, Message = "无法找到单位注册申请信息" };
            }
            //只有测绘单位可以提交注册审核
            if (record.CompanyType.CompanyBaseTypeFromName() != CompanyBaseType.SurveyCompany) {
                return new ApiResult { StateCode = 0, Message = "只有测绘单位需要注册，请检查" };
            }
            //记录需要更新的状态必须为已提交/待审核
            if (record.StateCode != 0 && record.StateCode != 4) {
                return new ApiResult { StateCode = 0, Message = "只有在未提交或者退回修改的状态下才能提交审核" };
            }
            if (string.IsNullOrEmpty(record.DetailInfo)) {
                return new ApiResult { StateCode = 0, Message = "单位信息不完整" };
            }
            try {
                CompanyRegisterDetailInfo detail = JsonConvert.DeserializeObject<CompanyRegisterDetailInfo>(record.DetailInfo);
                if (detail.CompanyEmployees == null) {
                    return new ApiResult { StateCode = 0, Message = "人员信息未录入当前申请人" };
                }
                List<CompanyEmployees> list = new List<CompanyEmployees>(detail.CompanyEmployees);
                int num = 0;
                list.ForEach(data => {
                    if (data.PersonNumber == record.CreateUserPersonNo && data.PersonRole == "单位管理员") {
                        num += 1;
                    }
                });
                if (num == 0) {
                    return new ApiResult { StateCode = 0, Message = "人员信息中需要录入当前申请人，且角色为‘单位管理员’" };
                }
                //进入提交逻辑
                //更改记录状态及审核信息
                record.PostTime = DateTime.Now;
                record.AcceptAuditTime = null;
                record.CloseTime = null;
                record.SuccessTime = null;
                record.StateCode = 1;
                service.UpdateOne(record);
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 0, Message = "提交失败，错误信息：" + e.Message + "" };
            }

            return new ApiResult { StateCode = 1, Message = "提交成功" };
        }
        /// <summary>
        /// 签收单位信息审核
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost, Route("AcceptCompanyAuditRequest"),Obsolete("不再使用", true)]
        public ApiResult AcceptCompanyAuditRequest(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult { StateCode = 0, Message = "ID不能为空，请传入正确的参数" };
            }
            //ID必须存在对应的记录
            var record = service.GetById<CompanyRegisterRequest>(id);
            if (record == null) {
                return new ApiResult { StateCode = 0, Message = "无法找到单位注册申请信息" };
            }
            if (record.CreateUserID == UserInfo.UserId) {
                return new ApiResult { StateCode = 0, Message = "无法签收您自己创建的单位注册申请" };
            }
            //更改审核信息
            record.AcceptAuditTime = DateTime.Now;
            record.ResponsePerson = UserInfo.PersonName;
            record.StateCode = 2;
            try {
                service.UpdateOne(record);
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 0, Message = "错误信息：" + e.Message + "" };
            }
            return new ApiResult { StateCode = 1, Message = "签收成功" };
        }
        /// <summary>
        /// 退回单位信息审核
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost, Route("BackCompanyAuditRequest"), Obsolete("不再使用", true)]
        public ApiResult BackCompanyAuditRequest(CompanyRegisterRequest request) {
            if (request == null) {
                return new ApiResult { StateCode = 0, Message = "请输入正确的信息" };
            }
            if (string.IsNullOrWhiteSpace(request.ID)) {
                return new ApiResult { StateCode = 0, Message = "ID不能为空，请传入正确的参数" };
            }
            //ID必须存在对应的记录
            var record = service.GetById<CompanyRegisterRequest>(request.ID);
            if (record == null) {
                return new ApiResult { StateCode = 0, Message = "无法找到单位注册申请信息" };
            }
            //更改审核信息
            record.ResponsePerson = UserInfo.PersonName;
            record.ResponseMessage = request.ResponseMessage;
            record.StateCode = 4;
            try {
                service.UpdateOne(record);
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 0, Message = "退回失败，错误信息：" + e.Message + "" };
            }
            return new ApiResult { StateCode = 1, Message = "退回成功" };
        }
        /// <summary>
        /// 关闭单位信息审核
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost, Route("CloseCompanyAuditRequest")]
        public ApiResult CloseCompanyAuditRequest(CompanyRegisterRequest request) {
            if (request == null) {
                return new ApiResult { StateCode = 0, Message = "请输入正确的信息" };
            }
            if (string.IsNullOrWhiteSpace(request.ID)) {
                return new ApiResult { StateCode = 0, Message = "ID不能为空，请传入正确的参数" };
            }
            //ID必须存在对应的记录
            var record = service.GetById<CompanyRegisterRequest>(request.ID);
            if (record == null) {
                return new ApiResult { StateCode = 0, Message = "无法找到单位注册申请信息" };
            }
            //更改审核信息
            record.CloseTime = DateTime.Now;
            record.ResponsePerson = UserInfo.PersonName;
            record.ResponseMessage = request.ResponseMessage;
            //申请人关闭
            if (record.CreateUserID == UserInfo.UserId) {
                record.ResponsePerson = null;
                record.ResponseMessage = "申请人自行撤销。";
            }
            record.StateCode = 5;
            try {
                service.UpdateOne(record);
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 0, Message = "关闭失败，错误信息：" + e.Message + "" };
            }
            return new ApiResult { StateCode = 1, Message = "关闭成功" };
        }
        /// <summary>
        /// 判断单位是否审核通过
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost, Route("JudgeExamineRequest")]
        public ApiResult JudgeExamineRequest(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult { StateCode = 0, Message = "ID不能为空，请传入正确的参数" };
            }
            //ID必须存在对应的记录
            var record = service.GetById<CompanyRegisterRequest>(id);
            if (record == null) {
                return new ApiResult { StateCode = 0, Message = "无法找到单位信息" };
            }
            //判断是否审核通过
            if (record.StateCode == 3) {
                return new ApiResult { StateCode = 1, Message = "审核通过" };
            }
            else {
                return new ApiResult { StateCode = 0, Message = "审核未通过" };
            }
        }
        /// <summary>
        /// 获取单位信息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost, Route("GetCompanyDetailsInfo")]
        public ApiResult GetCompanyDetailsInfo(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult { StateCode = 0, Message = "ID不能为空，请传入正确的参数" };
            }
            try {
                //获取单位基本信息
                var company = service.GetById<CompanyBaseInfo>(id);
                if (company == null) {
                    return new ApiResult { StateCode = 0, Message = "系统中找不到该单位信息" };
                };
                if (company.CompanyType.CompanyBaseTypeFromName() == CompanyBaseType.ConstructionCompany) {
                    var employees = service.GetList<DeveloperEmployee>("RelationRequestId=:id", new OracleParameter(":id", OracleDbType.Varchar2) { Value = company.ID });

                    return new ApiResult {
                        StateCode = 1,
                        Data = new {
                            DetailInfo = new {
                                CompanyBaseInfo = company,
                                CompanyQualification = default(object),
                                CompanyEmployees = employees,
                                CompanyEquipments = default(object)
                            }
                        }
                    };
                }
                //测绘单位
                //获取单位资质信息
                var qualification = service.GetById<CompanyQualification>(company.ID);
                //获取单位人员信息
                var companyEmployeeses = service.GetList<CompanyEmployees>("RelationRequestId=:id", new OracleParameter(":id", OracleDbType.Varchar2) { Value = company.ID });
                dynamic resultInfo = new {
                    DetailInfo = new {
                        CompanyBaseInfo = company,
                        CompanyQualification = CompanyQualificationShowModel.FromModel(qualification.ToModel()),
                        CompanyEmployees = companyEmployeeses,
                        CompanyEquipments = default(object)
                    }
                };
                return new ApiResult { StateCode = 1, Data = resultInfo };
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 0, Message = "单位信息获取失败，错误信息：" + e.Message + "" };
            }
        }
        /// <summary>
        /// 根据用户信息获取单位信息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost, Route("GetCompanyInfoByUserInfo")]
        public ApiResult GetCompanyInfoByUserInfo() {
            try {
                //判断是否是测绘单位
                var where = $"PersonNumber='{UserInfo.PersonNo}'";
                var suerveyCompanyEmployee = service.GetList<CompanyEmployees>(where).FirstOrDefault();
                if (suerveyCompanyEmployee == null) {
                    //判断是否已绑定为开发企业
                    var developerCompanyEmployees = service.GetList<DeveloperEmployee>(where);
                    if (!developerCompanyEmployees.Any()) {
                        //判断是否正在注册测绘单位
                        var registingCompany = service.GetList<CompanyRegisterRequest>($"CreateUserID='{UserInfo.UserId}' AND StateCode in(0,1,2,4,6)").FirstOrDefault();
                        if (registingCompany == null) {
                            //用户未绑定任何单位
                            return new ApiResult { StateCode = 0, Message = "未绑定单位信息或单位信息已失效" };
                        }
                        //用户正在注册测绘单位
                        dynamic cBriefInfo = new {
                            UserInfo.UserId,
                            registingCompany.CompanyType,
                            CompanyList = new[] {
                                    new {
                                        CID = registingCompany.ID,
                                        CName = registingCompany.CompanyName,
                                        CType = registingCompany.CompanyType,
                                        State = registingCompany.StateCode,
                                        CRole = "",
                                        CNo = registingCompany.CompanyNo
                                    }
                                }
                        };
                        return new ApiResult { StateCode = 1, Data = cBriefInfo };
                    }
                    else {
                        var companyList = new List<object>();
                        //开发企业的人员
                        foreach (var developerEmployee in developerCompanyEmployees) {
                            var company = service.GetById<CompanyBaseInfo>(developerEmployee.RelationRequestId);
                            if (company != null) {
                                companyList.Add(new {
                                    CID = company.ID,
                                    CName = company.CompanyName,
                                    CType = company.CompanyType,
                                    State = 3,
                                    CRole = developerEmployee.PersonRole,
                                    CNo = company.CreditCode
                                });
                            }
                        }
                        dynamic cBriefInfo = new {
                            UserInfo.UserId,
                            CompanyType = CompanyBaseType.ConstructionCompany.ToName(),
                            CompanyList = companyList
                        };
                        return new ApiResult { StateCode = 1, Data = cBriefInfo };
                    }
                }
                else {
                    //绑定了已注册的测绘单位
                    var cRecord = service.GetById<CompanyBaseInfo>(suerveyCompanyEmployee.RelationRequestId);
                    if (cRecord != null) {
                        dynamic cBriefInfo = new {
                            UserInfo.UserId,
                            cRecord.CompanyType,
                            CompanyList = new[] {
                                new {
                                    CID = cRecord.ID,
                                    CName = cRecord.CompanyName,
                                    CType = cRecord.CompanyType,
                                    State = 3,
                                    CRole = suerveyCompanyEmployee.PersonRole,
                                    CNo = cRecord.CreditCode
                                }
                            }
                        };
                        return new ApiResult { StateCode = 1, Data = cBriefInfo };
                    }
                    return new ApiResult { StateCode = 0, Message = "未绑定单位信息或单位信息已失效" };
                }
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 0, Message = "单位信息获取失败，错误信息：" + e.Message + "" };
            }
        }
        /// <summary>
        /// 获取单位信息列表
        /// </summary>
        /// <param name="statecode"></param>
        /// <param name="companyname"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpGet, Route("GetCompanyListInfo")]
        public ApiResult GetCompanyListInfo(int StateCode, string CompanyName, int PageIndex, int PageSize, string Sort) {
            string where = "StateCode!=0";
            if (StateCode != 6) {
                where += " and StateCode=" + StateCode + "";
            }
            string countWhere = "";
            if (!string.IsNullOrEmpty(CompanyName)) {
                where += " and CompanyName like '%" + CompanyName + "%'";
                countWhere += " and CompanyName like '%" + CompanyName + "%'";
            }

            try {
                List<CompanyRegisterRequest> C = service.GetPagerList<CompanyRegisterRequest>(PageIndex, PageSize, where, Sort);
                List<dynamic> ResultInfo = new List<dynamic>();
                C.ForEach(data => {
                    var item = new {
                        ID = data.ID,
                        CompanyName = data.CompanyName,
                        CompanyNo = data.CompanyNo,
                        CompanyType = data.CompanyType,
                        CreateUserID = data.CreateUserID,
                        CreateUserName = data.CreateUserName,
                        CreateUserPersonNo = data.CreateUserPersonNo,
                        CreateTime = !string.IsNullOrEmpty(data.CreateTime.ToString()) ? string.Format("{0:G}", data.CreateTime).Replace('/', '-') : null,
                        PostTime = !string.IsNullOrEmpty(data.PostTime.ToString()) ? string.Format("{0:G}", data.PostTime).Replace('/', '-') : null,
                        AcceptAuditTime = !string.IsNullOrEmpty(data.AcceptAuditTime.ToString()) ? string.Format("{0:G}", data.AcceptAuditTime).Replace('/', '-') : null,
                        SuccessTime = !string.IsNullOrEmpty(data.SuccessTime.ToString()) ? string.Format("{0:G}", data.SuccessTime).Replace('/', '-') : null,
                        CloseTime = !string.IsNullOrEmpty(data.CloseTime.ToString()) ? string.Format("{0:G}", data.CloseTime).Replace('/', '-') : null,
                        StateCode = data.StateCode,
                        ResponseMessage = data.ResponseMessage,
                        ResponsePerson = data.ResponsePerson
                    };
                    ResultInfo.Add(item);
                });
                dynamic CountInfo = new {
                    Total = service.GetList<CompanyRegisterRequest>("StateCode!=0" + countWhere + "").Count,
                    DQS = service.GetList<CompanyRegisterRequest>("StateCode=1 " + countWhere + "").Count,
                    SHZ = service.GetList<CompanyRegisterRequest>("StateCode=2 " + countWhere + "").Count,
                    YTG = service.GetList<CompanyRegisterRequest>("StateCode=3 " + countWhere + "").Count,
                    YTH = service.GetList<CompanyRegisterRequest>("StateCode=4 " + countWhere + "").Count,
                    YGB = service.GetList<CompanyRegisterRequest>("StateCode=5 " + countWhere + "").Count
                };
                dynamic PageInfo = new {
                    PageIndex = PageIndex,
                    PageSize = PageSize,
                    Total = service.GetList<CompanyRegisterRequest>(where).Count
                };
                dynamic deObj = new {
                    DataTable = ResultInfo,
                    Count = CountInfo,
                    Page = PageInfo
                };
                return new ApiResult { StateCode = 1, Data = deObj, Message = "" };
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 0, Message = "单位信息列表获取失败，错误信息：" + e.Message + "" };
            }
        }
        /// <summary>
        /// 根据单位id获取审核详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("GetCompanyAuditInfo"), Obsolete("不再使用", true)]
        public ApiResult GetCompanyAuditInfo(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult { StateCode = 0, Message = "ID不能为空，请传入正确的参数" };
            }
            var record = service.GetById<CompanyRegisterRequest>(id);
            if (record == null) {
                return new ApiResult { StateCode = 0, Message = "无法找到单位注册申请信息" };
            }
            try {
                dynamic auditInfo = new System.Dynamic.ExpandoObject();
                auditInfo.StateCode = record.StateCode;
                auditInfo.ResponsePerson = record.ResponsePerson;
                auditInfo.ResponseMessage = record.ResponseMessage;
                switch (record.StateCode) {
                    case 3://通过
                        auditInfo.ResponseTime = !string.IsNullOrEmpty(record.SuccessTime.ToString()) ? string.Format("{0:G}", record.SuccessTime).Replace('/', '-') : null;
                        break;
                    //case 4://退回
                    //    resultInfo.ResponseTime = record;
                    //    break;
                    case 5://关闭
                        auditInfo.ResponseTime = !string.IsNullOrEmpty(record.CloseTime.ToString()) ? string.Format("{0:G}", record.CloseTime).Replace('/', '-') : null;
                        break;
                    default:
                        auditInfo.ResponseTime = null;
                        break;
                }
                dynamic resultInfo = new {
                    DetailInfo = record.DetailInfo,
                    CompanyAuditInfo = auditInfo
                };
                return new ApiResult { StateCode = 1, Data = resultInfo, Message = "" };
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 0, Message = "获取失败，错误信息：" + e.Message + "" };
            }
        }
        /// <summary>
        /// 保存建设单位或者测绘单位的人员信息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost, Route("SaveDeveloperUserInfo")]
        public ApiResult SaveDeveloperUserInfo(CompanyEmployees request) {
            if (request == null) {
                return new ApiResult { StateCode = 0, Message = "请输入正确的信息" };
            }
            if (string.IsNullOrWhiteSpace(request.RelationRequestId)) {
                return new ApiResult { StateCode = 0, Message = "单位ID不能为空，请传入正确的参数" };
            }
            if (string.IsNullOrWhiteSpace(request.PersonRole)) {
                return new ApiResult { StateCode = 0, Message = "人员角色不能为空，请传入正确的参数" };
            }
            if (string.IsNullOrWhiteSpace(request.PersonNumber)) {
                return new ApiResult { StateCode = 0, Message = "人员身份证号不能为空，请传入正确的参数" };
            }
            if (string.IsNullOrWhiteSpace(request.PersonName)) {
                return new ApiResult { StateCode = 0, Message = "人员姓名不能为空，请传入正确的参数" };
            }

            //2020-10-20增加后端控制只有单位管理员才能添加和编辑人员信息
            if (UserInfo == null) {
                return new ApiResult { StateCode = 0, Message = "操作失败，请先登录" };
            }

            if (!UserInfo.IsSDCCompanyAdmin(request.RelationRequestId)) {
                return new ApiResult { StateCode = 0, Message = "只有本单位的单位管理员才能执行此操作" };
            }

            try {
                var company = service.GetById<CompanyBaseInfo>(request.RelationRequestId);
                if (string.IsNullOrWhiteSpace(request.ID)) {
                    //ID为空时 新增
                    if (company.CompanyType.CompanyBaseTypeFromName() == CompanyBaseType.ConstructionCompany) {
                        //建设单位只能添加报建员
                        if (request.PersonRole != "报建员") {
                            return new ApiResult { StateCode = 0, Message = "建设单位只能添加报建员" };
                        }
                        var record = service.GetList<DeveloperEmployee>("PersonNumber=:pno AND RelationRequestId=:cid"
                            , new OracleParameter(":pno", OracleDbType.Varchar2) { Value = request.PersonNumber }
                            , new OracleParameter(":cid", OracleDbType.Varchar2) { Value = request.RelationRequestId }
                        );
                        if (record.Count > 0) {
                            return new ApiResult { StateCode = 0, Message = "人员信息已存在，请勿重复录入" };

                        }

                        //判断该人员是否已属于测绘单位
                        var surveyEmployees = service.GetList<CompanyEmployees>("PersonNumber=:pno"
                            , new OracleParameter(":pno", OracleDbType.Varchar2) { Value = request.PersonNumber }
                        );
                        if (surveyEmployees.Any()) {
                            return new ApiResult { StateCode = 0, Message = "该人员已属于测绘单位，无法添加到此单位中" };
                        }

                        //开始添加
                        request.ID = Guid.NewGuid().ToString("N");
                        var employee =
                            JsonConvert.DeserializeObject<DeveloperEmployee>(JsonConvert.SerializeObject(request));
                        service.InsertOne(employee);
                    }
                    else if (company.CompanyType.CompanyTypeFromName() == CompanyType.SurveyCompany) {
                        //测绘单位只能添加测绘人员或者单位管理员
                        if (request.PersonRole != "单位管理员" && request.PersonRole != "测绘人员") {
                            return new ApiResult { StateCode = 0, Message = "测绘单位只能添加单位管理员和测绘人员" };
                        }
                        var record = service.GetList<CompanyEmployees>("PersonNumber=:pno", new OracleParameter(":pno", OracleDbType.Varchar2) { Value = request.PersonNumber });
                        if (record.Count > 0) {
                            return record.First().RelationRequestId == request.RelationRequestId
                                ? new ApiResult { StateCode = 0, Message = "人员信息已存在，请勿重复录入" }
                                : new ApiResult { StateCode = 0, Message = $"该人员已经是其它测绘单位的[{record.First().PersonRole}]，不能添加" };
                        }
                        //判断该人员是否已属于开发企业
                        var developeEmployees = service.GetList<DeveloperEmployee>("PersonNumber=:pno"
                            , new OracleParameter(":pno", OracleDbType.Varchar2) { Value = request.PersonNumber }
                        );
                        if (developeEmployees.Any()) {
                            return new ApiResult { StateCode = 0, Message = "该人员已属于建设单位的人员，无法添加到此单位中" };
                        }
                        request.ID = Guid.NewGuid().ToString("N");
                        service.InsertOne(request);
                    }
                    HttpRequestBase req = ((HttpContextBase)Request.Properties["MS_HttpContext"]).Request;
                    var reqInfoHelper = new HttpRequestInfoHelper(req);
                    LogService.WriteLogs(Request, "单位人员管理", $"新增人员 >> ID：{request.ID} >> 姓名：{request.PersonName} >> 身份证号：{request.PersonNumber} >> 角色：{request.PersonRole}", reqInfoHelper);
                }
                else {
                    //ID不为空时 更新
                    if (company.CompanyType.CompanyBaseTypeFromName() == CompanyBaseType.ConstructionCompany) {
                        //建设单位修改
                        var record = service.GetById<DeveloperEmployee>(request.ID);
                        if (record == null) {
                            return new ApiResult { StateCode = 0, Message = "参数ID不正确" };
                        }
                        //ID不为空的时候，不允许修改所在单位，角色，其它可以保存
                        request.RelationRequestId = record.RelationRequestId;
                        request.PersonRole = record.PersonRole;
                        var records = service.GetList<DeveloperEmployee>("PersonNumber=:pno AND ID!=:id AND RelationRequestId=:cid"
                            , new OracleParameter(":pno", OracleDbType.Varchar2) { Value = request.PersonNumber }
                            , new OracleParameter(":id", OracleDbType.Varchar2) { Value = record.ID }
                            , new OracleParameter(":cid", OracleDbType.Varchar2) { Value = record.RelationRequestId }
                        );
                        if (records.Count > 0) {
                            return new ApiResult { StateCode = 0, Message = "人员信息已存在，请勿重复录入" };
                        }
                        //2020-09-09 edit by wy  如果修改管理员角色，至少保证单位还有一个管理员
                        if (request.PersonRole == "单位管理员") {
                            var list = service.GetList<DeveloperEmployee>(
                                "RelationRequestId=:cid and PersonRole='单位管理员' and ID!=:id"
                                , new OracleParameter(":id", OracleDbType.Varchar2) { Value = record.ID }
                                , new OracleParameter(":cid", OracleDbType.Varchar2) { Value = record.RelationRequestId }
                            );
                            if (list.Count == 0) {
                                return new ApiResult { StateCode = 0, Message = "请保证本单位至少有一个单位管理员" };
                            }
                        }
                        var employee =
                            JsonConvert.DeserializeObject<DeveloperEmployee>(JsonConvert.SerializeObject(request));
                        service.UpdateOne(employee);
                    }
                    else if (company.CompanyType.CompanyTypeFromName() == CompanyType.SurveyCompany) {
                        //测绘单位修改人员
                        var record = service.GetById<CompanyEmployees>(request.ID);
                        if (record == null) {
                            return new ApiResult { StateCode = 0, Message = "参数ID不正确" };
                        }
                        //ID不为空的时候，不允许修改所在单位，其它可以保存
                        request.RelationRequestId = record.RelationRequestId;
                        var records = service.GetList<CompanyEmployees>("PersonNumber='" + request.PersonNumber + "' and ID!='" + request.ID + "'");
                        if (records.Count > 0) {
                            return records.First().RelationRequestId == request.RelationRequestId
                                ? new ApiResult { StateCode = 0, Message = "人员信息已存在，请勿重复录入" }
                                : new ApiResult { StateCode = 0, Message = $"该人员已经是其它单位的[{records.First().PersonRole}]，不能保存" };
                        }
                        if (record.PersonRole == "注册测绘师") {
                            return new ApiResult { StateCode = 0, Message = "无法修改注册测绘师角色信息" };
                        }
                        //如果修改管理员角色，至少保证单位还有一个管理员
                        if (request.PersonRole != "单位管理员" && record.PersonRole == "单位管理员") {
                            var list = service.GetList<CompanyEmployees>("RelationRequestId='" + request.RelationRequestId + "' and PersonRole='单位管理员' and ID!='" + request.ID + "'");
                            if (list.Count == 0) {
                                return new ApiResult { StateCode = 0, Message = "请保证本单位至少有一个单位管理员" };
                            }
                        }
                        service.UpdateOne(request);
                    }
                    HttpRequestBase req = ((HttpContextBase)Request.Properties["MS_HttpContext"]).Request;
                    var reqInfoHelper = new HttpRequestInfoHelper(req);
                    LogService.WriteLogs(Request, "单位人员管理", $"编辑人员 >> ID：{request.ID} >> 姓名：{request.PersonName} >> 身份证号：{request.PersonNumber} >> 角色：{request.PersonRole}", reqInfoHelper);
                }
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 0, Message = "保存失败，错误信息：" + e.Message + "" };
            }
            return new ApiResult { StateCode = 1, Message = "保存成功" };
        }
        /// <summary>
        /// 删除建设单位和测绘单位的人员信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost, Route("DeleteDeveloperUserInfo")]
        public ApiResult DeleteDeveloperUserInfo(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult { StateCode = 0, Message = "ID不能为空，请输入正确的信息" };
            }
            int num = 0;
            try {
                //获取要删除的所有id
                var ids = id.Split(",".ToArray(), StringSplitOptions.RemoveEmptyEntries);
                if (ids.Any(item => !Guid.TryParse(item, out var guid))) {
                    return new ApiResult { StateCode = 0, Message = "参数错误，请传入正确的参数" };
                }

                if (ids.Length > 20) {
                    //暂时控制最多只能删除20个
                    return new ApiResult { StateCode = 0, Message = "批量删除用户过多，请减少删除用户的个数" };
                }

                //string where = "";
                //for (int i = 0; i < idObj.Length; i++) {
                //    where += " and ID!='" + idObj[i] + "'";
                //}

                //检测所有ID是否都属于同一家公司
                var conditions = $"ID IN({string.Join(",", ids.Select(item => $"'{item}'"))})";
                var records = service.GetList<CompanyEmployees>(conditions); //先检测是否是测绘单位
                if (!records.Any()) {
                    //不是测绘单位
                    var employees = service.GetList<DeveloperEmployee>(conditions);
                    if (employees.Any()) {
                        records = JsonConvert.DeserializeObject<List<CompanyEmployees>>(JsonConvert.SerializeObject(employees));
                    }
                }

                if (!records.Any()) {
                    return new ApiResult { StateCode = 0, Message = "找不到要删除的人员的记录" };
                }

                if (records.Select(r => r.RelationRequestId).Distinct().Count() > 1) {
                    return new ApiResult { StateCode = 0, Message = "批量删除只能删除同一家单位的人员" };
                }

                //控制删除的权限，只有单位管理员，或者只有移除自己才能执行此操作
                if (!UserInfo.IsSDCCompanyAdmin(records.First().RelationRequestId)) {
                    //不是单位管理员，检测是否是移除自己
                    if (ids.Length != 1 || records.First().PersonNumber != UserInfo.PersonNo) {
                        //不是移除自己
                        return new ApiResult { StateCode = 0, Message = "只有单位管理员才能执行此操作" };
                    }
                }

                //检测删除后，单位是否还有单位管理员
                var company = service.GetById<CompanyBaseInfo>(records.First().RelationRequestId);
                if (company.CompanyType.CompanyBaseTypeFromName() == CompanyBaseType.ConstructionCompany) {
                    var developerEmployees = service.GetList<DeveloperEmployee>($"RelationRequestId='{company.ID}'");
                    if (developerEmployees.Where(e => !ids.Contains(e.ID)).Count(e => e.PersonRole == "单位管理员" && e.UserType != "1") == 0) {
                        return new ApiResult { StateCode = 0, Message = "删除失败，至少保留一个个人单位管理员" };
                    }

                    List<DeveloperEmployee> deleteEmployees = new List<DeveloperEmployee>();
                    foreach (var pid in ids) {
                        var record = service.GetById<DeveloperEmployee>(pid);
                        if (record.UserType == "1") continue; //

                        //判断是否有未移交的业务数据
                        //判断是否有未移交的业务，如果有就提示先移交业务
                        if (service.Exists<BusinessBaseInfo>($"CreatePersonNo='{record.PersonNumber}' AND CreateCompanyNo='{company.CreditCode}'")) {
                            return new ApiResult() { StateCode = 0, Message = $"【{record.PersonName}】有未移交的业务，请先移交业务" };
                        }

                        deleteEmployees.Add(record);
                    }

                    //可以删除
                    foreach (var record in deleteEmployees) {
                        service.DeleteOne(record);
                        num++;
                        //需要记录到系统日志
                        HttpRequestBase req = ((HttpContextBase)Request.Properties["MS_HttpContext"]).Request;
                        var reqInfoHelper = new HttpRequestInfoHelper(req);
                        LogService.WriteLogs(Request, "单位人员管理", $"删除人员 >> ID：{record.ID} >> 姓名：{record.PersonName} >> 身份证号：{record.PersonNumber} >> 角色：{record.PersonRole}", reqInfoHelper);
                    }

                    return new ApiResult { StateCode = num > 0 ? 1 : 0, Message = num > 0 ? "人员删除成功" : "人员信息删除失败" };
                }
                else if (company.CompanyType.CompanyTypeFromName() == CompanyType.SurveyCompany) {
                    var employees = service.GetList<CompanyEmployees>($"RelationRequestId='{company.ID}'");

                    //判断本次删除的人中，包含了哪些角色
                    //如果包含单位管理员角色，就判断至少保留一位管理员如果包含
                    //注册测绘师就判断至少保留一位测绘师

                    //获取这一批删除的人员都是哪些角色
                    var roles = employees.Where(e => ids.Contains(e.ID)).Select(e => e.PersonRole).Distinct();

                    if (employees.Where(e => !ids.Contains(e.ID)).Count(e => e.PersonRole == "单位管理员" && e.UserType != "1") == 0 && roles.Contains("单位管理员")) {
                        return new ApiResult { StateCode = 0, Message = "删除失败，至少保留一个个人单位管理员" };
                    }
                    if (employees.Where(e => !ids.Contains(e.ID)).Count(e => e.PersonRole == "注册测绘师") == 0 && roles.Contains("注册测绘师")) {
                        return new ApiResult { StateCode = 0, Message = "删除失败，至少保留一位注册测绘师" };
                    }
                    //可以删除
                    foreach (var pid in ids) {
                        var record = service.GetById<CompanyEmployees>(pid);
                        if (record.UserType == "1") continue; //跳转企业类型的单位管理员
                        //如果是注册测绘师，要同时关闭授权
                        if (record.PersonRole == "注册测绘师") {
                            var authList =
                                service.GetList<SurveyMasterAuth>(
                                    "CompanyId=:cid And SurveyMasterPersonNo=:smo And StateCode in(0,1,2)"
                                    , new OracleParameter(":cid", OracleDbType.Varchar2) { Value = record.RelationRequestId }
                                    , new OracleParameter(":smo", OracleDbType.Varchar2) { Value = record.PersonNumber }
                                );
                            if (authList.Any()) {
                                foreach (var auth in authList) {
                                    auth.StateCode = -1;
                                    service.UpdateOne(auth);
                                }
                            }
                        }
                        service.DeleteOne(record);
                        num++;
                        //需要记录到系统日志
                        HttpRequestBase req = ((HttpContextBase)Request.Properties["MS_HttpContext"]).Request;
                        var reqInfoHelper = new HttpRequestInfoHelper(req);
                        LogService.WriteLogs(Request, "单位人员管理", $"删除人员 >> ID：{record.ID} >> 姓名：{record.PersonName} >> 身份证号：{record.PersonNumber} >> 角色：{record.PersonRole}", reqInfoHelper);
                    }

                    return new ApiResult { StateCode = num > 0 ? 1 : 0, Message = num > 0 ? "人员删除成功" : "人员信息删除失败" };
                }
                else {
                    return new ApiResult { StateCode = 0, Message = "删除失败，找不到有效的单位信息" };
                }
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 0, Message = "删除失败，错误信息：" + e.Message + "" };
            }
        }
        /// <summary>
        /// 离开自己所在单位
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost, Route("LeaveTheCompany")]
        public ApiResult LeaveTheCompany(string companyId) {
            //判断自己是否属于此单位
            if (string.IsNullOrWhiteSpace(companyId)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }

            var company = service.GetById<CompanyBaseInfo>(companyId);
            if (company == null) {
                return new ApiResult() { StateCode = 0, Message = "在系统查询不到此单位" };
            }

            //判断是否单位管理员
            var isCompanyAdmin = UserInfo.IsSDCCompanyAdmin(companyId);
            //如果是
            if (isCompanyAdmin) {
                //判断是否最后一个管理员
                //如果是开发商、机关单位、普通单位
                if (company.CompanyType == CompanyType.RealEstateCompany.ToName() || company.CompanyType == CompanyType.Institute.ToName() || company.CompanyType == CompanyType.NormalCompany.ToName()) {
                    var records = service.GetList<DeveloperEmployee>("RelationRequestId=:cid AND PersonRole='单位管理员' AND (USERTYPE != '1' OR USERTYPE IS NULL)"
                        , new OracleParameter(":cid", OracleDbType.Varchar2) { Value = companyId }
                    );
                    if (records.Count <= 1) {
                        return new ApiResult() { StateCode = 0, Message = $"最后一个个人单位管理员不能离职" };
                    }
                }
                else {
                    var records = service.GetList<CompanyEmployees>("RelationRequestId=:cid AND PersonRole='单位管理员' AND (USERTYPE != '1' OR USERTYPE IS NULL)"
                        , new OracleParameter(":cid", OracleDbType.Varchar2) { Value = companyId }
                    );
                    if (records.Count <= 1) {
                        return new ApiResult() { StateCode = 0, Message = $"最后一个个人单位管理员不能离职" };
                    }
                }
            }

            if (company.CompanyType.CompanyBaseTypeFromName() == CompanyBaseType.ConstructionCompany) {

                //判断是否有未移交的业务，如果有就提示先移交业务
                if (service.Exists<BusinessBaseInfo>($"CreatePersonNo='{UserInfo.PersonNo}' AND CreateCompanyNo='{company.CreditCode}'")) {
                    return new ApiResult() { StateCode = 0, Message = $"请先联系单位管理员移交业务再离职" };
                }

                var relateCompanyList = UserInfo.GetRelateDevelopCompanyList();
                if (relateCompanyList.All(c => c.Key.ID != companyId)) {
                    return new ApiResult() { StateCode = 1, Message = $"您已经不是单位[{company.CompanyName}]的人员" };
                }
                //离开此开发企业
                var developEmployee =
                    service.GetList<DeveloperEmployee>(
                        $"RelationRequestId='{company.ID}' AND PersonNumber='{UserInfo.PersonNo}'");
                if (developEmployee.Any()) {
                    service.DeleteOne(developEmployee.First());
                    HttpRequestBase req = ((HttpContextBase)Request.Properties["MS_HttpContext"]).Request;
                    var reqInfoHelper = new HttpRequestInfoHelper(req);
                    LogService.WriteLogs(Request, "离开单位", $"离开人员 >> ID：{developEmployee.First().ID} >> 姓名：{developEmployee.First().PersonName} >> 身份证号：{developEmployee.First().PersonNumber} >> 角色：{developEmployee.First().PersonRole} >> 所在单位：{company.CompanyName}", reqInfoHelper);
                }
                return new ApiResult() { StateCode = 1, Message = $"您已经成功离开[{company.CompanyName}]" };
            }
            else if (company.CompanyType.CompanyTypeFromName() == CompanyType.SurveyCompany) {
                var relateSurveyCompany = UserInfo.GetRelateSurveyCompany();
                if (relateSurveyCompany.ID != companyId) {
                    return new ApiResult() { StateCode = 1, Message = $"您已经不是单位[{company.CompanyName}]的人员" };
                }
                //离开此测绘单位
                var employee =
                    service.GetList<CompanyEmployees>(
                        $"RelationRequestId='{company.ID}' AND PersonNumber='{UserInfo.PersonNo}'");
                if (employee.Any()) {
                    //如果是注册测绘师，要同时关闭授权
                    if (employee.First().PersonRole == "注册测绘师") {
                        var authList =
                            service.GetList<SurveyMasterAuth>(
                                "CompanyId=:cid And SurveyMasterPersonNo=:smpo And StateCode in(0,1,2)"
                                , new OracleParameter(":cid", OracleDbType.Varchar2) { Value = employee.First().RelationRequestId }
                                , new OracleParameter(":smpo", OracleDbType.Varchar2) { Value = employee.First().PersonNumber }
                                );
                        if (authList.Any()) {
                            foreach (var auth in authList) {
                                auth.StateCode = -1;
                                service.UpdateOne(auth);
                            }
                        }
                    }
                    service.DeleteOne(employee.First());
                    HttpRequestBase req = ((HttpContextBase)Request.Properties["MS_HttpContext"]).Request;
                    var reqInfoHelper = new HttpRequestInfoHelper(req);
                    LogService.WriteLogs(Request, "离开单位", $"离开人员 >> ID：{employee.First().ID} >> 姓名：{employee.First().PersonName} >> 身份证号：{employee.First().PersonNumber} >> 角色：{employee.First().PersonRole} >> 所在单位：{company.CompanyName}", reqInfoHelper);
                }
                return new ApiResult() { StateCode = 1, Message = $"您已经成功离开[{company.CompanyName}]" };
            }
            else {
                return new ApiResult() { StateCode = 0, Message = "单位类型有误" };
            }
        }
        /// <summary>
        /// 根据单位ID获取单位人员信息列表
        /// </summary>
        /// <param name="id"></param>
        /// <param name="PageIndex"></param>
        /// <param name="PageSize"></param>
        /// <returns></returns>
        [HttpGet, Route("GetDeveloperUserList")]
        public ApiResult GetDeveloperUserList(string id, int PageIndex, int PageSize) {
            if (id == null) {
                return new ApiResult { StateCode = 0, Message = "ID不能为空，请输入正确的信息" };
            }
            try {
                var company = service.GetById<CompanyBaseInfo>(id);
                if (company == null) {
                    return new ApiResult { StateCode = 0, Message = "系统中不存在该单位的信息" };
                }


                if (company.CompanyType.CompanyBaseTypeFromName() == CompanyBaseType.ConstructionCompany) {
                    return new ApiResult {
                        StateCode = 1,
                        Data = new {
                            DataTable = service.GetPagerList<DeveloperEmployee>(PageIndex, PageSize, "RelationRequestId=:id", "ID", new OracleParameter(":id", OracleDbType.Varchar2) { Value = company.ID }),
                            Page = new {
                                PageIndex = PageIndex,
                                PageSize = PageSize,
                                Total = service.GetList<DeveloperEmployee>("RelationRequestId=:id", new OracleParameter(":id", OracleDbType.Varchar2) { Value = company.ID }).Count
                            }
                        }
                    };

                }
                //测绘单位
                var list = service.GetPagerList<CompanyEmployees>(PageIndex, PageSize, "RelationRequestId=:id", "ID", new OracleParameter(":id", OracleDbType.Varchar2) { Value = company.ID });
                dynamic PageInfo = new {
                    PageIndex = PageIndex,
                    PageSize = PageSize,
                    Total = service.GetList<CompanyEmployees>("RelationRequestId=:id", new OracleParameter(":id", OracleDbType.Varchar2) { Value = company.ID }).Count
                };
                dynamic deObj = new {
                    DataTable = list,
                    Page = PageInfo
                };
                return new ApiResult { StateCode = 1, Data = deObj };
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 0, Message = "获取失败，错误信息：" + e.Message + "" };
            }
        }
        /// <summary>
        /// 根据开发商的统一社会信用代码，拉取最新的单位信息和管理员信息
        /// </summary>
        /// <param name="companyNo"></param>
        /// <returns></returns>
        [HttpPost, Route("CreateOrReplaceRealEstateDevelopeCompany"), SDCAuthorize(SDCAuthorizeRole.BDCWebAdmin)]
        public ApiResult CreateOrReplaceRealEstateDevelopeCompany(string companyNo) {
            var client = new WebClient() { Proxy = null, Encoding = Encoding.UTF8 };
            var url = $"https://{ExternalApiConfig.LoginServer}/suapi/GetRealEstateDeveloperCompanyInfo?companyNo={companyNo}";
            //有Cookie传Cookie，有Token传Token
            var headerConfig = JObject.FromObject(Request.Properties["SDC-BDCWebHeader"]);
            if (headerConfig["Method"]?.Value<string>() == "Cookies") {
                client.Headers.Set(HttpRequestHeader.Cookie, $@".AspNet.ApplicationCookie={headerConfig["Value"].Value<string>()}");
            }
            else if (headerConfig["Method"]?.Value<string>() == "bearerToken") {
                client.Headers.Set(HttpRequestHeader.Authorization, $@"bearer {headerConfig["Value"].Value<string>()}");
            }
            else {
                return new ApiResult { StateCode = 0, Message = "无法调用接口" };
            }

            var res = client.DownloadString(url);

            if (res.StartsWith("{")) {
                var result = JObject.Parse(res).ToObject<ApiResult>();
                if (result.StateCode == 1) {
                    if (result.Data == null) {
                        return new ApiResult { StateCode = 0, Message = "查询不到有效的企业信息" };
                    }
                    var companyJObject = JObject.FromObject(result.Data);
                    //判断当前企业是要更新还是新增
                    string companyName = companyJObject["CompanyName"].ToString();
                    var companyBaseInfos = service.GetList<CompanyBaseInfo>($"CreditCode='{companyNo}' OR CompanyName='{companyName}'");
                    if (companyBaseInfos.Count > 1) {
                        return new ApiResult { StateCode = 0, Message = "查询到多个企业信息，请联系技术支持核查原因" };
                    }
                    if (companyBaseInfos.Any()) {
                        //更新
                        var record = companyBaseInfos.First();
                        record.CompanyName = companyJObject["CompanyName"]?.Value<string>();
                        record.CreditCode = companyJObject["CompanyNo"]?.Value<string>();
                        record.CompanyAddress = companyJObject["CompanyAddress"]?.Value<string>();
                        record.Contacter = record.Contacter ?? string.Empty;
                        record.ContacterPhone = companyJObject["CompanyPhone"]?.Value<string>();
                        service.UpdateOne(record);

                        //更新管理员
                        //增加判断是否有新增、删除的管理员
                        var adminsRecords = service.GetList<DeveloperEmployee>($"RelationRequestId='{record.ID}' AND PersonRole='单位管理员'");
                        var adminsJArray = companyJObject["CompanyAdmins"].ToObject<JArray>();
                        foreach (var adminJToken in adminsJArray) {
                            var personNo = adminJToken["IDNo"]?.Value<string>();
                            var admin = adminsRecords.FirstOrDefault(a =>
                                a.PersonNumber == personNo);
                            if (admin != null) {
                                admin.PersonPhone = adminJToken["Phone"]?.Value<string>();
                                service.UpdateOne(admin);
                            }
                            else {
                                //不是本单位的管理员，则判断是否已经绑定测绘单位
                                var employees = service.GetList<CompanyEmployees>($"PersonNumber='{personNo}'");
                                if (!employees.Any()) {
                                    //同一个单位不能重复添加人员
                                    var repeatEmployees = service.GetList<DeveloperEmployee>($"PersonNumber='{personNo}' AND RelationRequestId='{record.ID}'");
                                    if (!repeatEmployees.Any()) {
                                        //可以添加
                                        var newAdmin = new DeveloperEmployee {
                                            ID = Guid.NewGuid().ToString("N"),
                                            RelationRequestId = record.ID,
                                            PersonName = adminJToken["Name"]?.Value<string>(),
                                            PersonNumber = personNo,
                                            PersonPhone = adminJToken["Phone"]?.Value<string>(),
                                            PersonRole = "单位管理员"
                                        };
                                        service.InsertOne(newAdmin);
                                    }
                                }
                                else {
                                    //该管理员已绑定测绘单位，则不能绑定开发商
                                    continue;
                                }
                            }
                        }
                    }
                    else {
                        //新建
                        var record = new CompanyBaseInfo() {
                            ID = Guid.NewGuid().ToString()
                        };
                        record.RelationRequestId = record.ID;
                        record.CompanyName = companyJObject["CompanyName"]?.Value<string>();
                        record.CreditCode = companyJObject["CompanyNo"]?.Value<string>();
                        record.CompanyAddress = companyJObject["CompanyAddress"]?.Value<string>();
                        record.ContacterPhone = companyJObject["CompanyPhone"]?.Value<string>();
                        record.CompanyType = "开发商";
                        record.Contacter = record.Contacter ?? string.Empty;

                        service.InsertOne(record);

                        //新增管理员
                        var adminsJArray = companyJObject["CompanyAdmins"].ToObject<JArray>();
                        foreach (var adminJToken in adminsJArray) {
                            var personNo = adminJToken["IDNo"]?.Value<string>();
                            //判断是否已经绑定测绘单位
                            var employees = service.GetList<CompanyEmployees>($"PersonNumber='{personNo}'");
                            if (!employees.Any()) {
                                //可以添加 
                                var newAdmin = new DeveloperEmployee {
                                    ID = Guid.NewGuid().ToString("N"),
                                    RelationRequestId = record.ID,
                                    PersonName = adminJToken["Name"]?.Value<string>(),
                                    PersonNumber = personNo,
                                    PersonPhone = adminJToken["Phone"]?.Value<string>(),
                                    PersonRole = "单位管理员"
                                };
                                service.InsertOne(newAdmin);
                            }
                            else {
                                //该管理员已绑定测绘单位，则不能绑定开发商
                                continue;
                            }

                        }
                    }
                    return new ApiResult() { StateCode = 1, Message = "操作完成" };
                }
                else {
                    return result;
                }
            }
            else {
                return new ApiResult { StateCode = 0, Message = "系统异常" };
            }
        }

        /// <summary>
        /// 根据单位ID获取单位注册测绘师列表
        /// </summary>
        /// <param name="id"></param>
        /// <param name="q"></param>
        /// <param name="PageIndex"></param>
        /// <param name="PageSize"></param>
        /// <returns></returns>
        [HttpGet, Route("GetSurveyMasterList")]
        public ApiResult GetSurveyMasterList(string id, int PageIndex, int PageSize, string q = "") {
            if (id == null) {
                return new ApiResult { StateCode = 0, Message = "ID不能为空，请输入正确的信息" };
            }
            try {
                string where = "RelationRequestId=:cid and PersonRole='注册测绘师'";
                var parameters = new List<OracleParameter>();
                parameters.Add(new OracleParameter(":cid", OracleDbType.Varchar2) { Value = id });
                if (!string.IsNullOrWhiteSpace(q)) {
                    where += $" and PersonName like :likeq or PersonPhone like :likeq";
                    parameters.Add(new OracleParameter(":likeq", OracleDbType.Varchar2) { Value = $"%{q}%" });
                }
                var list = service.GetPagerList<CompanyEmployees>(PageIndex, PageSize, where, "ID", parameters.ToArray());
                dynamic PageInfo = new {
                    PageIndex = PageIndex,
                    PageSize = PageSize,
                    Total = service.GetList<CompanyEmployees>(where, parameters.ToArray()).Count
                };
                dynamic deObj = new {
                    DataTable = list,
                    Page = PageInfo
                };
                return new ApiResult { StateCode = 1, Data = deObj };
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 0, Message = "获取失败，错误信息：" + e.Message + "" };
            }
        }

        /// <summary>
        /// 根据单位ID获取单位注册测绘师列表和授权情况
        /// </summary>
        /// <param name="id"></param>
        /// <param name="PageIndex"></param>
        /// <param name="PageSize"></param>
        /// <param name="q"></param>
        /// <returns></returns>
        [HttpGet, Route("GetSurveyMasterWithAuthInfoList")]
        public ApiResult GetSurveyMasterWithAuthInfoList(int PageIndex, int PageSize, string q = "") {
            try {
                //获取当前用户身份
                var surveyCompany = UserInfo.GetRelateSurveyCompany();
                if (surveyCompany == null) {
                    return new ApiResult { StateCode = 0, Message = "您不是测绘单位的人员" };
                }
                //获取所在单位角色
                var me = service.GetList<CompanyEmployees>("RelationRequestId=:cid And PersonNumber=:pno"
                    , new OracleParameter(":cid", OracleDbType.Varchar2) { Value = surveyCompany.ID }
                    , new OracleParameter(":pno", OracleDbType.Varchar2) { Value = UserInfo.PersonNo }
                ).FirstOrDefault();
                if (me == null) {
                    return new ApiResult { StateCode = 0, Message = "您不是测绘单位的人员" };
                }

                if (me.PersonRole == "测绘人员") {
                    //写死返回空数据
                    return new ApiResult {
                        StateCode = 1,
                        Data = new {
                            DataTable = new List<object>(),
                            Page = new {
                                PageIndex = PageIndex,
                                PageSize = PageSize,
                                Total = 0
                            }
                        }
                    };
                }

                string where = "RelationRequestId=:cid and PersonRole='注册测绘师'";
                if (me.PersonRole == "注册测绘师") {
                    where += $" And PersonNumber='{UserInfo.PersonNo}'";
                }
                var parameters = new List<OracleParameter>();
                parameters.Add(new OracleParameter(":cid", OracleDbType.Varchar2) { Value = surveyCompany.ID });
                if (!string.IsNullOrWhiteSpace(q)) {
                    where += $" and PersonName like :likeq or PersonPhone like :likeq";
                    parameters.Add(new OracleParameter(":likeq", OracleDbType.Varchar2) { Value = $"%{q}%" });
                }
                var list = service.GetPagerList<CompanyEmployees>(PageIndex, PageSize, where, "ID", parameters.ToArray());
                var surveyMasterList = list.Select(SurveyMasterWithAuthModel.FromModel).ToList();
                foreach (var authModel in surveyMasterList) {
                    var auth = service.GetList<SurveyMasterAuth>(
                        "SurveyMasterID=:smid And CompanyID=:cid And StateCode in(0,1,2)"
                        , new OracleParameter(":smid", OracleDbType.Varchar2) { Value = authModel.ID }
                        , new OracleParameter(":cid", OracleDbType.Varchar2) { Value = authModel.RelationRequestId }
                        ).FirstOrDefault();
                    if (auth != null) {
                        authModel.AuthRequest = SurveyMasterAuthRequest.FromSurveyMasterAuth(auth);
                        authModel.AuthRequest.BusinessClasses = service.GetList<SurveyMasterAuthClass>("AuthID=:aid"
                            , new OracleParameter(":aid", OracleDbType.Varchar2) { Value = auth.ID }
                        ).Select(c => c.ToModel()).ToList();
                    }
                }
                dynamic PageInfo = new {
                    PageIndex = PageIndex,
                    PageSize = PageSize,
                    Total = service.GetList<CompanyEmployees>(where, parameters.ToArray()).Count
                };
                dynamic deObj = new {
                    DataTable = surveyMasterList,
                    Page = PageInfo
                };
                return new ApiResult { StateCode = 1, Data = deObj };
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 0, Message = "获取失败，错误信息：" + e.Message + "" };
            }
        }

        /// <summary>
        /// 提交授权申请、修改授权申请信息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost, Route("SaveSurveyMasterAuth")]
        public ApiResult SaveSurveyMasterAuth(SurveyMasterAuthRequest request) {
            //////判断参数是否正确

            //空值判断
            if (request == null) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }
            if (string.IsNullOrWhiteSpace(request.CompanyID)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误，CompanyID不能为空" };
            }
            if (string.IsNullOrWhiteSpace(request.SurveyMasterID)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误，SurveyMasterID不能为空" };
            }
            if (request.BusinessClasses == null || !request.BusinessClasses.Any()) {
                return new ApiResult() { StateCode = 0, Message = "参数错误，BusinessClasses不能为空" };
            }

            //逻辑有效性判断
            var company = service.GetById<CompanyBaseInfo>(request.CompanyID);
            if (company?.CompanyType.CompanyTypeFromName() != CompanyType.SurveyCompany) {
                return new ApiResult() { StateCode = 0, Message = "参数错误，该单位不属于有效的测绘单位" };
            }
            var surveyMaster = service.GetById<CompanyEmployees>(request.SurveyMasterID);
            if (surveyMaster?.PersonRole != "注册测绘师") {
                return new ApiResult() { StateCode = 0, Message = "参数错误，该人员不存在或者不是注册测绘师，请核对" };
            }
            if (request.BusinessClasses.Any(c => !BusinessFlowConfig.EnableFlows.Contains(c.BusinessClass))) {
                return new ApiResult() { StateCode = 0, Message = "参数错误，BusinessClasses不正确，请检查" };
            }
            if (request.AuthEndDate.Date < DateTime.Today) {
                return new ApiResult() { StateCode = 0, Message = "参数错误，AuthEndDate不能早于当前的日期" };
            }

            SurveyMasterAuth authRecord = null;
            if (!string.IsNullOrWhiteSpace(request.ID)) {
                authRecord = service.GetById<SurveyMasterAuth>(request.ID);
                if (authRecord == null) {
                    return new ApiResult() { StateCode = 0, Message = "参数错误，ID不为空但找不到数据记录" };
                }
                else if (authRecord.StateCode != 0) {
                    return new ApiResult() { StateCode = 0, Message = "当前授权的环节已不允许修改" };
                }
            }

            //检查此注册测绘师是否已经有其它正在生效的授权
            var recordAuthList = service.GetList<SurveyMasterAuth>("SurveyMasterPersonNo=:smno And StateCode in(0,1)"
                , new OracleParameter(":smno", OracleDbType.Varchar2) { Value = surveyMaster.PersonNumber });
            if (authRecord == null && recordAuthList.Any() || authRecord != null && recordAuthList.Any(a => a.ID != authRecord.ID)) {
                return new ApiResult() { StateCode = 0, Message = "已有其它已生效或待确认的授权，不能重复发起授权" };
            }

            //////判断调用者的权限
            var companyid = request.CompanyID;
            var surverCompany = UserInfo.GetRelateSurveyCompany();
            if (surverCompany == null) {
                return new ApiResult() { StateCode = 0, Message = "您不是测绘单位的人员，不能进行此操作" };
            }
            var operPerson = service.GetList<CompanyEmployees>("RelationRequestId=:cid And PersonNumber=:pno And PersonRole='单位管理员'"
                , new OracleParameter(":cid", OracleDbType.Varchar2) { Value = surverCompany.ID }
                , new OracleParameter(":pno", OracleDbType.Varchar2) { Value = UserInfo.PersonNo }
            ).FirstOrDefault();
            if (operPerson == null) {
                return new ApiResult() { StateCode = 0, Message = "您不是本单位的管理员，不能进行此操作" };
            }
            if (companyid != surverCompany.ID) {
                return new ApiResult() { StateCode = 0, Message = "您不能操作其它单位的信息" };
            }
            //处理数据
            request.ID = request.ID ?? Guid.NewGuid().ToString("N");
            request.SurveyMasterPersonNo = surveyMaster.PersonNumber;
            request.StateCode = 0;
            request.CreateDate = DateTime.Now;
            request.Creator = UserInfo.PersonName;
            request.CreatorID = UserInfo.UserId;

            var auth = request.ToSurveyMasterAuth();
            foreach (var authClassModel in request.BusinessClasses) {
                authClassModel.AuthID = request.ID;
                authClassModel.ID = authClassModel.ID ?? Guid.NewGuid().ToString("N");
            }

            var authClassList = request.BusinessClasses.Select(SurveyMasterAuthClass.FromModel).ToList();

            //移除已过期数据
            var expireList = service.GetList<SurveyMasterAuth>($"CompanyID=:cid And SurveyMasterPersonNo=:smno And StateCode=2"
                , new OracleParameter(":cid", OracleDbType.Varchar2) { Value = surverCompany.ID }
                , new OracleParameter(":smno", OracleDbType.Varchar2) { Value = auth.SurveyMasterPersonNo }
                );
            if (expireList?.Any() == true) {
                foreach (var expiredAuth in expireList) {
                    expiredAuth.StateCode = -1;
                    service.UpdateOne(expiredAuth);
                }
            }

            //保存数据，更新旧状态
            if (authRecord == null) {
                //新增
                service.InsertOne(auth);
                foreach (var authClass in authClassList) {
                    service.InsertOne(authClass);
                }
                //写入日志
                HttpRequestBase req = ((HttpContextBase)Request.Properties["MS_HttpContext"]).Request;
                var reqInfoHelper = new HttpRequestInfoHelper(req);
                LogService.WriteLogs(Request, "注册测绘师授权新建", $"注册测绘师授权新建 >> ID：{request.ID} >> 测绘单位：{company.CompanyName} >> 注册测绘师姓名：{surveyMaster.PersonName} >> 身份证号：{surveyMaster.PersonNumber} >> 授权业务：{string.Join(",", authClassList.Select(a => a.BusinessClass))} >> 授权期限：{request.AuthEndDate:yyyy-MM-dd HH:mm:ss}", reqInfoHelper);
            }
            else {
                //修改
                service.UpdateOne(auth);
                foreach (var authClass in authClassList) {
                    if (service.GetById<SurveyMasterAuthClass>(authClass.ID) == null) {
                        service.InsertOne(authClass);
                    }
                    else {
                        service.UpdateOne(authClass);
                    }
                }
                //写入日志
                HttpRequestBase req = ((HttpContextBase)Request.Properties["MS_HttpContext"]).Request;
                var reqInfoHelper = new HttpRequestInfoHelper(req);
                LogService.WriteLogs(Request, "注册测绘师授权修改", $"注册测绘师授权修改 >> ID：{request.ID} >> 测绘单位：{company.CompanyName} >> 注册测绘师姓名：{surveyMaster.PersonName} >> 身份证号：{surveyMaster.PersonNumber} >> 授权业务：{string.Join(",", authClassList.Select(a => a.BusinessClass))} >> 授权期限：{request.AuthEndDate:yyyy-MM-dd HH:mm:ss}", reqInfoHelper);
            }

            var returnAuth = SurveyMasterAuthRequest.FromSurveyMasterAuth(auth);
            returnAuth.BusinessClasses = request.BusinessClasses;
            return new ApiResult() { StateCode = 1, Data = returnAuth };
        }

        /// <summary>
        /// 测绘师未确认的时候，撤销关闭
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost, Route("CancelSurveyMasterAuth")]
        public ApiResult CancelSurveyMasterAuth(string id) {
            //判断参数是否正确
            //空值判断
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult() { StateCode = 0, Message = "ID不能为空，请输入正确的信息" };
            }
            //接口逻辑判断
            var record = service.GetById<SurveyMasterAuth>(id);
            if (record == null) {
                return new ApiResult() { StateCode = 0, Message = "系统未能找到对应的数据" };
            }

            if (record.StateCode != 0) {
                return new ApiResult() { StateCode = 0, Message = "当前的状态无法撤销" };
            }
            //判断调用者的权限
            var surverCompany = UserInfo.GetRelateSurveyCompany();
            if (surverCompany == null) {
                return new ApiResult() { StateCode = 0, Message = "您不是测绘单位的人员，不能进行此操作" };
            }
            var operPerson = service.GetList<CompanyEmployees>("RelationRequestId=:cid And PersonNumber=:pno And PersonRole='单位管理员'"
                , new OracleParameter(":cid", OracleDbType.Varchar2) { Value = surverCompany.ID }
                , new OracleParameter(":pno", OracleDbType.Varchar2) { Value = UserInfo.PersonNo }
            ).FirstOrDefault();
            if (operPerson == null) {
                return new ApiResult() { StateCode = 0, Message = "您不是本单位的管理员，不能进行此操作" };
            }
            if (record.CompanyID != surverCompany.ID) {
                return new ApiResult() { StateCode = 0, Message = "您不能操作其它单位的信息" };
            }
            //处理数据
            record.StateCode = -1;
            //保存数据，更新旧状态           
            service.UpdateOne(record);
            HttpRequestBase req = ((HttpContextBase)Request.Properties["MS_HttpContext"]).Request;
            var reqInfoHelper = new HttpRequestInfoHelper(req);
            LogService.WriteLogs(Request, "注册测绘师授权撤销", $"注册测绘师授权撤销 >> ID：{record.ID} >> 测绘单位：{surverCompany.CompanyName} >> 注册测绘师身份证号：{record.SurveyMasterPersonNo} ", reqInfoHelper);
            return new ApiResult() { StateCode = 1 };
        }

        /// <summary>
        /// 测绘师已授权或者已过期的，关闭此授权，该操作只能由注册测绘师本人操作
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost, Route("CloseSurveyMasterAuth")]
        public ApiResult CloseSurveyMasterAuth(string id) {
            //判断参数是否正确
            //空值判断
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult() { StateCode = 0, Message = "ID不能为空，请输入正确的信息" };
            }
            //接口逻辑判断
            var record = service.GetById<SurveyMasterAuth>(id);
            if (record == null) {
                return new ApiResult() { StateCode = 0, Message = "系统未能找到对应的数据" };
            }

            if (record.StateCode != 1 && record.StateCode != 2) {
                return new ApiResult() { StateCode = 0, Message = "当前的状态无法撤销" };
            }
            //判断调用者的权限
            if (UserInfo.PersonNo != record.SurveyMasterPersonNo && !UserInfo.IsSDCCompanyAdmin(record.CompanyID)) {
                return new ApiResult() { StateCode = 0, Message = "您不是单位管理员或者注册测绘师本人，不能执行此操作" };
            }
            //处理数据
            record.StateCode = -1;
            //保存数据，更新旧状态
            service.UpdateOne(record);
            HttpRequestBase req = ((HttpContextBase)Request.Properties["MS_HttpContext"]).Request;
            var reqInfoHelper = new HttpRequestInfoHelper(req);
            LogService.WriteLogs(Request, "注册测绘师关闭授权", $"注册测绘师关闭授权 >> ID：{record.ID} >> 姓名：{UserInfo.PersonName} >> 身份证号：{UserInfo.PersonNo} ", reqInfoHelper);
            return new ApiResult() { StateCode = 1 };
        }
        /// <summary>
        /// 根据业务ID，获取已授权的注册测绘师
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("GetAuthorizedSurveyMasters")]
        public ApiResult GetAuthorizedSurveyMasters(string id) {
            //判断参数是否正确
            //空值判断
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult() { StateCode = 0, Message = "ID不能为空，请输入正确的信息" };
            }
            //获取业务信息
            var business = service.GetById<BusinessBaseInfo>(id);
            if (business == null) {
                return new ApiResult() { StateCode = 0, Message = "该业务不存在" };
            }
            if (business.BusinessClass == BusinessFlowConfig.BaseSurveyDataDownloadFlow.GetType().Name) {
                return new ApiResult() { StateCode = 0, Message = "该业务不需要测绘单位汇交和确认成果" };
            }
            var company = UserInfo.GetRelateSurveyCompany();
            if (company == null) {
                return new ApiResult() { StateCode = 0, Message = "您不是测绘单位的人员" };
            }
            if (company.CreditCode != business.SurveyCompanyNo) {
                return new ApiResult() { StateCode = 0, Message = "该业务不是委托您所在的测绘单位" };
            }
            //根据CompanyID获取已授权的注册测绘师列表
            var surveyMasterList = service.GetList<CompanyEmployees>(
                $"ID in(Select SurveyMasterID From SurveyMasterAuth Where StateCode=1 And CompanyID=:cid And ID in(SELECT AuthId From SurveyMasterAuthClass Where BusinessClass=:cls)) And RelationRequestId=:cid"
                , new OracleParameter(":cid", OracleDbType.Varchar2) { Value = company.ID }
                , new OracleParameter(":cls", OracleDbType.Varchar2) { Value = business.BusinessClass }
            );
            var surveyMasterWithAuthList = surveyMasterList.Select(SurveyMasterWithAuthModel.FromModel).ToList();
            foreach (var authModel in surveyMasterWithAuthList) {
                var auth = service.GetList<SurveyMasterAuth>(
                    "SurveyMasterID=:smid And CompanyID=:cid And StateCode=1"
                    , new OracleParameter(":smid", OracleDbType.Varchar2) { Value = authModel.ID }
                    , new OracleParameter(":cid", OracleDbType.Varchar2) { Value = authModel.RelationRequestId }
                    ).FirstOrDefault();
                if (auth != null) {
                    authModel.AuthRequest = SurveyMasterAuthRequest.FromSurveyMasterAuth(auth);
                    //authModel.AuthRequest.BusinessClasses = service.GetList<SurveyMasterAuthClass>("AuthID=:aid"
                    //    , new OracleParameter(":aid", OracleDbType.Varchar2) { Value = auth.ID }
                    //).Select(c => c.ToModel()).ToList();
                }
            }
            return new ApiResult() { StateCode = 1, Data = surveyMasterWithAuthList };
        }

        /// <summary>
        /// 注册测绘师本人获取授权列表，包括待确认、已确认、已过期和已失效的
        /// </summary>
        /// <returns></returns>
        [HttpGet, Route("GetMySurveyMasterAuthList")]
        public ApiResult GetMySurveyMasterAuthList() {
            var result = new List<object>();
            var myAuthList = service.GetList<SurveyMasterAuth>("SurveyMasterPersonNo=:pno", new OracleParameter(":pno", OracleDbType.Varchar2) { Value = UserInfo.PersonNo });
            if (myAuthList.Any()) {
                var authList = myAuthList.Select(SurveyMasterAuthRequest.FromSurveyMasterAuth)
                    .OrderBy(a => a.StateCode < 0 ? 99 : a.StateCode).ThenByDescending(a => a.CreateDate).ToList();
                foreach (var auth in authList) {
                    //过滤没有经过确认的记录
                    if (auth.StateCode == -1 && auth.AcceptDate == null) {
                        continue;
                    }
                    auth.BusinessClasses = SurveyMasterAuthClassModel.GetListFromAuthId(auth.ID);
                    string companyName = service.GetById<CompanyBaseInfo>(auth.CompanyID)?.CompanyName;
                    if (string.IsNullOrWhiteSpace(companyName)) {
                        companyName = service.GetList<SurveyCompanyWithDraw>($"CompanyId='{auth.CompanyID}'").FirstOrDefault()
                            ?.CompanyName;
                    }
                    var AuthId = auth.ID;
                    var CompanyName = companyName;
                    var AuthEndDate = auth.AuthEndDate.ToString("yyyy-MM-dd");
                    var Creator = auth.Creator;
                    var CreateDate = auth.CreateDate.ToString("yyyy-MM-dd");
                    var AcceptDate = auth.AcceptDate?.ToString("yyyy-MM-dd");
                    var BusinessClasses = string.Join("、", auth.BusinessClasses
                        .Select(c =>
                            BusinessFlowConfig.AllFlows.FirstOrDefault(f => f.GetType().Name == c.BusinessClass)
                                ?.FlowName).Where(c => !string.IsNullOrWhiteSpace(c)));
                    result.Add(new {
                        AuthId,
                        CompanyName,
                        AuthEndDate,
                        Creator,
                        CreateDate,
                        AcceptDate,
                        BusinessClasses,
                        auth.StateCode
                    });
                }
                return new ApiResult() { StateCode = 1, Data = result };
            }
            return new ApiResult() { StateCode = 1, Data = myAuthList };
        }

        /// <summary>
        /// 注册测绘师刷脸确认授权
        /// </summary>
        /// <param name="authId"></param>
        /// <returns></returns>
        [HttpPost, Route("ConfirmSurveyMasterAuth")]
        public ApiResult ConfirmSurveyMasterAuth(string authId, string faceId = null) {
            if (string.IsNullOrWhiteSpace(authId)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误，authId不能为空" };
            }
            var auth = service.GetById<SurveyMasterAuth>(authId);
            if (auth.StateCode != 0) {
                return new ApiResult() { StateCode = 0, Message = "此授权的状态已不是待确认，不能执行此操作" };
            }
            if (auth.SurveyMasterPersonNo != UserInfo.PersonNo) {
                return new ApiResult() { StateCode = 0, Message = "您不是此授权指定的注册测绘师，不能执行此操作" };
            }

            auth.StateCode = 1;
            auth.AcceptDate = DateTime.Now;

            service.UpdateOne(auth);
            HttpRequestBase req = ((HttpContextBase)Request.Properties["MS_HttpContext"]).Request;
            var reqInfoHelper = new HttpRequestInfoHelper(req);
            LogService.WriteLogs(Request, "注册测绘师确认授权", $"注册测绘师确认授权 >> ID：{auth.ID} >> 姓名：{UserInfo.PersonName} >> 身份证号：{UserInfo.PersonNo} ", reqInfoHelper);

            return new ApiResult() { StateCode = 1, Message = "授权信息已确认" };
        }

        /// <summary>
        /// 获取多测合一业务范围
        /// </summary>
        /// <returns></returns>
        [HttpGet, Route("GetSurveyFlows")]
        public ApiResult GetSurveyFlows() {
            var flows = BusinessFlowConfig.AllFlows.Where(f => f.Enable && f.HasSurvey);
            var result = new List<dynamic>();
            foreach (var flow in flows) {
                if (result.Any(r => r.label == flow.Catalog)) {
                    result.First(r => r.label == flow.Catalog).children.Add(new {
                        value = flow.GetType().Name,
                        label = flow.FlowName,
                        children = default(object)
                    });
                }
                else {
                    result.Add(new {
                        value = flow.Catalog,
                        label = flow.Catalog,
                        children = new List<object>() {
                            new {
                                value = flow.GetType().Name,
                                label = flow.FlowName,
                                children = default(object)
                            }
                        }
                    });
                }
            }
            return new ApiResult() { StateCode = 1, Data = result };
        }
    }
}

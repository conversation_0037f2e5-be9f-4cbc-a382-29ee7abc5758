﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using SDCPCWeb.Models.Attachment;

namespace SDCPCWeb.Models.BusinessFlow {
    public abstract class BaseProject {
        /// <summary>
        /// 基本信息
        /// </summary>
        public abstract BusinessBaseInfoModel BaseInfo { get; set; }
        /// <summary>
        /// 附件信息
        /// </summary>
        public abstract List<AttachmentInfoModel> Attachments { get; set; }
        /// <summary>
        /// 当前经过的流转环节信息
        /// </summary>
        public abstract List<BusinessLinkInfoModel> ActionsInfos { get; set; }
        /// <summary>
        /// 最新的环节，当前环节
        /// </summary>
        public abstract BusinessLinkInfoModel CurrentAction { get; set; }
        /// <summary>
        /// 工作流定义信息
        /// </summary>
        public abstract BusinessFlowBase FlowInfo { get; }

    }
}
﻿#region 文件信息描述
// /* ===============================================
// * 功能描述：
// * 创 建 人：吕俊宏
// * 创建日期：2021年01月19日 9:03
// * 项目名称：SDCPCWeb
// * 文件名称：RequestExtenstion.cs
// * 创建用户名：Lvjunhong
// * ================================================*/
#endregion

using System.Linq;
using System.Net.Http;
using System.Web;
using SDCPCWeb.Services;

namespace SDCPCWeb.Extensions {
    public static class RequestExtenstion {
        /// <summary>
        /// 从Cookie中获取SDC-CID的值，从浏览器的Cookie中获取当前的值
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public static string GetSDCCompanyIdFromCookie(this HttpRequestMessage request) {
            return request?.Headers.GetCookies().FirstOrDefault()?.Cookies.FirstOrDefault(c => c.Name == "SDC-CID")?.Value;
        }

        /// <summary>
        /// 添加日志记录统一方法
        /// </summary>
        /// <param name="request"></param>
        /// <param name="operationType"></param>
        /// <param name="operationContent"></param>
        public static void WriteSDCLog(this HttpRequestMessage request, string operationType, string operationContent,string remark = null) {
            HttpRequestBase req = ((HttpContextBase)request?.Properties["MS_HttpContext"])?.Request;
            var reqInfoHelper = new HttpRequestInfoHelper(req);
            LogService.WriteLogs(request, operationType, operationContent, reqInfoHelper, remark);

        }
    }
}
﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using SDCPCWeb.Models.Share;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SDCPCWeb.Models.Mongo;
using SDCPCWeb.Models.Result;
using SDCPCWeb.Models.System;
using SDCPCWeb.Services;

namespace SDCPCWeb.Models.Share.Tests {
    [TestClass()]
    public class ShareMethodsTests {
        [TestMethod()]
        public void ShareMethodsTest() {
            var share = new ShareMethods();
        }

        [TestMethod()]
        public void ExecuteMethodTest() {
            BusinessFlowConfig.Initialize();
            var share = new ShareMethods();
            ApiResult result;
            //result = share.ExecuteMethod("GetMarkPointProjectList", new ShareMethods.GetMarkPointProjectListInput() {
            //    ProjectName = "邕武医院扩建工程",
            //});
            //Console.WriteLine(JsonConvert.SerializeObject(result));
            //result = share.ExecuteMethod("GetMarkPointProjectInfo", new ShareMethods.GetMarkPointProjectInfoInnput() {
            //    ID = "67034141e4c348a58323b6a8cb3556d2",
            //});
            //Console.WriteLine(JsonConvert.SerializeObject(result));
            result = share.ExecuteMethod("GetEstateActualSurveyProjectList",
                new ShareMethods.GetEstateActualSurveyProjectListInput() {
                    BusinessNumber = "2020101000036",
                    //ProjectName = "大唐",
                });
            Console.WriteLine(JsonConvert.SerializeObject(result));
            result = share.ExecuteMethod("GetEstateActualSurveyProjectInfo",
                new ShareMethods.GetMarkPointProjectInfoInnput() {
                    ID = "50091b40fed74b3cab547a7ac8cea7ae",
                });
            Console.WriteLine(JsonConvert.SerializeObject(result));

        }

        [TestMethod]
        public void MakeShareRequestTest() {
            var request = new ShareRequest() {
                ExternIdentity = "sp.nanning.gov.cn",
                MethodName = "GetMarkPointProjectInfo",
                Parameters = new ShareMethods.GetMarkPointProjectInfoInnput {
                    ID = "67034141e4c348a58323b6a8cb3556d2"
                }
            };
            var data = JsonConvert.SerializeObject(request);
            Console.WriteLine(data);
            var rsa = new RSAService();
            rsa.LoadPublicKeyFile(@"C:\publickey.cer");
            Console.WriteLine(JsonConvert.SerializeObject(new {
                Data = rsa.Encrypt(data),
                Sign = "AAA"
            }));

        }

        [TestMethod]
        public void MakeShareRequestForSPTest() {
            var data = "{\"Parameters\":{\"ProjectCode\":\"2020-450103-47-03-019813\",\"ProjectName\":\"融创九熙府三期\"}}";
            Console.WriteLine(data);
            var mongo = new MongoService<ExternIdentity>();
            var identity = mongo.GetById("sp.nanning.gov.cn");
            var rsa = new RSAService(identity.PublicKey);
            Console.WriteLine(JsonConvert.SerializeObject(new {
                Data = rsa.Encrypt(data),
                Sign = rsa.SignData(data)
            }));
        }

        [TestMethod]
        public void ReceiveShareResponseFromSPTest() {
            var responseContent = @"
{
    ""Data"": ""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"",
    ""Sign"": ""da0xG1hN2UEq1yRi0amX2EazkWBfFOi4U1TxB36b+uTpjgtV8o8EJcbyjEV3NLMgP+iMxVulQRCkCVmcEt9UdzOr7fP4ZuH1Xfn9jZY9D1/Z6sShLBNqQ1rZbX+Sqf1j7JIhGP5En6VrKm76rrCtMHDs/UJappQy9pgc/X3B0IY=""
}
";
            var data = JsonConvert.DeserializeObject<SDCEncryptedData>(responseContent);
            var mongo = new MongoService<ExternIdentity>();
            var identity = mongo.GetById("sp.nanning.gov.cn");
            var rsa = new RSAService(identity.PublicKey);

            Console.WriteLine("对Data的密文进行解密：");
            var decryptedData = rsa.Decrypt(data.Data);
            Console.WriteLine(decryptedData);

            Console.WriteLine("使用解密后明文对Sign的数字签名进行验签：");
            Console.WriteLine($"验签结果：{rsa.VerifyData(decryptedData, data.Sign)}");

        }

        [TestMethod]
        public void AddExternIdentityTest() {
            var identity = new ExternIdentity() {
                Id = "sp.nanning.gov.cn",
                Name = "南宁市行政审批局-南宁市工程建设审批系统",
                PublicKey = @"",
                IsValid = true
            };

            var mongo = new MongoService<ExternIdentity>();
            mongo.Insert(identity);
        }
    }
}
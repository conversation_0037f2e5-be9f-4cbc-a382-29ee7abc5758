﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;

namespace SDCPCWeb.Models.Result {
    public class PageResult {
        public int PageIndex { get; set; }
        public int PageSize { get; set; }
        public int TotalCount { get; set; }
        public object List { get; set; }
    }
    public class PageResult<T> {
        public Page Page { get; set; } = new Page();
        public List<T> DataTable { get; set; }
    }

    public struct Page {
        public int PageIndex;
        public int PageSize;
        public int Total;
    }
}
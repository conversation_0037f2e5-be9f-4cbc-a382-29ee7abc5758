﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Web;
using System.Web.Configuration;
using System.Web.Http;
using Hangfire;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Oracle.ManagedDataAccess.Client;
using SDCPCWeb.Jobs;
using SDCPCWeb.Models.Attachment;
using SDCPCWeb.Models.BusinessContent.GuiHuaDingDian;
using SDCPCWeb.Models.BusinessContent;
using SDCPCWeb.Models.BusinessContent.GuiHuaHeShi;
using SDCPCWeb.Models.BusinessContent.KanCeDingJie;
using SDCPCWeb.Models.BusinessContent.YanXian;
using SDCPCWeb.Models.BusinessFlow;
using SDCPCWeb.Models.EstateConstruction;
using SDCPCWeb.Models.Result;
using SDCPCWeb.Models.System;
using SDCPCWeb.Services;

namespace SDCPCWeb.Controllers {
    /// <summary>
    /// 提供给EPS相关模块调用的接口控制器
    /// </summary>
    [RoutePrefix("internal/epsapi")]
    public class EPSApiController : ApiController {
        private readonly OracleDataService service = new OracleDataService();
        /// <summary>
        /// 获取预测绘项目相关信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("GetPreSurveyProjectInfo")]
        public ApiResult GetPreSurveyProjectInfo(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult { StateCode = 0, Message = "请输入正确的信息" };
            }
            var record = service.GetById<BusinessBaseInfo>(id);
            if (record == null) {
                return new ApiResult { StateCode = 0, Message = "无法找到业务信息" };
            }
            try {
                if (record.BusinessClass == "RealEstatePreSurveyFlow" 
                    || record.BusinessClass == BusinessFlowConfig.RealEstatePreCheckSurveyFlow.GetType().Name
                    || record.BusinessClass == BusinessFlowConfig.RealEstatePreCheckSurveyAutoFlow.GetType().Name
                    || record.BusinessClass == BusinessFlowConfig.RealEstatePreSurveyResultChangeFlow.GetType().Name
                    || record.BusinessClass == BusinessFlowConfig.RealEstatePreSurveyBuildingTableChangeFlow.GetType().Name) {
                    PreSurvey_ContentInfo content = service.GetById<PreSurvey_ContentInfo>(record.ID);
                    List<ProjectPlanPermissionInfo> projectList = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(content.ProjectPlanPermission);
                    ProjectPlanPermissionInfo project = projectList[0];
                    dynamic result = new {
                        project.Code,
                        project.ProjectName,
                        project.Address,
                        ParcelNo = content.GroundCode,
                        record.DeveloperName,
                        record.DeveloperNo,
                        record.SurveyCompanyName,
                        record.SurveyCompanyNo
                    };
                    return new ApiResult { StateCode = 1, Data = result, Message = "获取成功" };
                }
                return new ApiResult { StateCode = 0, Message = "该业务不是不动产预测绘业务" };
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 0, Message = "获取失败，错误信息：" + e.Message + "" };
            }
        }

        /// <summary>
        /// 获取实核测绘项目相关信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("GetRealActualSurveyProjectInfo")]
        public ApiResult GetRealActualSurveyProjectInfo(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult { StateCode = 0, Message = "请输入正确的信息" };
            }
            var record = service.GetById<BusinessBaseInfo>(id);
            if (record == null) {
                return new ApiResult { StateCode = 0, Message = "无法找到业务信息" };
            }
            try {
                if (record.BusinessClass == "RealEstateActualSurveyFlow" || record.BusinessClass == "RealEstateActualResultChangeFlow" || record.BusinessClass == "RealEstateOverallActualSurveyFlow") {
                    var content = service.GetById<EstateActualSurveyContentInfo>(record.ID);
                    List<ProjectPlanPermissionInfo> projectList = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(content.ProjectPlanPermission);
                    dynamic result = new {
                        Code = projectList.Select(project => project.Code).ToList(),
                        ProjectName = projectList.Select(project => project.ProjectName).ToList(),
                        Address = projectList.Select(project => project.Address).ToList(),
                        ParcelNo = content.GroundCode,
                        record.DeveloperName,
                        record.DeveloperNo,
                        record.SurveyCompanyName,
                        record.SurveyCompanyNo
                    };
                    return new ApiResult { StateCode = 1, Data = result, Message = "获取成功" };
                }
                return new ApiResult { StateCode = 0, Message = "该业务不是不动产实核测绘业务" };
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 1, Message = "获取失败，错误信息：" + e.Message + "" };
            }
        }
        /// <summary>
        /// 启动EPS进行数据切图
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost, Route("PostEpsDownLoadResult")]
        public ApiResult PostEpsDownLoadResult(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult { StateCode = 0, Message = "请输入正确的信息" };
            }
            var record = service.GetById<BusinessBaseInfo>(id);
            if (record == null) {
                return new ApiResult { StateCode = 0, Message = "无法找到业务信息" };
            }

            if (!new[] { "BaseSurveyDataDownloadFlow", "BlueLineSurveyFlow", "MarkPointSurveyFlow" }.Contains(record.BusinessClass)) {
                return new ApiResult { StateCode = 0, Message = "业务类型不正确" };
            }
            try {
                //项目范围附件逻辑删除
                List<AttachmentInfo> list = service.GetList<AttachmentInfo>($"BusinessID='{record.ID}' AND AttachmentName='项目范围坐标' and StateCode=0");
                //附件存储根路径
                string RootPath = WebConfigurationManager.AppSettings["RootPath"];
                //获取项目范围
                var txt = File.ReadAllText(RootPath + "\\" + list[0].AttachmentPath);
                txt = txt.Trim().Replace("\r\n", ",");
                txt = txt.Replace("\n", ",");
                var coordinates = LongUrlEncode(txt);
                var url = ExternalApiConfig.EPSSDCServices + "/DownloadBaseData";
                string downloadLayer = record.BusinessClass == "BaseSurveyDataDownloadFlow"
                    ? "地形数据,路网数据,控制性详细规划数据"
                    : "地形数据,路网数据";
                string par = $"ID={id}&strYWMC={HttpUtility.UrlEncode(record.BusinessType)}&strDownloadLayer={HttpUtility.UrlEncode(downloadLayer)}&strCOORDINATE={coordinates}";
                string responseString = "";
                var bytes = Encoding.UTF8.GetBytes(par);
                HttpWebRequest request = WebRequest.Create(url) as HttpWebRequest;
                request.Method = "POST";
                request.ContentType = "application/x-www-form-urlencoded";
                request.ContentLength = bytes.Length;
                using (Stream requestStream = request.GetRequestStream()) {
                    requestStream.Write(bytes, 0, bytes.Length);
                    requestStream.Flush();
                    requestStream.Close();
                }
                using (HttpWebResponse response = request.GetResponse() as HttpWebResponse) {
                    StreamReader reader = new StreamReader(response.GetResponseStream());
                    responseString = reader.ReadToEnd();
                    reader.Close();
                }
                System.Xml.XmlDocument xml = new System.Xml.XmlDocument();
                xml.LoadXml(responseString);
                JObject result = (JObject)JsonConvert.DeserializeObject(xml.InnerText);
                string fileId = result["FILEID"]?.Value<string>();
                if (string.IsNullOrWhiteSpace(fileId)) {
                    //EPS接口有异常，仅返回status信息
                    return new ApiResult() { StateCode = 0, Message = $"提交失败，数据检查接口异常，接口返回消息为：{result["status"].Value<string>()}" };
                }
                if (record.BusinessClass == "BaseSurveyDataDownloadFlow") {
                    //更新检查信息至contentinfo
                    BaseGISData_ContentInfo content = service.GetById<BaseGISData_ContentInfo>(record.ID);
                    content.DownLoadID = fileId;
                    content.DownLoadState = 0;
                    service.UpdateOne(content);
                }
                if (record.BusinessClass == "BlueLineSurveyFlow") {
                    //更新检查信息至contentinfo
                    BlueLineSurveyContentInfo content = service.GetById<BlueLineSurveyContentInfo>(record.ID);
                    content.DownLoadID = fileId;
                    content.DownLoadState = 0;
                    service.UpdateOne(content);
                }
                if (record.BusinessClass == "MarkPointSurveyFlow") {
                    MarkPointSurveyContentInfo content = service.GetById<MarkPointSurveyContentInfo>(record.ID);
                    content.DownLoadID = fileId;
                    content.DownLoadState = 0;
                    service.UpdateOne(content);
                }
                //启动异步作业进行检测
                BackgroundJob.Schedule(() => SurveyResultJob.LoadEPSDownLoadState(fileId, record.ID, null), TimeSpan.FromMinutes(1));

                return new ApiResult() { StateCode = 1, Data = fileId, Message = "提交成功" };
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 0, Message = "提交失败，错误信息：" + e.Message + "" };
            }
        }
        /// <summary>
        /// 获取EPS下载状态
        /// </summary>
        /// <param name="downloadid"></param>
        /// <param name="businessClass"></param>
        /// <returns></returns>
        [HttpGet, Route("GetEPSDownLoadState")]
        public ApiResult GetEPSDownLoadState(string downloadid, string businessClass) {
            /* todo 此接口为前端调用，则只需要检测ContentInfo的下载状态即可*/
            if (downloadid == null) {
                return new ApiResult() { StateCode = 0, Message = "下载ID不能为空" };
            }
            try {
                if (businessClass == "BaseSurveyDataDownloadFlow") {
                    List<BaseGISData_ContentInfo> projectList = service.GetList<BaseGISData_ContentInfo>("DownLoadID='" + downloadid + "'");
                    if (projectList.Count == 0) {
                        return new ApiResult() { StateCode = 0, Message = "下载ID无效" };
                    }
                    return new ApiResult() { StateCode = 1, Data = projectList[0].DownLoadState, Message = "获取成功" };
                }
                if (businessClass == "BlueLineSurveyFlow") {
                    List<BlueLineSurveyContentInfo> projectList = service.GetList<BlueLineSurveyContentInfo>("DownLoadID='" + downloadid + "'");
                    if (projectList.Count == 0) {
                        return new ApiResult() { StateCode = 0, Message = "下载ID无效" };
                    }
                    return new ApiResult() { StateCode = 1, Data = projectList[0].DownLoadState, Message = "获取成功" };
                }
                if (businessClass == "MarkPointSurveyFlow") {
                    List<MarkPointSurveyContentInfo> projectList = service.GetList<MarkPointSurveyContentInfo>("DownLoadID='" + downloadid + "'");
                    if (projectList.Count == 0) {
                        return new ApiResult() { StateCode = 0, Message = "下载ID无效" };
                    }
                    return new ApiResult() { StateCode = 1, Data = projectList[0].DownLoadState, Message = "获取成功" };
                }
                return new ApiResult() { StateCode = 0, Message = "获取失败，业务类型无效" };
            }
            catch (Exception e) {
                return new ApiResult() { StateCode = 0, Message = "获取失败，错误信息：" + e.Message + "" };
            }
        }

        /// <summary>
        /// 获取工程规划许可证信息
        /// </summary>
        /// <param name="code">许可证号或者项目编号</param>
        /// <returns></returns>
        [HttpGet, Route("GetProjectPlanPermissionInfo")]
        public ApiResult GetProjectPlanPermissionInfo(string code) {
            if (string.IsNullOrEmpty(code)) {
                return new ApiResult { StateCode = 0, Message = "请输入正确的工程规划许可证号" };
            }

            string url = ExternalApiConfig.ServiceUrl;
            url += $"?currentPage=1&currentCount=20&text={code}";
            try {
                string result = Get(url);
                if (result.Trim() == "[]") {

                    string url1 = ExternalApiConfig.ServiceUrl1;
                    url1 += $"?text={code}";
                    result = Get(url1);
                    if (result.Trim() == "[]") {
                        return new ApiResult {StateCode = 2, Message = "该《建设工程规划许可证》证号未找到，请核对"};
                    }
                }
                JArray ja = (JArray)JsonConvert.DeserializeObject(result);
                //待处理
                ProjectPlanPermissionInfo project = new ProjectPlanPermissionInfo();
                project.Code = ja[0]["certificateNumber"].ToString();
                if (string.IsNullOrWhiteSpace(project.Code)) {
                    return new ApiResult { StateCode = 2, Message = "该《建设工程规划许可证》证号未找到，请核对" };
                }

                project.ConstructCompany = ja[0]["buildunit"].ToString();
                project.Address = ja[0]["buildaddress"].ToString();
                project.ProjectName = ja[0]["projectname"].ToString();
                project.AppendixImgNumber = ja[0]["projectcode"].ToString();
                JObject gc = (JObject)JsonConvert.DeserializeObject(ja[0]["gc"].ToString());
                JArray lb = (JArray)JsonConvert.DeserializeObject(ja[0]["lb"].ToString());
                project.Buildings = new List<Building>();
                foreach (JObject l in lb) {
                    Building item = new Building {
                        Type = l["fd01"].ToString(),
                        Area = string.IsNullOrEmpty(l["fd02"].ToString()) ? 0 : Convert.ToDecimal(l["fd02"])
                    };
                    project.Buildings.Add(item);
                }
                project.BuildingNature = gc["fd01"].ToString();
                project.BuildingStructure = gc["fd02"].ToString();
                project.TotalArea = new TotalAreaOrFloor {
                    Aboveground = string.IsNullOrEmpty(gc["fd17"].ToString()) ? 0 : Convert.ToDecimal(gc["fd17"]),
                    Underground = string.IsNullOrEmpty(gc["fd18"].ToString()) ? 0 : Convert.ToDecimal(gc["fd18"])
                };
                project.BaseArea = string.IsNullOrEmpty(gc["fd16"].ToString()) ? 0 : Convert.ToDecimal(gc["fd16"]);
                //fd12 地上层数，同一栋不同单元有不同层高，取最大值为层高值 from 李俊双 2020-11-20
                var aboveground = gc["fd12"].ToString();
                var underground = gc["fd13"].ToString();

                if (aboveground.Contains("/")) {
                    var multiFloors = aboveground.Split("/".ToCharArray(), StringSplitOptions.RemoveEmptyEntries);
                    decimal tmpResult;
                    if (multiFloors.All(f => decimal.TryParse(f, out tmpResult))) {
                        //全部都是数值型，取最大值
                        aboveground = multiFloors.Select(Convert.ToDecimal).Max().ToString();
                    }
                }
                else if (aboveground.Contains("F+D")) {
                    //地上层数如果是这么写的话，那就直接从这个字符串中提取地上层数和地下层数
                    var multiParts = aboveground.Replace("F+D", ",")
                        .Split(",".ToCharArray(), StringSplitOptions.RemoveEmptyEntries);
                    aboveground = multiParts[0];
                    underground = multiParts[1];
                }
                //fd13 地下层数，同一栋不同单元有不同层高，取最大值为层高值 from 李俊双 2020-12-30
                if (underground.Contains("/")) {
                    var multiFloors = underground.Split("/".ToCharArray(), StringSplitOptions.RemoveEmptyEntries);
                    decimal tmpResult;
                    if (multiFloors.All(f => decimal.TryParse(f, out tmpResult))) {
                        //全部都是数值型，取最大值
                        underground = multiFloors.Select(Convert.ToDecimal).Max().ToString();
                    }
                }
                project.Floors = new TotalAreaOrFloor {
                    Aboveground = string.IsNullOrEmpty(aboveground) ? 0 : Convert.ToDecimal(aboveground),
                    Underground = string.IsNullOrEmpty(underground) ? 0 : Convert.ToDecimal(underground)
                };
                project.BuildingHeight = string.IsNullOrEmpty(gc["fd07"].ToString()) ? 0 : Convert.ToDecimal(gc["fd07"]);
                project.BuildingBlockNumber = string.IsNullOrEmpty(gc["fd06"].ToString()) ? 0 : Convert.ToInt32(gc["fd06"]);
                project.Invest = string.IsNullOrEmpty(gc["fd08"].ToString()) ? 0 : Convert.ToDecimal(gc["fd08"]);
                JArray tx = (JArray)JsonConvert.DeserializeObject(ja[0]["tx"].ToString());
                project.SetType = new List<SetType>();
                foreach (JObject t in tx) {
                    SetType item = new SetType {
                        Name = t["fd01"].ToString(),
                        Number = string.IsNullOrEmpty(t["fd02"].ToString()) ? 0 : Convert.ToInt32(t["fd02"]),
                        Area = string.IsNullOrEmpty(t["fd03"].ToString()) ? 0 : Convert.ToDecimal(t["fd03"])
                    };
                    project.SetType.Add(item);
                }
                project.Others = new SetType();
                return new ApiResult { StateCode = 1, Data = project };
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 0, Message = "获取失败，错误信息：" + e.Message + "" };
            }
        }

        /// <summary>
        /// 调用Get请求获取数据
        /// </summary>
        /// <param name="url"></param>
        /// <returns></returns>
        private string Get(string url) {
            using (var client = new WebClient() { Proxy = null, Encoding = Encoding.UTF8 }) {
                client.Headers.Add(HttpRequestHeader.ContentType, "application/json");
                client.Headers.Add(HttpRequestHeader.Accept, "text/html, application/xhtml+xml, */*");
                return client.DownloadString(url);
            }
        }

        /// <summary>
        /// 分段对参数进行UrlEncode
        /// </summary>
        /// <param name="toEncodeString"></param>
        /// <returns></returns>
        private string LongUrlEncode(string toEncodeString) {
            const int limit = 4000;
            var sb = new StringBuilder();
            var loops = toEncodeString.Length / limit;
            for (int i = 0; i <= loops; i++) {
                sb.Append(Uri.EscapeDataString(i < loops
                    ? toEncodeString.Substring(limit * i, limit)
                    : toEncodeString.Substring(limit * i)));
            }

            return sb.ToString();
        }
    }
}

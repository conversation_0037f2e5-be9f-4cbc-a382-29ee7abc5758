﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using SDCPCWeb.Services;
using Newtonsoft.Json;
using SDCPCWeb.Models.System;

namespace SDCPCWeb.Models.CompanyRegister {
    public class CompanyInfoModifyRequestModel {
        /// <summary>
        /// 变更ID
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 关联的测绘单位ID
        /// </summary>
        public string CompanyId { get; set; }
        /// <summary>
        /// 申请用户ID
        /// </summary>
        public string CreateUserId { get; set; }
        /// <summary>
        /// 申请用户名称
        /// </summary>
        public string CreateUserName { get; set; }
        /// <summary>
        /// 申请用户身份证号
        /// </summary>
        public string CreateUserNo { get; set; }
        /// <summary>
        /// 申请用户的电话
        /// </summary>
        public string CreateUserPhone { get; set; }
        /// <summary>
        /// 申请变更时间
        /// </summary>
        public DateTime CreateTime { get; set; }
        /// <summary>
        /// 变更类型
        /// </summary>
        public ModifyType Type { get; set; }
        /// <summary>
        /// 变更原因
        /// </summary>
        public string ModifyReason { get; set; }
        /// <summary>
        /// 变更前数据JSON
        /// </summary>
        public string OldContent { get; set; }
        /// <summary>
        /// 变更后数据JSON
        /// </summary>
        public string NewContent { get; set; }
        /// <summary>
        /// 状态：0，未提交，1已提交，2审核中，3审核通过，4审核不通过
        /// </summary>
        public int StateCode { get; set; }
        /// <summary>
        /// 审核签收时间
        /// </summary>
        public DateTime? SignTime { get; set; }
        /// <summary>
        /// 听过时间
        /// </summary>
        public DateTime? AcceptTime { get; set; }
        /// <summary>
        /// 驳回时间
        /// </summary>
        public DateTime? CloseTime { get; set; }
        /// <summary>
        /// 审核人
        /// </summary>
        public string AuditPerson { get; set; }
        /// <summary>
        /// 驳回原因
        /// </summary>
        public string ResponseReason { get; set; }
        /// <summary>
        /// 变更类型枚举转为文字
        /// </summary>
        /// <returns></returns>
        public string ModifyTypeName() {
            if (Type == ModifyType.NothingChanged) {
                return "无信息变更";
            }

            var modifyTypeNames = new List<string>();

            if ((Type & ModifyType.ModifyBaseInfo) != 0) {
                modifyTypeNames.Add("基本信息变更");
            }

            if ((Type & ModifyType.ModifyQualification) != 0) {
                modifyTypeNames.Add("测绘资质信息变更");
            }

            if ((Type & ModifyType.AddAlterSurveyMaster) != 0) {
                modifyTypeNames.Add("添加或修改注册测绘师");
            }

            return modifyTypeNames.Any() ? string.Join("、", modifyTypeNames) : "未知信息变更";
        }
    }

    /// <summary>
    /// 变更类型
    /// </summary>
    [Flags]
    public enum ModifyType {
        /// <summary>
        /// 无修改
        /// </summary>
        NothingChanged = 0x0,
        /// <summary>
        /// 变更基本信息
        /// </summary>
        ModifyBaseInfo = 0x1,
        /// <summary>
        /// 变更资质信息
        /// </summary>
        ModifyQualification = 0x2,
        /// <summary>
        /// 添加或者修改注册测绘师
        /// </summary>
        AddAlterSurveyMaster = 0x4,
    }

    public class CompanyInfoModifyRequest : CompanyInfoModifyRequestModel, IOracleDataTable {
        public CompanyInfoModifyRequestModel ToModel() {
            return JsonConvert.DeserializeObject<CompanyInfoModifyRequestModel>(JsonConvert.SerializeObject(this));
        }

        public CompanyInfoModifyRequestInfoModel ToInfoModel() {
            return JsonConvert.DeserializeObject<CompanyInfoModifyRequestInfoModel>(JsonConvert.SerializeObject(this));
        }
    }

    public class CompanyInfoModifyRequestInfoModel : CompanyInfoModifyRequestModel {
        public string TypeName => ModifyTypeName();
        /// <summary>
        /// 旧模型
        /// </summary>
        public new string OldContent {
            get {
                var model = JsonConvert.DeserializeObject<CompanyRegisterDetailInfo>(base.OldContent);
                return JsonConvert.SerializeObject(model.ToModel(), SystemConfig.JsonDateTimeConverter);
            }
            set => base.OldContent = value;
        }
        /// <summary>
        /// 新模型
        /// </summary>
        public new string NewContent {
            get {
                var model = JsonConvert.DeserializeObject<CompanyRegisterDetailInfo>(base.NewContent);
                return JsonConvert.SerializeObject(model.ToModel(), SystemConfig.JsonDateTimeConverter);
            }
            set => base.NewContent = value;
        }
    }
}
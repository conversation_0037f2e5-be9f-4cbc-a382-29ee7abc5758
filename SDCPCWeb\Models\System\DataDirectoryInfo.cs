﻿using Newtonsoft.Json;
using SDCPCWeb.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace SDCPCWeb.Models.System {
    /// <summary>
    /// 数据字典
    /// </summary>
    public class DataDirectoryModel {
        /// <summary>
        /// ID
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 父级ID
        /// </summary>
        public string ParentID { get; set; }
        /// <summary>
        /// 分类名称
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 分类描述
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 分类状态 0 禁用  1 启用
        /// </summary>
        public int StateCode { get; set; }
        /// <summary>
        /// 分类排序
        /// </summary>
        public int Sort { get; set; }
    }
    public class DataDirectoryInfo : DataDirectoryModel, IOracleDataTable {
        public DataDirectoryModel ToModel() {
            return JsonConvert.DeserializeObject<DataDirectoryModel>(JsonConvert.SerializeObject(this));
        }
    }
    /// <summary>
    /// 数据字典树结构
    /// </summary>
    public class DataDirectoryTree {
        /// <summary>
        /// ID
        /// </summary>
        public string value { get; set; }
        /// <summary>
        /// NAME
        /// </summary>
        public string label { get; set; }
        /// <summary>
        /// 子集
        /// </summary>
        public List<DataDirectoryTree> children { get; set; }
    }
}
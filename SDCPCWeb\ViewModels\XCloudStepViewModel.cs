﻿using System.Collections.Generic;

namespace SDCPCWeb.ViewModels {
    /// <summary>
    /// 云平台步骤业务类型信息
    /// </summary>
    public class XCloudStepBusinessInfoViewModel {
        /// <summary>
        /// 业务类型
        /// </summary>
        public string BusinessClass { get; set; }

        /// <summary>
        /// 步骤列表
        /// </summary>
        public List<XCloudStepInfoViewModel> Steps { get; set; }
    }

    /// <summary>
    /// 步骤信息
    /// </summary>
    public class XCloudStepInfoViewModel {
        /// <summary>
        /// 序号
        /// </summary>
        public int Index { get; set; }

        /// <summary>
        /// 显示的步骤名称
        /// </summary>
        public string DisplayName { get; set; }

        /// <summary>
        /// 是否当前步骤
        /// </summary>
        public bool IsCurrent { get; set; } = false;

        /// <summary>
        /// 是否已完成
        /// </summary>
        public bool IsFinished { get; set; } = false;

        /// <summary>
        /// 绑定的真实步骤名称
        /// </summary>
        public List<string> StepNames { get; set; }
    }
}
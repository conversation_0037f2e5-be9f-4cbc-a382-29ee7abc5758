﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web.Configuration;
using System.Web.Http;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using Oracle.ManagedDataAccess.Client;
using SDCPCWeb.Models;
using SDCPCWeb.Models.Attachment;
using SDCPCWeb.Models.BusinessContent.YanXian;
using SDCPCWeb.Models.BusinessFlow;
using SDCPCWeb.Models.CompanyRegister;
using SDCPCWeb.Models.EstateConstruction;
using SDCPCWeb.Models.External;
using SDCPCWeb.Models.Result;
using SDCPCWeb.Models.System;
using SDCPCWeb.Services;

namespace SDCPCWeb.Controllers {
    /// <summary>
    /// 对外成果共享接口
    /// </summary>
    /// <remarks>邕e登专用</remarks>
    [RoutePrefix("sdcapi/external_bdc"), BDCAuthorize]
    public class ExternalForBDCController : ApiController {
        private OracleDataService oracle = new OracleDataService();
        private UserInfo UserInfo => Request.Properties.ContainsKey("SDC-UserInfo") ? (UserInfo)Request.Properties["SDC-UserInfo"] : null;

        /// <summary>
        /// 获取已完成验收的测绘列表
        /// </summary>
        /// <returns></returns>
        [HttpGet, Route("LoadSurveyProjects")]
        public ApiResult LoadSurveyProjects(string companyId = null) {
            if (UserInfo == null) {
                return new ApiResult() { StateCode = 0, Message = "获取用户信息失败，请重新登录" };
            }
            //获取建设单位信息，不是开发企业的用户不能调用此接口
            var companyList = UserInfo.GetRelateDevelopCompanyList();
            if (!companyList.Any()) {
                return new ApiResult() { StateCode = 0, Message = "用户在多测合一系统不属于任何建设单位" };
            }
            //判断传入的companyID是否与用户所关联的建设单位一致
            var company = companyId == null
                ? companyList.Keys.FirstOrDefault()
                : companyList.Keys.FirstOrDefault(c => c.ID == companyId);
            if (company == null) {
                return new ApiResult() { StateCode = 0, Message = "用户在多测合一系统关联的建设单位信息不一致" };
            }

            var sql = string.Format(SurveyProjectItem.MainSql, UserInfo.UserId, company.CreditCode);

            return new ApiResult() { StateCode = 1, Data = oracle.GetListWithSql<SurveyProjectItem>(sql, "2"), Message = "获取完成" };
        }

        /// <summary>
        /// 获取项目详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("GetPreSurveyProjectInfo")]
        public ApiResult GetPreSurveyProjectInfo(string id) {
            //判断业务
            var business = oracle.GetById<BusinessBaseInfo>(id);
            if (business == null) {
                return new ApiResult() { StateCode = 0, Message = "该业务不存在" };
            }

            if (business.BusinessClass != "RealEstatePreSurveyFlow") {
                return new ApiResult() { StateCode = 0, Message = "该业务不是不动产预测绘业务" };
            }

            if (business.StateCode != 2) {
                return new ApiResult() { StateCode = 0, Message = "该业务未完成验收，请先完成验收" };
            }

            //判断不动产预测绘成果是否已确认完毕并生成测绘成果
            var surveyResult = oracle.GetById<PreSurvey_ContentInfo>(id);
            if (surveyResult == null) {
                return new ApiResult() { StateCode = 0, Message = "该测绘成果不存在" };
            }

            if (surveyResult.SurveyMasterSureTime == null) {
                return new ApiResult() { StateCode = 0, Message = "该测绘成果未经过确认" };
            }

            //组织工规证信息

            var projectInfo = JsonConvert.DeserializeObject<List<object>>(surveyResult.ProjectPlanPermission);

            //组织测绘成果信息

            var buildingTableInfo = JsonConvert.DeserializeObject<List<object>>(surveyResult.BuildingTableInfo);

            //获取楼盘表附件
            var attachments =
                oracle.GetList<AttachmentInfo>(
                    $"BusinessID='{surveyResult.ID}' AND AttachmentType='项目成果附件' AND StateCode=0");



            return new ApiResult() {
                StateCode = 1,
                Data = new {
                    ProjectPlanInfo = projectInfo,
                    SurveyResult = buildingTableInfo,
                    BuildingTableInfoAttachmentID = attachments.FirstOrDefault(a => a.AttachmentName == "楼盘信息表")?.ID,
                    SurveyMDBAttachmentID = attachments.FirstOrDefault(a => a.AttachmentName == "不动产预测绘测绘成果")?.ID,
                }
            };
        }

        /// <summary>
        /// 下载项目附件
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("DownloadAttachment"), AllowAnonymous]
        public HttpResponseMessage DownloadAttachment(string id) {
            //参数要正确
            if (string.IsNullOrWhiteSpace(id)) {
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "参数错误");
            }
            //下载项目附件，只有该项目的创建者才能下载此附件
            var attachment = oracle.GetById<AttachmentInfo>(id);
            if (string.IsNullOrWhiteSpace(attachment?.BusinessID)) {
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "附件不存在");
            }

            var business = oracle.GetById<BusinessBaseInfo>(attachment.BusinessID);
            if (business?.CreateUserId != UserInfo?.UserId && attachment.AttachmentName != "楼盘信息表" && attachment.AttachmentName != "不动产实核业务备案信息表") {
                return Request.CreateErrorResponse(HttpStatusCode.Unauthorized, "您无下载此附件的权限");
            }
            //下载附件
            try {
                string RootPath = WebConfigurationManager.AppSettings["RootPath"];
                var stream = new FileStream(RootPath + "\\" + attachment.AttachmentPath, FileMode.Open);
                HttpResponseMessage response = new HttpResponseMessage(HttpStatusCode.OK);
                response.Content = new StreamContent(stream);
                response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/octet-stream");
                //获取原始文件名称
                var FileName = attachment.AttachmentName + attachment.AttachmentExt;
                response.Content.Headers.ContentDisposition = new ContentDispositionHeaderValue("attachment") {
                    FileName = FileName
                };
                return response;
            }
            catch (Exception e) {
                return Request.CreateErrorResponse(HttpStatusCode.InternalServerError, e.Message);
            }
        }

        /// <summary>
        /// 作废预测绘项目成果
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost, Route("RejectToSurvey")]
        public ApiResult RejectToSurvey(string id) {
            //参数要正确
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }

            var business = oracle.GetById<BusinessBaseInfo>(id);
            if (business == null) {
                return new ApiResult() { StateCode = 0, Message = "该项目不存在" };
            }

            var content = oracle.GetById<PreSurvey_ContentInfo>(id);
            if (content == null) {
                return new ApiResult() { StateCode = 0, Message = "该项目不是预测绘业务" };
            }

            //报建员驳回预测绘业务，DataCheck状态变为-1，标记为该成果已作废
            var planInfos = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(content.ProjectPlanPermission);
            foreach (var planInfo in planInfos) {
                var code = planInfo.Code;
                var estateProject = EstateProjectInfo.GetEstateProjectInfo(id, code, SurveyClass.PreSurvey); //oracle.GetById<EstateProjectInfo>(code);
                estateProject.PreSurveyState = SurveyState.Invalid;
                oracle.UpdateOne(estateProject);
            }
            content.DataCheckState = -1;
            business.ExtendInfo = JsonConvert.SerializeObject(new { RejectByCreatorTime = DateTime.Now }, SystemConfig.JsonDateTimeConverter);
            oracle.UpdateOne(content);
            oracle.UpdateOne(business);
            return new ApiResult() { StateCode = 1, Message = "操作成功" };

        }

        /// <summary>
        /// 获取关联的放线测量成果状态
        /// </summary>
        /// <param name="preSurveyBusinessID"></param>
        /// <returns></returns>
        [HttpPost, Route("GetRelatedPutLineSurveyState"), AllowAnonymous]
        public ApiResult GetRelatedPutLineSurveyState(string preSurveyBusinessID) {
            //取得有效的不动产预测绘项目
            var preSurvey = oracle.GetById<PreSurvey_ContentInfo>(preSurveyBusinessID);
            var putlineSurvey = oracle.GetById<PutLine_ContentInfo>(preSurveyBusinessID);
            if (preSurvey == null && putlineSurvey == null) {
                return new ApiResult() { StateCode = 0, Message = "该不动产预测绘项目不存在" };
            }

            var business = oracle.GetById<BusinessBaseInfo>(preSurveyBusinessID);

            if (business.StateCode != 2) {
                return new ApiResult() { StateCode = 0, Message = "该不动产预测绘项目尚未完成验收" };
            }

            if (preSurvey != null) {
                if (preSurvey.DataCheckState != 1 && preSurvey.DataCheckState != -2) {
                    return new ApiResult() { StateCode = 0, Message = "该不动产预测绘项目尚未通过成果确认" };
                }
            }

            if (putlineSurvey != null) {
                if (putlineSurvey.DataCheckState != 1 && putlineSurvey.DataCheckState != -2) {
                    return new ApiResult() { StateCode = 0, Message = "该不动产预测绘项目尚未通过成果确认" };
                }
            }

            //到工程规划许可关联关系表中获取其状态
            ProjectPlanPermissionInfo plan = null;
            if (preSurvey != null)
                plan = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(preSurvey.ProjectPlanPermission)?
                .FirstOrDefault();
            else
                plan = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(putlineSurvey.ProjectPlanPermission)?
                    .FirstOrDefault();

            var estateProject = EstateProjectInfo.GetEstateProjectInfo(business.ID, plan?.Code ?? "Unknown", preSurvey != null ? SurveyClass.PreSurvey : SurveyClass.Putline); //oracle.GetList<EstateProjectInfo>($"PlanCerificateCode='{plan?.Code ?? "Unknown"}'").FirstOrDefault();
            if (preSurvey == null && putlineSurvey != null && estateProject != null) {
                if (string.IsNullOrEmpty(estateProject.PreSurveyID))
                    estateProject.PreSurveyState = estateProject.PutLineSurveyState;
            }
                
            if (estateProject != null && (estateProject.PutLineSurveyState == SurveyState.CompleteSurvey || estateProject.PutLineSurveyState == SurveyState.CompleteCorrection || estateProject.PutLineSurveyState == SurveyState.CompleteAudit)) {
                return new ApiResult() { StateCode = 1, Message = "获取关联放线测量成功", Data = estateProject };
            }

            return new ApiResult() { StateCode = 0, Message = "未能获取关联的放线测量业务信息" };
        }

        /// <summary>
        /// 根据放线测量业务ID下载放线测量成果报告
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("DownloadPutlineReportBySurveyID")]
        public HttpResponseMessage DownloadPutlineReportBySurveyID(string id) {
            //参数要正确
            Guid guid;
            if (string.IsNullOrWhiteSpace(id) || !Guid.TryParse(id, out guid)) {
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "参数错误");
            }
            //下载项目附件，只有该项目的创建者才能下载此附件
            var attachments = oracle.GetList<AttachmentInfo>($"BusinessID='{id}' AND StateCode=0 AND AttachmentType='项目成果附件'");
            if (!attachments.Any()) {
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "附件不存在");
            }

            var business = oracle.GetById<BusinessBaseInfo>(id);
            var userCompany = UserInfo?.GetRelateSurveyCompany();
            if (business?.SurveyCompanyNo != userCompany?.CreditCode && business?.CreateUserId != UserInfo?.UserId) {
                return Request.CreateErrorResponse(HttpStatusCode.Unauthorized, "您无下载此附件的权限");
            }
            //下载附件
            try {
                string RootPath = WebConfigurationManager.AppSettings["RootPath"];
                var stream = new FileStream(RootPath + "\\" + attachments.First().AttachmentPath, FileMode.Open);
                HttpResponseMessage response = new HttpResponseMessage(HttpStatusCode.OK);
                response.Content = new StreamContent(stream);
                response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/octet-stream");
                //获取原始文件名称
                var FileName = attachments.First().AttachmentName + attachments.First().AttachmentExt;
                response.Content.Headers.ContentDisposition = new ContentDispositionHeaderValue("attachment") {
                    FileName = FileName
                };
                return response;
            }
            catch (Exception e) {
                return Request.CreateErrorResponse(HttpStatusCode.InternalServerError, e.Message);
            }
        }

        /// <summary>
        /// 获取最新的测绘成果状态
        /// </summary>
        /// <param name="estateProject"></param>
        /// <returns></returns>
        [HttpPost, Route("GetLatestSurveyStates")]
        public ApiResult GetLatestSurveyStates(EstateProjectInfoModel estateProject) {
            if (estateProject == null) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }

            var putLineContent = oracle.GetById<PutLine_ContentInfo>(estateProject.PutLineSurveyID);
            var preSurveyContent = oracle.GetById<PreSurvey_ContentInfo>(estateProject.PreSurveyID);

            return new ApiResult {
                StateCode = 1,
                Data = new {
                    PutLineSurveyCheckState = putLineContent?.DataCheckState,
                    PutLineSurveyRejectMessage = putLineContent?.RejectMessage,
                    PreSurveyCheckState = preSurveyContent?.DataCheckState,
                    PreSurveyRejectMessage = preSurveyContent?.RejectMessage,
                }
            };
        }

        /// <summary>
        /// 下载已完成备案的测绘成果备案凭证
        /// </summary>
        /// <param name="appno"></param>
        /// <returns></returns>
        [HttpGet, Route("DownloadBAZMPDF"), AllowAnonymous]
        public async Task<HttpResponseMessage> DownloadBAZMPDF(string appno = null, string code = null) {
            if (string.IsNullOrWhiteSpace(appno) && string.IsNullOrWhiteSpace(code)) {
                return Request.CreateErrorResponse(HttpStatusCode.NotFound, "参数错误");
            }

            //防sql注入判断
            Regex regex = new Regex(@".*('|-|\+).*");
            if (!string.IsNullOrWhiteSpace(appno)) {
                bool regexResult = regex.IsMatch(appno);
                if (regexResult) {
                    return Request.CreateErrorResponse(HttpStatusCode.NotFound, "无效的参数");
                }
            }

            if (!string.IsNullOrWhiteSpace(code)) {
                bool regexResult = regex.IsMatch(code);
                if (regexResult) {
                    return Request.CreateErrorResponse(HttpStatusCode.NotFound, "无效的参数");
                }
            }

            var client = new HttpClient();
            var result = await client.GetAsync($"{ExternalApiConfig.xCloudApiUrl}/dc/bayx/GetBazmPDFbyBH?xsywh={appno ?? ""}&rid={code ?? ""}");
            return result;
        }

        /// <summary>
        /// 查看已完成备案的测绘成果备案凭证
        /// </summary>
        /// <param name="appno"></param>
        /// <returns></returns>
        [HttpGet, Route("ViewBAZMPDF/不动产测绘成果备案通知"), AllowAnonymous]
        public async Task<HttpResponseMessage> ViewBAZMPDF(string appno = null, string code = null) {
            var response = await DownloadBAZMPDF(appno, code);
            if (response.IsSuccessStatusCode) {
                response.Content.Headers.ContentType.MediaType = "application/pdf";
                response.Content.Headers.ContentDisposition.DispositionType = "inline";
                response.Content.Headers.ContentDisposition.FileName = "不动产测绘成果备案通知.pdf";
            }

            return response;
        }

        /// <summary>
        /// 下载已完成备案的测绘成果备案凭证
        /// </summary>
        /// <param name="appno"></param>
        /// <returns></returns>
        [HttpGet, Route("DownloadSCBAZMPDF"), AllowAnonymous]
        public HttpResponseMessage DownloadSCBAZMPDF(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return Request.CreateErrorResponse(HttpStatusCode.NotFound, "参数错误");
            }

            //防sql注入判断
            Regex regex = new Regex(@".*('|-|\+).*");
            bool regexResult = regex.IsMatch(id);
            if (regexResult) {
                return Request.CreateErrorResponse(HttpStatusCode.NotFound, "无效的参数");
            }

            var businessInfo = oracle.GetById<BusinessBaseInfo>(id);
            if (businessInfo == null 
                || (businessInfo.BusinessClass != nameof(RealEstateActualSurveyFlow)
                    && businessInfo.BusinessClass != nameof(RealEstateActualResultChangeFlow)
                    && businessInfo.BusinessClass != nameof(CouncilPlanCheckFlow)
                    && businessInfo.BusinessClass != nameof(RealEstateOverallActualSurveyFlow)
                    )
                ) {
                return Request.CreateErrorResponse(HttpStatusCode.NotFound, "业务不存在");
            }

            string attachmentName = "不动产实核业务备案信息表";
            if (businessInfo.BusinessClass == nameof(CouncilPlanCheckFlow))
                attachmentName = "市政工程规划条件核实业务备案信息表";
            if (businessInfo.BusinessClass == nameof(RealEstateOverallActualSurveyFlow))
                attachmentName = "规划条件全面核实业务备案信息表";
            var baAttachment =
                oracle.GetList<AttachmentInfo>($"BusinessId=:id And StateCode=0 And AttachmentName='{attachmentName}'",
                    new OracleParameter(":id", OracleDbType.Varchar2) { Value = id }).FirstOrDefault();
            if (baAttachment == null) {
                return Request.CreateErrorResponse(HttpStatusCode.NotFound, "该成果尚未通过实核");
            }

            var stream = baAttachment.ToStream();
            var response = Request.CreateResponse(HttpStatusCode.OK);
            response.Content = new StreamContent(stream);
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/octet-stream");
            //获取原始文件名称
            var FileName = baAttachment.AttachmentName + baAttachment.AttachmentExt;
            response.Content.Headers.ContentDisposition = new ContentDispositionHeaderValue("attachment") {
                FileName = FileName
            };
            return response;
        }

        /// <summary>
        /// 查看已完成备案的测绘成果备案凭证
        /// </summary>
        /// <param name="appno"></param>
        /// <returns></returns>
        [HttpGet, Route("ViewSCBAZMPDF/不动产测绘成果备案通知"), AllowAnonymous]
        public async Task<HttpResponseMessage> ViewSCBAZMPDF(string id) {
            var response = DownloadSCBAZMPDF(id);
            if (response.IsSuccessStatusCode) {
                response.Content.Headers.ContentType.MediaType = "application/pdf";
                response.Content.Headers.ContentDisposition.DispositionType = "inline";
            }
            return response;
        }

        /// <summary>
        /// 根据实测绘的ID获取不动产项目信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("GetEstateProjectInfoByRealActualSurveyId"), AllowAnonymous]
        public async Task<ApiResult> GetEstateProjectInfoByRealActualSurveyId(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }

            //防sql注入判断
            Regex regex = new Regex(@".*('|-|\+).*");
            bool regexResult = regex.IsMatch(id);
            if (regexResult) {
                return new ApiResult {
                    StateCode = 0,
                    Message = "无效的参数"
                };
            }

            var business = oracle.GetById<BusinessBaseInfo>(id);
            if (business == null) {
                return new ApiResult() { StateCode = 0, Message = "在系统中找不到此测绘成果" };
            }

            var estateProjects = oracle.GetList<EstateProjectInfo>("RealSurveyID=:id",
                new OracleParameter(":id", OracleDbType.Varchar2) { Value = id });
            if (estateProjects.Any() == false) {
                //判断此业务已完成，如果已完成，但是在项目管理表里面没有的话，可能是被其它业务覆盖了
                if (business.StateCode == 2) {
                    return new ApiResult() { StateCode = 0, Message = "该测绘成果已发生变化" };
                }
                return new ApiResult() { StateCode = 0, Message = "该测绘成果尚未完成验收" };
            }

            var estateProject = estateProjects.FirstOrDefault(s => s.RealSurveyState == SurveyState.CompleteAudit);
            if (estateProject == null) {
                return new ApiResult() { StateCode = 0, Message = "该测绘成果尚未完成实核" };
            }

            return new ApiResult() { StateCode = 1, Data = estateProject };
        }

        /// <summary>
        /// 根据预测绘的ID获取不动产项目信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("GetEstateProjectInfoByPreSurveyId"), AllowAnonymous]
        public ApiResult GetEstateProjectInfoByPreSurveyId(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }

            //防sql注入判断
            Regex regex = new Regex(@".*('|-|\+).*");
            bool regexResult = regex.IsMatch(id);
            if (regexResult) {
                return new ApiResult {
                    StateCode = 0,
                    Message = "无效的参数"
                };
            }

            var business = oracle.GetById<BusinessBaseInfo>(id);
            if (business == null) {
                return new ApiResult() { StateCode = 0, Message = "在系统中找不到此测绘成果" };
            }

            var estateProjects = oracle.GetList<EstateProjectInfo>("PreSurveyID=:id",
                new OracleParameter(":id", OracleDbType.Varchar2) { Value = id });
            if (estateProjects.Any() == false) {
                //判断此业务已完成，如果已完成，但是在项目管理表里面没有的话，可能是被其它业务覆盖了
                if (business.StateCode == 2) {
                    return new ApiResult() { StateCode = 0, Message = "该测绘成果已发生变化" };
                }
                return new ApiResult() { StateCode = 0, Message = "该测绘成果尚未完成验收" };
            }

            var estateProject = estateProjects.FirstOrDefault(s => s.PreSurveyState == SurveyState.CompleteAudit);
            if (estateProject == null) {
                return new ApiResult() { StateCode = 0, Message = "该测绘成果尚未完成备案" };
            }

            //获取楼盘信息表附件ID

            return new ApiResult() { StateCode = 1, Data = estateProject };
        }

        /// <summary>
        /// 根据业务ID下载楼盘信息表
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("DownloadBuildingInfoPDFByBusinessId"), AllowAnonymous]
        public HttpResponseMessage DownloadBuildingInfoPDFByBusinessId(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return Request.CreateErrorResponse(HttpStatusCode.NotFound, "参数错误");
            }

            //防sql注入判断
            Regex regex = new Regex(@".*('|-|\+).*");
            bool regexResult = regex.IsMatch(id);
            if (regexResult) {
                return Request.CreateErrorResponse(HttpStatusCode.NotFound, "无效的参数");
            }

            var businessInfo = oracle.GetById<BusinessBaseInfo>(id);
            if (businessInfo == null 
                || (businessInfo.BusinessClass != nameof(RealEstateActualSurveyFlow)
                    && businessInfo.BusinessClass != nameof(RealEstatePreSurveyFlow)
                    && businessInfo.BusinessClass != nameof(RealEstateActualResultChangeFlow)
                    && businessInfo.BusinessClass != nameof(RealEstatePreSurveyResultChangeFlow)
                    && businessInfo.BusinessClass != nameof(CouncilPlanCheckFlow)
                    )
                ) {
                return Request.CreateErrorResponse(HttpStatusCode.NotFound, "业务不存在");
            }

            string attachmentName = "楼盘信息表";
            var baAttachment =
                oracle.GetList<AttachmentInfo>($"BusinessId=:id And StateCode=0 And AttachmentName='{attachmentName}'",
                    new OracleParameter(":id", OracleDbType.Varchar2) { Value = id }).FirstOrDefault();
            if (baAttachment == null) {
                return Request.CreateErrorResponse(HttpStatusCode.NotFound, "该成果尚未汇交");
            }

            var stream = baAttachment.ToStream();
            var response = Request.CreateResponse(HttpStatusCode.OK);
            response.Content = new StreamContent(stream);
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/octet-stream");
            //获取原始文件名称
            var FileName = baAttachment.AttachmentName + baAttachment.AttachmentExt;
            response.Content.Headers.ContentDisposition = new ContentDispositionHeaderValue("attachment") {
                FileName = FileName
            };
            return response;
        }

        /// <summary>
        /// 根据业务ID下载竣工规划条件核实信息表
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("DownloadPlanCheckInfoPDFByBusinessId"), AllowAnonymous]
        public async Task<HttpResponseMessage> DownloadPlanCheckInfoPDFByBusinessId(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return Request.CreateErrorResponse(HttpStatusCode.NotFound, "参数错误");
            }

            //防sql注入判断
            Regex regex = new Regex(@".*('|-|\+).*");
            bool regexResult = regex.IsMatch(id);
            if (regexResult) {
                return Request.CreateErrorResponse(HttpStatusCode.NotFound, "无效的参数");
            }

            var businessInfo = oracle.GetById<BusinessBaseInfo>(id);
            if (businessInfo == null 
                || (businessInfo.BusinessClass != nameof(RealEstateActualSurveyFlow)
                    && businessInfo.BusinessClass != nameof(RealEstatePreSurveyFlow)
                    && businessInfo.BusinessClass != nameof(RealEstateActualResultChangeFlow)
                    && businessInfo.BusinessClass != nameof(CouncilPlanCheckFlow)
                    && businessInfo.BusinessClass != nameof(RealEstateOverallActualSurveyFlow)
                    )
                ) {
                return Request.CreateErrorResponse(HttpStatusCode.NotFound, "业务不存在");
            }

            //如果是实核和实核成果变更业务
            if (businessInfo.BusinessClass != nameof(RealEstateActualSurveyFlow)
                || businessInfo.BusinessClass != nameof(RealEstateActualResultChangeFlow)
               ) {
                //判断是否仅实测
                bool isOnlySC = await XCloudService.CheckIsOnlySC(businessInfo.ID, businessInfo.BusinessClass);
                if (isOnlySC) {
                    return Request.CreateErrorResponse(HttpStatusCode.NotFound, "仅实测不支持下载竣工规划条件核实信息表");
                }
            }

            string sqlAttachment = "AttachmentCategories='竣工规划条件核实信息表'";
            if (businessInfo.BusinessClass == nameof(CouncilPlanCheckFlow))
                sqlAttachment = "AttachmentCategories='市政工程规划条件核实成果表'";
            var baAttachment =
                oracle.GetList<AttachmentInfo>($"BusinessId=:id And StateCode=0 And ({sqlAttachment} )",
                    new OracleParameter(":id", OracleDbType.Varchar2) { Value = id }).FirstOrDefault();
            if (baAttachment == null) {
                return Request.CreateErrorResponse(HttpStatusCode.NotFound, "该成果尚未汇交");
            }

            var stream = baAttachment.ToStream();
            var response = Request.CreateResponse(HttpStatusCode.OK);
            response.Content = new StreamContent(stream);
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/octet-stream");
            //获取原始文件名称
            var FileName = baAttachment.AttachmentName + baAttachment.AttachmentExt;
            response.Content.Headers.ContentDisposition = new ContentDispositionHeaderValue("attachment") {
                FileName = FileName
            };
            return response;
        }

        /// <summary>
        /// 根据多测合一业务ID获取不动产测绘类型
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("GetEstateProjectClassBySurveyId"), AllowAnonymous]
        public async Task<ApiResult> GetEstateProjectClassBySurveyId(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }

            //防sql注入判断
            Regex regex = new Regex(@".*('|-|\+).*");
            bool regexResult = regex.IsMatch(id);
            if (regexResult) {
                return new ApiResult {
                    StateCode = 0,
                    Message = "无效的参数"
                };
            }

            var baseInfo = oracle.GetById<BusinessBaseInfo>(id);
            if (baseInfo == null) {
                return new ApiResult() { StateCode = 0, Message = "请输入正确的信息" };
            }

            if (baseInfo.BusinessClass == nameof(RealEstateActualSurveyFlow)) {
                //判断是否仅实测
                bool isOnlySC = await XCloudService.CheckIsOnlySC(baseInfo.ID, baseInfo.BusinessClass);

                //实核业务
                return new ApiResult() {
                    StateCode = 1,
                    Data = new {
                        ClassName = baseInfo.BusinessClass,
                        RealActualSurveyId = baseInfo.ID,
                        isOnlySC
                    }
                };
            }

            if (baseInfo.BusinessClass == nameof(CouncilPlanCheckFlow)) {
                //实核业务
                return new ApiResult() {
                    StateCode = 1,
                    Data = new {
                        ClassName = baseInfo.BusinessClass,
                        RealActualSurveyId = baseInfo.ID
                    }
                };
            }

            if (baseInfo.BusinessClass == nameof(RealEstateOverallActualSurveyFlow)) {
                return new ApiResult() {
                    StateCode = 1,
                    Data = new {
                        ClassName = baseInfo.BusinessClass,
                        RealActualSurveyId = baseInfo.ID
                    }
                };
            }

            switch (baseInfo.BusinessClass) {
                case nameof(RealEstatePreCheckSurveyFlow): {
                    var project = RealEstatePreCheckSurveyProject.GetByBusinessID(id);
                    return new ApiResult() {
                        StateCode = 1,
                        Data = new {
                            ClassName = baseInfo.BusinessClass,
                            PreSurveyId = baseInfo.ID,
                            LPBID = project.Attachments?.FirstOrDefault(a => a.AttachmentName == "楼盘信息表")?.ID
                        }
                    };
                }

                case nameof(RealEstatePreSurveyResultChangeFlow): {
                    var project = RealEstatePreSurveyResultChangeProject.GetByBusinessID(id);
                    return new ApiResult() {
                        StateCode = 1,
                        Data = new {
                            ClassName = baseInfo.BusinessClass,
                            PreSurveyId = baseInfo.ID,
                            LPBID = project.Attachments?.FirstOrDefault(a => a.AttachmentName == "楼盘信息表")?.ID
                        }
                    };
                }

                case nameof(RealEstateActualResultChangeFlow): {
                    var project = RealEstateActualResultChangeProject.GetByBusinessID(id);
                    //判断是否仅实测
                    bool isOnlySC = await XCloudService.CheckIsOnlySC(baseInfo.ID, baseInfo.BusinessClass);
                    return new ApiResult() {
                        StateCode = 1,
                        Data = new {
                            ClassName = baseInfo.BusinessClass,
                            RealActualSurveyId = baseInfo.ID,
                            LPBID = project.Attachments?.FirstOrDefault(a => a.AttachmentName == "楼盘信息表")?.ID,
                            isOnlySC
                        }
                    };
                }

                case nameof(MeasurePutLinePreCheckFlow): {
                    var project = MeasurePutLinePreCheckProject.GetByBusinessID(id);
                    return new ApiResult() {
                        StateCode = 1,
                        Data = new {
                            ClassName = baseInfo.BusinessClass,
                            PreSurveyId = baseInfo.ID,
                            LPBID = project.Attachments?.FirstOrDefault(a => a.AttachmentName == "楼盘信息表")?.ID
                        }
                    };
                }

                case nameof(RealEstatePreCheckSurveyAutoFlow): {
                    var project = RealEstatePreCheckSurveyAutoProject.GetByBusinessID(id);
                    return new ApiResult() {
                        StateCode = 1,
                        Data = new {
                            ClassName = baseInfo.BusinessClass,
                            PreSurveyId = baseInfo.ID,
                            LPBID = project.Attachments?.FirstOrDefault(a => a.AttachmentName == "楼盘信息表")?.ID
                        }
                    };
                }

                case nameof(CouncilMeasurePutLinePreCheckFlow): {
                    var project = CouncilMeasurePutLinePreCheckProject.GetByBusinessID(id);
                    return new ApiResult() {
                        StateCode = 1,
                        Data = new {
                            ClassName = baseInfo.BusinessClass,
                            PreSurveyId = baseInfo.ID,
                            LPBID = project.Attachments?.FirstOrDefault(a => a.AttachmentName == "楼盘信息表")?.ID
                        }
                    };
                }
            }

            return new ApiResult() { StateCode = 0, Message = "请输入正确的信息" };
        }

        /// <summary>
        /// 查看已完成备案的测绘成果备案凭证
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("ViewBAZMPDF/不动产测绘成果备案信息表"), AllowAnonymous]
        public HttpResponseMessage ViewBAXXBPDF(string id) {
            //防sql注入判断
            Regex regex = new Regex(@".*('|-|\+).*");
            bool regexResult = regex.IsMatch(id);
            if (regexResult) {
                return Request.CreateErrorResponse(HttpStatusCode.NotFound, "无效的参数");
            }

            var businessBaseInfo = oracle.GetById<BusinessBaseInfo>(id);
            BaseProject project = null;
            if (businessBaseInfo.BusinessClass == nameof(RealEstatePreSurveyResultChangeFlow)) {
                project = RealEstatePreSurveyResultChangeProject.GetByBusinessID(id);
            }
            else {
                project = RealEstatePreCheckSurveyProject.GetByBusinessID(id);
            }
             
            var attachment = project?.Attachments?.FirstOrDefault(a => a.AttachmentName == "不动产预核业务备案信息表");
            if (attachment == null) {
                attachment = project?.Attachments?.FirstOrDefault(a => a.AttachmentName == "规划验线备案信息表");
                if (attachment == null)
                    return Request.CreateErrorResponse(HttpStatusCode.NotFound, "文件不存在");
            }
            //下载附件
            try {
                string RootPath = ExternalApiConfig.RootPath;
                var stream = new FileStream(RootPath + "\\" + attachment.AttachmentPath, FileMode.Open);
                HttpResponseMessage response = new HttpResponseMessage(HttpStatusCode.OK);
                response.Content = new StreamContent(stream);
                response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/pdf");
                response.Content.Headers.ContentDisposition = new ContentDispositionHeaderValue("attachment") {
                    FileName = $"{attachment.AttachmentName}.pdf",
                    DispositionType = "inline"
                };
                return response;
            } catch (Exception e) {
                return Request.CreateErrorResponse(HttpStatusCode.InternalServerError, e.Message);
            }
        }

        /// <summary>
        /// 根据业务ID下载竣工红线图
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("DownloadJGHXTPDFByBusinessId"), AllowAnonymous]
        public async Task<HttpResponseMessage> DownloadJGHXTPDFByBusinessId(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return Request.CreateErrorResponse(HttpStatusCode.NotFound, "参数错误");
            }

            //防sql注入判断
            Regex regex = new Regex(@".*('|-|\+).*");
            bool regexResult = regex.IsMatch(id);
            if (regexResult) {
                return Request.CreateErrorResponse(HttpStatusCode.NotFound, "无效的参数");
            }

            var businessInfo = oracle.GetById<BusinessBaseInfo>(id);
            if (businessInfo == null
                || (businessInfo.BusinessClass != nameof(CouncilPlanCheckFlow)
                    )
                ) {
                return Request.CreateErrorResponse(HttpStatusCode.NotFound, "业务不存在");
            }

            if (businessInfo.StateCode != 2) {
                return Request.CreateErrorResponse(HttpStatusCode.NotFound, "该成果尚未汇交");
            }

            //获取市政竣工红线图
            var fileInfo = await XCloudService.GetJGHXT(id);
            if (fileInfo == null) {
                return Request.CreateErrorResponse(HttpStatusCode.NotFound, "竣工红线图不存在");
            }

            var response = Request.CreateResponse(HttpStatusCode.OK);
            response.Content = new StreamContent(fileInfo.Item1);
            response.Content.Headers.ContentType = new MediaTypeHeaderValue(fileInfo.Item3);
            //获取原始文件名称
            string fileName = "竣工红线图";
            string fileExtName = string.Empty;
            if (fileInfo.Item2.Contains(".")) {
                var arr = fileInfo.Item2.Split('.');
                fileExtName = $".{arr[arr.Length - 1]}";
            }

            var FileName = $"{fileName}{fileExtName}";
            response.Content.Headers.ContentDisposition = new ContentDispositionHeaderValue("attachment") {
                FileName = FileName
            };
            return response;
        }
    }
}

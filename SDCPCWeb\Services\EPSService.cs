﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using Hangfire;
using Hangfire.Console;
using Hangfire.Server;
using Newtonsoft.Json.Linq;
using SDCPCWeb.Controllers;
using SDCPCWeb.Models.BusinessFlow;
using SDCPCWeb.Models.System;

namespace SDCPCWeb.Services {
    /// <summary>
    /// EPS服务类 
    /// </summary>
    public class EPSService {
        /// <summary>
        /// 通知EPS创建业务
        /// </summary>
        /// <param name="id">业务id</param>
        /// <param name="className">业务流程名称</param>
        /// <param name="console"></param>
        /// <returns></returns>
        [DisplayName("通知EPS创建业务"), AutomaticRetry(Attempts = 0)]
        public static async Task ToEPSAutoCreateBusiness(string id, string className, PerformContext console) {
            var servicesUrl = ExternalApiConfig.EPSSDCServices + "/RunEpsInterface";
            string par = $"ID={id}&strYWMC={HttpUtility.UrlEncode("自动落宗")}&strDownloadLayer=&strByte64=&strFileType=mdb";
            string url = $"{servicesUrl}?{par}";
            console?.WriteLine(url);

            var httpClient = HttpClientFactory.Create();
            var responseMessage = await httpClient.GetAsync(url);
            if (responseMessage.IsSuccessStatusCode) {
                var responseText = await responseMessage.Content.ReadAsStringAsync();
                console?.WriteLine(responseText);

                System.Xml.XmlDocument xml = new System.Xml.XmlDocument();
                xml.LoadXml(responseText);
                JObject result = JObject.Parse(xml.InnerText);
                if (result["status"]?.ToString() != "success") {
                    throw new Exception(result["status"]?.ToString());
                } else {
                    string fileId = result["FILEID"]?.ToString();
                    if (string.IsNullOrWhiteSpace(fileId)) {
                        throw new Exception("返回的FILEID为空");
                    }

                    //如果通知EPS创建业务成功，就进入检查EPS创建业务状态
                    BackgroundJob.Enqueue(() => CheckEPSAutoCreateBusinessStatus(fileId, id, className, null));
                }
            } else {
                throw new Exception(responseMessage.ReasonPhrase);
            }
        }

        /// <summary>
        /// 检查EPS创建业务状态
        /// </summary>
        /// <param name="id"></param>
        /// <param name="className">业务流程名称</param>
        /// <param name="console"></param>
        /// <returns></returns>
        [DisplayName("检查EPS创建业务状态"), AutomaticRetry(Attempts = 0)]
        public static async Task CheckEPSAutoCreateBusinessStatus(string fileId, string id, string className, PerformContext console) {
            console?.WriteLine("开始检查EPS创建业务状态");

            DateTime lastTime = DateTime.MinValue;
            int times = 0;

            //检查20次，每一分钟检查一次
            while (times < 20) {
                if (lastTime.AddMinutes(1) <= DateTime.Now) {
                    times++;
                    lastTime = DateTime.Now;

                    //如果入库成功
                    if (await CheckEPSAutoCreateBusinessStatus(fileId, console) == 1) {
                        console?.WriteLine("完成检查EPS创建业务状态");

                        switch (className) {
                            case nameof(RealEstatePreCheckSurveyAutoFlow): {
                                Hangfire.BackgroundJob.Enqueue(() =>
                                    EstateProjectAuditController.BackgroudCompletePreCheckSurveyAutoAudit(id, false, null));
                                break;
                            }

                            default: {
                                throw new Exception("不支持的业务流程");
                            }
                        }
                        return;
                    }
                    else {
                        console?.WriteLine("检查入库状态未完成");
                    }
                } else {
                    Thread.Sleep(3000);
                }
            }

            throw new Exception("检查EPS创建业务状态超时");
        }

        /// <summary>
        /// 根据宗地号获取利权人名称和证件号
        /// </summary>
        /// <param name="zdCode"></param>
        /// <returns>Item1：返回消息（空为成功，非空为错误消息）；Item2：权利人名称和证件号数组</returns>
        public static async Task<Tuple<string, JArray>> GetZDQLR(string zdCode) {
            string url = $"{ExternalApiConfig.EPSSDCServices}/GetQLRInfo?strzddm={zdCode}";

            var httpClient = HttpClientFactory.Create();
            var responseMessage = await httpClient.GetAsync(url);
            if (responseMessage.IsSuccessStatusCode) {
                var responseText = await responseMessage.Content.ReadAsStringAsync();

                System.Xml.XmlDocument xml = new System.Xml.XmlDocument();
                xml.LoadXml(responseText);
                JObject result = JObject.Parse(xml.InnerText);
                if (result["status"]?.ToString() != "success") {
                   return new Tuple<string, JArray>("根据宗地号获取权利人信息失败", null);
                } else {
                    var data = JArray.FromObject(result["data"]);
                    return new Tuple<string, JArray>(null, data);
                }
            } else {
                return new Tuple<string, JArray>($"根据宗地号获取权利人信息接口返回状态失败：{(int)responseMessage.StatusCode}", null);
            }
        }


        /// <summary>
        /// 检查EPS入库状态（待实现功能）
        /// </summary>
        /// <param name="id"></param>
        /// <returns>返回1成功；其它未完成</returns>
        private static async Task<int> CheckEPSAutoCreateBusinessStatus(string fileId, PerformContext console) {
            console?.WriteLine("开始检查EPS入库状态");

//#warning 测试用，记得修改。暂时先返回1
            //return 1;

            //调用EPS状态接口 MDBSTATUS 的值
            string url = $"{ExternalApiConfig.EPSSDCServices}/GetTableStrInterface?strFileGuid={fileId}";
            console?.WriteLine(url);

            var httpClient = HttpClientFactory.Create();
            var responseMessage = await httpClient.GetAsync(url);
            if (responseMessage.IsSuccessStatusCode) {
                var responseText = await responseMessage.Content.ReadAsStringAsync();
                console?.WriteLine(responseText);

                System.Xml.XmlDocument xml = new System.Xml.XmlDocument();
                xml.LoadXml(responseText);
                JObject result = JObject.Parse(xml.InnerText);
                if (result["status"]?.ToString() != "success") {
                    throw new Exception(result["status"]?.ToString());
                } else {
                    if (result["MDBSTATUS"]?.ToString() == "落宗成功") {
                        return 1;
                    }
                }
            } else {
                throw new Exception(responseMessage.ReasonPhrase);
            }

            return 0;
        }


    }
}
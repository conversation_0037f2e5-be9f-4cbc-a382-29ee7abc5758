﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Web;
using log4net;
using Newtonsoft.Json;
using SDCPCWeb.Models;
using SDCPCWeb.Models.System;

namespace SDCPCWeb.Services {
    /// <summary>
    /// 
    /// </summary>
    public static class LogService {
        private static ILog logger = LogManager.GetLogger("LogService");
        /// <summary>
        /// 
        /// </summary>
        /// <param name="message"></param>
        public static void Debug(string message) {
            logger.Debug(message);
        }

        /// <summary>
        /// 记录操作日志
        /// </summary>
        public static void WriteLogs(HttpRequestMessage request, string operationType, string operationContent, HttpRequestInfoHelper reqInfoHelper, string remark = null) {
            UserInfo user = null;
            if (request?.Properties.ContainsKey("SDC-UserInfo") == true) {
                user = request.Properties["SDC-UserInfo"] as UserInfo;
            }
            var personName = user?.PersonName;
            if (user != null) {
                personName = $"[{user.From}]{user.PersonName}";
            }

            SystemLogs log = new SystemLogs {
                ID = Guid.NewGuid().ToString("N"),
                UserName = personName,
                UserNo = user?.PersonNo,
                OperationTime = DateTime.Now,
                OperationType = operationType,
                OperationContent = operationContent,
                OperationIp =  reqInfoHelper.GetIP(),
                Remark = remark
            };

            using (OracleDataService service = new OracleDataService()) {
                service.InsertOne(log);
            }
        }

        /// <summary>
        /// 记录删除日志
        /// </summary>
        public static void WriteDeleteLog<T>(T deletedEntity, string deleteUser = null) {
            var deleteLog = new DeleteLog();
            deleteLog.ID = Guid.NewGuid().ToString("N");
            deleteLog.CreateTime = DateTime.Now;
            deleteLog.DeleteUser = deleteUser;
            deleteLog.DeleteTableName = typeof(T).Name;
            deleteLog.RecordContent = JsonConvert.SerializeObject(deletedEntity, SystemConfig.JsonDateTimeConverter);

            using (OracleDataService service = new OracleDataService()) {
                service.InsertOne(deleteLog);
            }
        }
    }
}
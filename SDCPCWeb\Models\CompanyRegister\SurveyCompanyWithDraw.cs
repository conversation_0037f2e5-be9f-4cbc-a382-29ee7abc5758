﻿using System;
using Newtonsoft.Json;
using SDCPCWeb.Services;

namespace SDCPCWeb.Models.CompanyRegister {
    /// <summary>
    /// 测绘单位注销
    /// </summary>
    public class SurveyCompanyWithDrawModel {
        public string ID { get; set; }
        public DateTime CreateTime { get; set; }
        public string CreateUserId { get; set; }
        public string CreatePersonName { get; set; }
        public string CompanyId { get; set; }
        public string CompanyName { get; set; }
        public int Reason { get; set; }
        public string OtherReason { get; set; }

        /// <summary>
        /// 状态：1已提交，2审核中，3审核通过，4审核不通过
        /// </summary>
        public int StateCode { get; set; }

        /// <summary>
        /// 是否是云平台主动发起注销：1：是，其它：否
        /// </summary>
        public int? IsFromXCloud { get; set; }

        /// <summary>
        /// 云平台业务id
        /// </summary>
        public string XCloudBusinessId { get; set; }

        /// <summary>
        /// 注销时的单位信息
        /// </summary>
        public string CompanyInfo { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class SurveyCompanyWithDraw : SurveyCompanyWithDrawModel, IOracleDataTable {
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public SurveyCompanyWithDrawModel ToModel() {
            return JsonConvert.DeserializeObject<SurveyCompanyWithDrawModel>(JsonConvert.SerializeObject(this));
        }
    }
}
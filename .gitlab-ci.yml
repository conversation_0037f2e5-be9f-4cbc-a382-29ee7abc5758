variables:
  NUGET_PATH: 'C:\gitlab_runner\nuget.exe'
  MSBUILD_PATH: 'C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\msbuild.exe'
  v: 'dev'

stages:
  - deploy

cache:
  paths:
    - packages/

publish_release:
  stage: deploy
  script: 
    - chcp 65001
    - '& "$env:NUGET_PATH" restore'
    - '& "$env:MSBUILD_PATH" .\SDCPCWeb\SDCPCWeb.csproj /p:DeployOnBuild=True /p:PublishProfile=sdcapi_inner.pubxml /p:AllowUntrustedCertificate=True /p:password=!Q2w#E4r%T6y /p:Configuration=Release /p:Platform=AnyCPU /p:WarningLevel=0'
  only:
    refs:
      - web
    variables:
      - '$v=="release"'
  tags:
    - inner-yunwei

publish_dev:
  stage: deploy
  script: 
    - chcp 65001
    - '& "$env:NUGET_PATH" restore'
    - '& "$env:MSBUILD_PATH" .\SDCPCWeb\SDCPCWeb.csproj /p:DeployOnBuild=True /p:PublishProfile=sdcapi_practice_inner.pubxml /p:AllowUntrustedCertificate=True /p:password=1qazXSW@3edc /p:Configuration=Debug /p:Platform=AnyCPU /p:WarningLevel=0'
  only:
    refs:
    - web
    variables:
    - '$v=="dev"'
  tags:
    - inner-yunwei

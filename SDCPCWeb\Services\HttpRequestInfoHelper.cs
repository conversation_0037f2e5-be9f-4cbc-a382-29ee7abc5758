﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Web;

namespace SDCPCWeb.Services {
    /// <summary>
    /// 
    /// </summary>
    public class HttpRequestInfoHelper {
        HttpRequestBase request;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="httpRequest"></param>
        public HttpRequestInfoHelper(HttpRequestBase httpRequest) {
            this.request = httpRequest;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public HttpRequestBase GetRequest() {
            return request;
        }
        /// <summary>
        /// 取得客户端真实IP，如果有代理则取第一个非内网地址  
        /// </summary>
        /// <returns></returns>
        public string GetIP() {
            if (request == null) {
                return "0.0.0.0";
            }
            var result = request.ServerVariables["HTTP_X_FORWARDED_FOR"];
            if (!string.IsNullOrWhiteSpace(result)) {
                //可能有代理  
                if (!result.Contains("."))        //没有“.”肯定是非IPv4格式  
                    result = null;
                else {
                    if (result.Contains(",")) {
                        //有“,”，估计多个代理。取第一个不是内网的IP。  
                        result = result.Replace("  ", "").Replace("'", "");
                        string[] temparyip = result.Split(",;".ToCharArray(), StringSplitOptions.RemoveEmptyEntries);
                        foreach (var ip in temparyip) {
                            if (IsIP(ip.Trim())
                                && !ip.Trim().StartsWith("10.")
                                && !ip.Trim().StartsWith("192.168.")
                                && !ip.Trim().StartsWith("172.16.")
                                ) {
                                return ip.Trim();        //找到不是内网的地址  
                            }
                        }
                    }
                    else if (IsIP(result)) {
                        //代理即是IP格式  
                        return result;
                    }
                    else {
                        //代理中的内容  非IP，取IP 
                        result = null;
                    }
                }
            }
            //string IpAddress = (request.ServerVariables["HTTP_X_FORWARDED_FOR"] != null && request.ServerVariables["HTTP_X_FORWARDED_FOR"] != String.Empty) ? request.ServerVariables["HTTP_X_FORWARDED_FOR"] : request.ServerVariables["HTTP_X_REAL_IP"];



            if (string.IsNullOrWhiteSpace(result))
                result = request.ServerVariables["HTTP_X_REAL_IP"];

            if (string.IsNullOrWhiteSpace(result))
                result = request.UserHostAddress;

            return result;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="ip"></param>
        /// <returns></returns>
        public bool IsIP(string ip) {
            return System.Text.RegularExpressions.Regex.IsMatch(ip, @"^((2[0-4]\d|25[0-5]|[01]?\d\d?)\.){3}(2[0-4]\d|25[0-5]|[01]?\d\d?)$");
        }

    }
}
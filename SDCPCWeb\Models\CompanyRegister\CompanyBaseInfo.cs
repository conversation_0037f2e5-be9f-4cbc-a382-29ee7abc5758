﻿using Newtonsoft.Json;
using SDCPCWeb.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace SDCPCWeb.Models.CompanyRegister {
    /// <summary>
    /// 单位基本信息
    /// </summary>
    public class CompanyBaseInfoModel {
        /// <summary>
        /// ID
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 关联已通过的申请ID，或者变更ID
        /// </summary>
        public string RelationRequestId { get; set; }
        /// <summary>
        /// 单位类型
        /// </summary>
        public string CompanyType { get; set; }
        /// <summary>
        /// 单位名称
        /// </summary>
        public string CompanyName { get; set; }
        /// <summary>
        /// 统一社会信用代码
        /// </summary>
        public string CreditCode { get; set; }
        /// <summary>
        /// 法定代表人姓名
        /// </summary>
        public string LegalPersonName { get; set; }
        /// <summary>
        /// 法定代表人身份证号
        /// </summary>
        public string LegalPersonNumber { get; set; }
        /// <summary>
        /// 单位地址
        /// </summary>
        public string CompanyAddress { get; set; }
        /// <summary>
        /// 联系人
        /// </summary>
        public string Contacter { get; set; }
        /// <summary>
        /// 联系电话
        /// </summary>
        public string ContacterPhone { get; set; }

    }

    public class CompanyBaseInfo : CompanyBaseInfoModel, IOracleDataTable {
        public CompanyBaseInfoModel ToModel() {
            return JsonConvert.DeserializeObject<CompanyBaseInfoModel>(JsonConvert.SerializeObject(this));
        }
    }
}
﻿using SDCPCWeb.Models;
using SDCPCWeb.Models.Attachment;
using SDCPCWeb.Models.BusinessFlow;
using SDCPCWeb.Models.Result;
using SDCPCWeb.Services;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Configuration;
using System.Web.Http;
using SDCPCWeb.Extensions;
using SDCPCWeb.Models.Mongo;
using SDCPCWeb.Models.System;

namespace SDCPCWeb.Controllers {
    /// <summary>
    /// 附件管理
    /// </summary>
    [RoutePrefix("sdcapi/AttachmentManage")]
    [SDCAuthorize(SDCAuthorizeRole.BDCWeb, SDCAuthorizeRole.xCloud)]
    public class AttachmentManageController : ApiController {
        private readonly OracleDataService service = new OracleDataService();
        private UserInfo UserInfo => Request.Properties.ContainsKey("SDC-UserInfo") ? (UserInfo)Request.Properties["SDC-UserInfo"] : null;
        /// <summary>
        /// 创建附件
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost, Route("CreateAttachmentRequest")]
        public ApiResult CreateNewAttachmentRequest(AttachmentInfo request) {
            if (request == null) {
                return new ApiResult { StateCode = 0, Message = "创建失败，请输入正确信息" };
            }
            //传入参数判断
            //必填值不能为空
            if (string.IsNullOrWhiteSpace(request.BusinessType)) {
                return new ApiResult { StateCode = 0, Message = "附件关联类型不能为空" };
            }
            if (string.IsNullOrWhiteSpace(request.BusinessID)) {
                return new ApiResult { StateCode = 0, Message = "附件关联ID不能为空" };
            }
            if (request.AttachmentType != "申请材料附件" && request.AttachmentType != "项目成果附件") {
                return new ApiResult { StateCode = 0, Message = "附件类型只能为【申请材料附件】或【项目成果附件】" };
            }
            if (string.IsNullOrWhiteSpace(request.AttachmentCategories)) {
                return new ApiResult { StateCode = 0, Message = "附件类别不能为空" };
            }
            //赋初始值
            request.ID = Guid.NewGuid().ToString("N");
            request.AttachmentName = null;
            request.AttachmentExt = null;
            request.UploadTime = null;
            request.AttachmentPath = null;
            request.AttachmentThumbPath = null;
            request.AttachmentLength = 0;
            request.StateCode = 0;
            //request.LiftTime = DateTime.Now;
            //request.ExpirationTime = DateTime.Now.AddYears(10);//获取数据时不支持时间字段为空，默认十年后才过期
            try {
                service.InsertOne(request);
                LogService.WriteLogs(Request, "附件管理", $"创建附件 >> 附件ID：{request.ID} >> 业务ID：{request.BusinessID}", new HttpRequestInfoHelper(((HttpContextBase)Request.Properties["MS_HttpContext"]).Request));
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 0, Message = "创建失败，错误信息：" + e.Message + "" };
            }
            return new ApiResult { StateCode = 1, Data = request.ID, Message = "创建成功" };
        }
        /// <summary>
        /// 附件上传（单个文件）
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost, Route("AttachmentUploadRequest")]
        public ApiResult AttachmentUploadRequest(string id) {
            if (id == null) {
                return new ApiResult { StateCode = 0, Message = "上传失败，请输入正确的信息" };
            }
            //ID必须存在对应的记录
            var record = service.GetById<AttachmentInfo>(id);
            if (record == null) {
                return new ApiResult { StateCode = 0, Message = "上传失败，无法找到附件信息" };
            }
            HttpFileCollection files = System.Web.HttpContext.Current.Request.Files;
            //判断是否有文件上传
            if (files.Count == 0) {
                return new ApiResult { StateCode = 0, Message = "上传失败，请选择要上传的文件" };
            }
            try {
                //附件存储根路径
                string RootPath = WebConfigurationManager.AppSettings["RootPath"];
                string Year = DateTime.Now.Year.ToString();
                string Month = DateTime.Now.Month.ToString();
                string Day = DateTime.Now.Day.ToString();
                //文件存储路径
                string SavePath = Year + "\\" + Month + "\\" + Day + "\\" + record.BusinessID;
                //路径不存在时，自动创建
                if (!Directory.Exists(RootPath + "\\" + SavePath)) {
                    Directory.CreateDirectory(RootPath + "\\" + SavePath);
                }
                SavePath = SavePath + "\\" + id;
                //文件原始后缀名
                string OldFileEextension = Path.GetExtension(files[0].FileName);
                //文件名称
                string Fname = Path.GetFileNameWithoutExtension(files[0].FileName);
                //保存文件
                files[0].SaveAs(RootPath + "\\" + SavePath);
                //判断上传内容是否为图片
                string[] ImageTypes = new string[] { ".JPG", ".JPEG", ".PNG", ".GIF", ".BMP" };
                string SLSavePath = "";
                if (Array.IndexOf(ImageTypes, OldFileEextension.ToUpper()) != -1) {
                    SLSavePath = SavePath + ".thumb";
                    long quality = 75;   //图片质量,优选75质量，存储比较小
                    SendSmallImage(RootPath + "\\" + SavePath, RootPath + "\\" + SLSavePath, 300, 300, quality, "CUT");
                }
                //修改文件后缀名，防止部分文件被直接执行 暂时禁用 
                // Path.ChangeExtension(YSSavePath, ".bak");
                //更新附件信息
                record.AttachmentName = Fname;
                record.AttachmentExt = OldFileEextension;
                record.UploadTime = DateTime.Now;
                record.AttachmentPath = SavePath;
                record.AttachmentThumbPath = SLSavePath;
                record.AttachmentLength = files[0].ContentLength;
                service.UpdateOne(record);
                LogService.WriteLogs(Request, "附件管理", $"上传附件 >> 附件ID：{record.ID} >> 附件名称：{record.AttachmentName}", new HttpRequestInfoHelper(((HttpContextBase)Request.Properties["MS_HttpContext"]).Request));
                dynamic ResultInfo = new {
                    ID = id,
                    AttachmentName = Fname,
                    AttachmentExt = OldFileEextension,
                    AttachmentLength = files[0].ContentLength,
                    AttachmentType = record.AttachmentType,
                    AttachmentCategories = record.AttachmentCategories
                };
                return new ApiResult { StateCode = 1, Message = "上传成功", Data = ResultInfo };
            }
            catch (Exception e) {
                LogService.WriteLogs(Request, "附件管理", $"上传附件异常 >> {e.GetStackTraces()}", new HttpRequestInfoHelper(((HttpContextBase)Request.Properties["MS_HttpContext"]).Request));
                return new ApiResult { StateCode = 0, Message = "上传失败，错误信息：" + e.Message + "" };
            }
        }
        /// <summary>
        /// 根据id查看图片
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("ShowImage")]
        public HttpResponseMessage ShowImage(string id) {
            if (id == null) {
                return new HttpResponseMessage(HttpStatusCode.BadRequest);
            }
            //ID必须存在对应的记录
            var record = service.GetById<AttachmentInfo>(id);
            if (record == null) {
                return new HttpResponseMessage(HttpStatusCode.NotFound);
            }
            try {
                string RootPath = WebConfigurationManager.AppSettings["RootPath"];
                var imgStream = new MemoryStream(File.ReadAllBytes(RootPath + "\\" + record.AttachmentPath));
                var resp = new HttpResponseMessage(HttpStatusCode.OK) {
                    Content = new StreamContent(imgStream)
                };
                resp.Content.Headers.ContentType = new MediaTypeHeaderValue("image/jpg");
                return resp;
            }
            catch (Exception e) {
                return Request.CreateErrorResponse(HttpStatusCode.InternalServerError, e.Message);
            }
        }
        /// <summary>
        /// 根据id查看图片缩略图
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("ShowThumbnail")]
        public HttpResponseMessage ShowThumbnail(string id) {
            if (id == null) {
                return new HttpResponseMessage(HttpStatusCode.BadRequest);
            }
            //ID必须存在对应的记录
            var record = service.GetById<AttachmentInfo>(id);
            if (record == null) {
                return new HttpResponseMessage(HttpStatusCode.NotFound);
            }
            try {
                string RootPath = WebConfigurationManager.AppSettings["RootPath"];
                var imgStream = new MemoryStream(File.ReadAllBytes(RootPath + "\\" + record.AttachmentThumbPath));
                var resp = new HttpResponseMessage(HttpStatusCode.OK) {
                    Content = new StreamContent(imgStream)
                };
                resp.Content.Headers.ContentType = new MediaTypeHeaderValue("image/jpg");
                return resp;
            }
            catch (Exception e) {
                return Request.CreateErrorResponse(HttpStatusCode.InternalServerError, e.Message);
            }
        }
        /// <summary>
        /// 根据id预览PDF
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("ShowPDF")]
        public HttpResponseMessage ShowPDF(string id) {
            if (id == null) {
                return new HttpResponseMessage(HttpStatusCode.BadRequest);
            }
            //ID必须存在对应的记录
            var record = service.GetById<AttachmentInfo>(id);
            if (record == null) {
                return new HttpResponseMessage(HttpStatusCode.NotFound);
            }
            try {
                string RootPath = WebConfigurationManager.AppSettings["RootPath"];
                var stream = new FileStream(RootPath + "\\" + record.AttachmentPath, FileMode.Open, FileAccess.Read, FileShare.Read);
                HttpResponseMessage response = new HttpResponseMessage(HttpStatusCode.OK);
                response.Content = new StreamContent(stream);
                response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/pdf");
                return response;
            }
            catch (Exception e) {
                return Request.CreateErrorResponse(HttpStatusCode.InternalServerError, e.Message);
            }
        }
        /// <summary>
        /// 附件下载
        /// </summary>
        /// <param name="id"></param>
        /// <param name="canDownloadDeleted">是否允许下载已删除的</param>
        /// <returns></returns>
        [HttpGet, Route("AttachmentDownloadRequest")]
        public HttpResponseMessage AttachmentDownloadRequest(string id, bool? canDownloadDeleted = null) {
            if (id == null) {
                return Request.CreateErrorResponse(HttpStatusCode.NotFound, "该数据不存在，无法提供下载");
            }
            //ID必须存在对应的记录
            var record = service.GetById<AttachmentInfo>(id);
            if (record == null) {
                return Request.CreateErrorResponse(HttpStatusCode.NotFound, "该数据不存在，无法提供下载");
            }
            
            if (canDownloadDeleted != true) {
                //新增：当项目附件被逻辑删除时 按404处理
                if (record.StateCode == 1) {
                    return Request.CreateErrorResponse(HttpStatusCode.NotFound, "该数据不存在，无法提供下载");
                }
            }

            if (!string.IsNullOrWhiteSpace(record.ValidateNumber)) {
                //有验证码，则提示无法下载
                return Request.CreateErrorResponse(HttpStatusCode.NotFound, "该数据存在验证码，请使用正确的方法下载");
            }
            try {
                string RootPath = WebConfigurationManager.AppSettings["RootPath"];
                using (var stream = new FileStream(RootPath + "\\" + record.AttachmentPath, FileMode.Open, FileAccess.Read, FileShare.Read)) {

                    byte[] bytes = new byte[stream.Length];
                    stream.Read(bytes, 0, bytes.Length);
                    MemoryStream memoryStream = new MemoryStream(bytes);
                    memoryStream.Position = 0;
                    HttpResponseMessage response = new HttpResponseMessage(HttpStatusCode.OK);
                    response.Content = new StreamContent(memoryStream);
                    response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/octet-stream");
                    //获取原始文件名称
                    var FileName = record.AttachmentName + record.AttachmentExt;
                    response.Content.Headers.ContentDisposition = new ContentDispositionHeaderValue("attachment") {
                        FileName = FileName
                    };
                    return response;
                }
            }
            catch (Exception e) {
                return Request.CreateErrorResponse(HttpStatusCode.InternalServerError, e.Message);
            }
        }

        /// <summary>
        /// 获取附件下载Key，使用Key可以调用匿名下载接口，一个Key的有效期是5分钟
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("GetAttachmentDownloadKey")]
        public ApiResult GetAttachmentDownloadKey(string id) {
            var key = Guid.NewGuid().ToString("N");
            MongoCache.Set("AttachmentDownload" + key, id, TimeSpan.FromMinutes(5));
            return new ApiResult() { StateCode = 1, Data = key };
        }


        [HttpGet, Route("DownloadAttachmentWithKey/{key}/{filename}.{ext}")]
        [AllowAnonymous]
        public HttpResponseMessage DownloadAttachmentWithKey(string key, string filename = null, string ext = null) {
            var attachmentId = MongoCache.Get("AttachmentDownload" + key);
            if (string.IsNullOrWhiteSpace(attachmentId)) {
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "信息不存在或已过期");
            }
            //MongoCache.Clear("AttachmentDownload" + key);
            var response = AttachmentDownloadRequest(attachmentId);
            if (response.IsSuccessStatusCode && !string.IsNullOrWhiteSpace(filename) && !string.IsNullOrWhiteSpace(ext)) {
                response.Content.Headers.ContentDisposition = null;
            }

            return response;
        }



        /// <summary>
        /// 基础数据下载前验证
        /// </summary>
        /// <param name="id"></param>
        /// <param name="validateNumber"></param>
        /// <returns></returns>
        [HttpGet, Route("GetBaseDataDownloadState")]
        public ApiResult GetBaseDataDownloadState(string id, string validateNumber) {
            if (id == null) {
                return new ApiResult { StateCode = 0, Data = 0, Message = "该数据不存在，无法提供下载" };
            }
            //ID必须存在对应的记录
            var record = service.GetById<AttachmentInfo>(id);
            if (record == null) {
                return new ApiResult { StateCode = 0, Data = 0, Message = "该数据不存在，无法提供下载" };
            }
            if (record.StateCode == 1) {
                return new ApiResult { StateCode = 0, Data = 0, Message = "该数据不存在，无法提供下载" };
            }
            if (record.ExpirationTime != null && DateTime.Compare(DateTime.Now, record.ExpirationTime.Value) > 0) {
                return new ApiResult { StateCode = 0, Data = 3, Message = "数据已过期被清理，请重新申请" };
            }
            if (record.LiftTime != null && DateTime.Compare(record.LiftTime.Value, DateTime.Now) > 0) {
                var liftTime = record.LiftTime.Value;
                return new ApiResult { StateCode = 0, Data = 4, Message = $"该数据已被锁定，暂时无法提供下载，请在{liftTime:yyyy-MM-dd HH:mm:ss}后重新下载" };
            }
            var key = "ValidateNumberError_" + record.ValidateNumber;
            if (record.ValidateNumber != validateNumber) {
                //记录错误次数到缓存
                var times = 0;
                var timesString = MongoCache.Get(key);
                if (timesString != null) {
                    times = int.Parse(timesString);
                }

                if (++times >= 3) {
                    //已经错误3次，锁定
                    AttachmentLockRequest(record.ID);
                    MongoCache.Clear(key);
                    return new ApiResult { StateCode = 0, Data = 2, Message = "您输入错误次数太多，数据已被锁定" };
                }
                MongoCache.Set(key, times.ToString(), TimeSpan.FromMinutes(10));
                return new ApiResult { StateCode = 0, Data = 2, Message = "提取码错误，请填写正确的提取码" };
            }
            else {
                MongoCache.Clear(key);
            }
            return new ApiResult { StateCode = 1, Data = 1, Message = "可以下载" };
        }

        /// <summary>
        /// 获取随机码
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("GetValidateNumber")]
        public async Task<ApiResult> GetValidateNumber(string id) {
            if (UserInfo.From != "BDCWeb") {
                return new ApiResult { StateCode = 0, Message = "您没有调用此接口的权限" };
            }
            if (id == null) {
                return new ApiResult { StateCode = 0, Message = "该数据不存在" };
            }
            //ID必须存在对应的记录
            var record = service.GetById<AttachmentInfo>(id);
            if (record == null) {
                return new ApiResult { StateCode = 0, Message = "该数据不存在" };
            }
            if (record.StateCode == 1) {
                return new ApiResult { StateCode = 0, Message = "该数据不存在" };
            }
            if (record.ExpirationTime != null && DateTime.Compare(DateTime.Now, record.ExpirationTime.Value) > 0) {
                return new ApiResult { StateCode = 0, Message = "数据已过期被清理" };
            }
            if (record.LiftTime != null && DateTime.Compare(record.LiftTime.Value, DateTime.Now) > 0) {
                var liftTime = record.LiftTime.Value;
                return new ApiResult { StateCode = 0, Message = $"该数据已被锁定，暂时无法提供下载，请在{liftTime:yyyy-MM-dd HH:mm:ss}后重新下载" };
            }

            var baseInfo = service.GetById<BusinessBaseInfo>(record.BusinessID);
            if (baseInfo == null) {
                return new ApiResult { StateCode = 0, Message = "该数据对应的业务不存在" };
            }

            if (baseInfo.BusinessClass != "BaseSurveyDataDownloadFlow" && baseInfo.BusinessClass != "MarkPointSurveyFlow") {
                return new ApiResult { StateCode = 0, Message = "该数据对应的业务不存在" };
            }

            if (string.IsNullOrWhiteSpace(record.ValidateNumber)) {
                return new ApiResult { StateCode = 0, Message = "获取提取码失败" };
            }

            //判断1分钟内是否已推送发过短信
            var cache = MongoCache.Get("GetValidateNumber_" + record.ValidateNumber);
            if (cache != null) {
                return new ApiResult { StateCode = 0, Message = "提取码已发送，请勿重复操作" };
            }

            //缓存1分钟
            MongoCache.Set("GetValidateNumber_" + record.ValidateNumber, "true", TimeSpan.FromMinutes(1));

            var smsMessage = $"您正在提取基础测绘地理信息数据，提取码{record.ValidateNumber}。该提取码可以用于本业务全部的基础数据提取，无需重复获取。";

            if (baseInfo.BusinessClass == "BaseSurveyDataDownloadFlow") {
                //await SmsService.SendSms(baseInfo.CreatePersonPhone, smsMessage);
                Hangfire.BackgroundJob.Enqueue(() =>
                    SmsService.HangfireSendSms(baseInfo.CreatePersonPhone, smsMessage, null));
                return new ApiResult { StateCode = 1, Message = $"提取码已发送到手机{baseInfo.CreatePersonPhone}，请注意查收" };
            }
            else {
                //await SmsService.SendSms(UserInfo.Phone, smsMessage);
                Hangfire.BackgroundJob.Enqueue(() =>
                    SmsService.HangfireSendSms(UserInfo.Phone, smsMessage, null));
                return new ApiResult { StateCode = 1, Message = $"提取码已发送到手机{UserInfo.Phone}，请注意查收" };
            }
        }

        /// <summary>
        /// 基础数据附件下载
        /// </summary>
        /// <param name="id"></param>
        /// <param name="validateNumber"></param>
        /// <returns></returns>
        [HttpGet, Route("BaseDataDownloadRequest")]
        public HttpResponseMessage BaseDataDownloadRequest(string id, string validateNumber) {
            if (id == null) {
                return new HttpResponseMessage(HttpStatusCode.BadRequest);
            }
            //ID必须存在对应的记录
            var record = service.GetById<AttachmentInfo>(id);
            if (record == null) {
                return Request.CreateErrorResponse(HttpStatusCode.NotFound, "该数据不存在，无法提供下载");
            }
            //新增：当项目附件被逻辑删除时 按404处理
            if (record.StateCode == 1) {
                return Request.CreateErrorResponse(HttpStatusCode.NotFound, "该数据不存在，无法提供下载");
            }
            if (record.ExpirationTime != null && DateTime.Compare(DateTime.Now, record.ExpirationTime.Value) > 0) {
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "数据已过期被清理，请重新申请");
            }
            if (record.LiftTime != null && DateTime.Compare(record.LiftTime.Value, DateTime.Now) > 0) {
                var liftTime = record.LiftTime.Value;
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, $"该数据已被锁定，暂时无法提供下载，请在{liftTime:yyyy-MM-dd HH:mm:ss}后重新下载");
            }
            //记录错误次数到缓存
            var key = "ValidateNumberError_" + record.ValidateNumber;
            if (record.ValidateNumber != validateNumber) {
                var times = 0;
                var timesString = MongoCache.Get(key);
                if (timesString != null) {
                    times = int.Parse(timesString);
                }

                if (++times >= 3) {
                    //已经错误3次，锁定
                    AttachmentLockRequest(record.ID);
                    MongoCache.Clear(key);
                    return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "您输入错误次数太多，数据已被锁定");
                }
                MongoCache.Set(key, times.ToString(), TimeSpan.FromMinutes(10));
                return Request.CreateErrorResponse(HttpStatusCode.BadRequest, "提取码错误，请填写正确的提取码");
            }
            else {
                MongoCache.Clear(key);
            }
            try {
                string RootPath = WebConfigurationManager.AppSettings["RootPath"];
                var stream = new FileStream(RootPath + "\\" + record.AttachmentPath, FileMode.Open, FileAccess.Read, FileShare.Read);
                HttpResponseMessage response = new HttpResponseMessage(HttpStatusCode.OK);
                response.Content = new StreamContent(stream);
                response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/octet-stream");
                //获取原始文件名称
                var FileName = record.AttachmentName + record.AttachmentExt;
                response.Content.Headers.ContentDisposition = new ContentDispositionHeaderValue("attachment") {
                    FileName = FileName
                };
                return response;
            }
            catch (Exception e) {
                return Request.CreateErrorResponse(HttpStatusCode.InternalServerError, e.Message);
            }
        }

        /// <summary>
        /// 附件锁定
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost, Route("AttachmentLockRequest")]
        public ApiResult AttachmentLockRequest(string id) {
            if (id == null) {
                return new ApiResult { StateCode = 0, Message = "锁定失败，请输入正确的信息" };
            }
            //ID必须存在对应的记录
            var record = service.GetById<AttachmentInfo>(id);
            if (record == null) {
                return new ApiResult { StateCode = 0, Message = "锁定失败，无法找到附件信息" };
            }
            //附件锁定两小时
            record.LiftTime = DateTime.Now.AddHours(2);
            try {
                service.UpdateOne(record);
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 0, Message = "锁定失败，错误信息：" + e.Message + "" };
            }
            return new ApiResult { StateCode = 1, Message = "锁定成功" };
        }

        /// <summary>
        /// 获取立项依据文件内容
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("GetAttachementFileStr")]
        public ApiResult GetAttachementFileStr(string id) {
            if (id == null) {
                return new ApiResult { StateCode = 0, Message = "获取失败，请输入正确的信息" };
            }
            //ID必须存在对应的记录
            var record = service.GetById<AttachmentInfo>(id);
            if (record == null) {
                return new ApiResult { StateCode = 0, Message = "获取失败，无法找到附件信息" };
            }
            try {
                string RootPath = WebConfigurationManager.AppSettings["RootPath"];
                FileStream file = new FileStream(RootPath + "\\" + record.AttachmentPath, FileMode.Open);
                StreamReader sr2 = new StreamReader(file);
                string result2 = sr2.ReadToEnd();
                sr2.Close();
                return new ApiResult { StateCode = 1, Message = "获取成功", Data = result2 };
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 0, Message = "获取失败，错误信息：" + e.Message + "" };
            }
        }


        /// <summary>
        /// 附件逻辑删除
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost, Route("AttachmentDeleteRequest")]
        public ApiResult AttachmentDeleteRequest(string id) {
            if (id == null) {
                return new ApiResult { StateCode = 0, Message = "删除失败，请输入正确的信息" };
            }
            //ID必须存在对应的记录
            var record = service.GetById<AttachmentInfo>(id);
            if (record == null) {
                return new ApiResult { StateCode = 0, Message = "删除失败，无法找到附件信息" };
            }
            //逻辑删除
            record.StateCode = 1;
            try {
                service.UpdateOne(record);
                LogService.WriteLogs(Request, "附件管理", $"删除附件 >> 附件ID：{record.ID} >> 业务ID：{record.BusinessID} >> 附件名称：{record.AttachmentName}", new HttpRequestInfoHelper(((HttpContextBase)Request.Properties["MS_HttpContext"]).Request));
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 0, Message = "删除失败，错误信息：" + e.Message + "" };
            }
            return new ApiResult { StateCode = 1, Message = "删除成功" };
        }
        /// <summary>
        /// 获取附件列表
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost, Route("GetAttachmentListRequest")]
        public ApiResult GetAttachmentListRequest(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult { StateCode = 0, Message = "ID不能为空，请传入正确的参数" };
            }
            try {
                //获取附件列表
                List<AttachmentInfo> AttachmentList = service.GetList<AttachmentInfo>("BusinessID='" + id + "' And StateCode=0");
                if (AttachmentList.Count > 0) {
                    return new ApiResult { StateCode = 1, Message = "", Data = AttachmentList };
                }
                else {
                    return new ApiResult { StateCode = 0, Message = "附件列表获取失败，无法找到任何附件信息" };
                }
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 0, Message = "附件列表获取失败，错误信息：" + e.Message + "" };
            }
        }

        /// <summary>
        /// 系统附件下载
        /// </summary>
        /// <param name="fileName"></param>
        /// <returns></returns>
        [HttpGet, Route("SystemAttachmentDownload")]
        public HttpResponseMessage SystemAttachmentDownload(string fileName) {
            if (fileName == null) {
                return Request.CreateErrorResponse(HttpStatusCode.NotFound, "该数据不存在，无法提供下载");
            }
            try {
                string RootPath = WebConfigurationManager.AppSettings["RootPath"];
                if (!File.Exists(RootPath + "\\Demo\\" + fileName)) {
                    return Request.CreateErrorResponse(HttpStatusCode.NotFound, "该数据不存在，无法提供下载");
                }
                var stream = new FileStream(RootPath + "\\Demo\\" + fileName, FileMode.Open);
                HttpResponseMessage response = new HttpResponseMessage(HttpStatusCode.OK);
                response.Content = new StreamContent(stream);
                response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/octet-stream");
                response.Content.Headers.ContentDisposition = new ContentDispositionHeaderValue("attachment") {
                    FileName = fileName
                };
                return response;
            }
            catch (Exception e) {
                return Request.CreateErrorResponse(HttpStatusCode.InternalServerError, e.Message);
            }
        }
        /// <summary>
        /// 根据单位id查看资质证书副本
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("ShowQualificationImage")]
        public HttpResponseMessage ShowQualificationImage(string id) {
            if (id == null) {
                return new HttpResponseMessage(HttpStatusCode.BadRequest);
            }
            //ID必须存在对应的记录
            List<AttachmentInfo> list = service.GetList<AttachmentInfo>($"BUSINESSID='{id}' and ATTACHMENTCATEGORIES='资质证书副本' and STATECODE=0");
            if (list.Count == 0) {
                return new HttpResponseMessage(HttpStatusCode.NotFound);
            }
            try {
                AttachmentInfo record = list[0];
                string RootPath = WebConfigurationManager.AppSettings["RootPath"];
                var imgStream = new MemoryStream(File.ReadAllBytes(RootPath + "\\" + record.AttachmentPath));
                var resp = new HttpResponseMessage(HttpStatusCode.OK) {
                    Content = new StreamContent(imgStream)
                };
                resp.Content.Headers.ContentType = new MediaTypeHeaderValue("image/jpg");
                return resp;
            }
            catch (Exception e) {
                return Request.CreateErrorResponse(HttpStatusCode.InternalServerError, e.Message);
            }
        }

        /// <summary>
        /// 生成缩略图
        /// </summary>
        /// <param name="fileName"></param>
        /// <param name="newFile"></param>
        /// <param name="maxHeight"></param>
        /// <param name="maxWidth"></param>
        /// <param name="qualityNum"></param>
        /// <param name="mode"></param>
        public static void SendSmallImage(string fileName, string newFile, int maxHeight, int maxWidth, long qualityNum, string mode) {
            Image img = Image.FromFile(fileName);
            System.Drawing.Imaging.ImageFormat thisFormat = img.RawFormat;
            int towidth = maxWidth;
            int toheight = maxHeight;
            int x = 0;
            int y = 0;
            int ow = img.Width;
            int oh = img.Height;
            switch (mode) {
                case "HW"://指定高宽缩放（可能变形）                
                    break;
                case "W"://指定宽，高按比例                    
                    toheight = img.Height * maxWidth / img.Width;
                    break;
                case "H"://指定高，宽按比例
                    towidth = img.Width * maxHeight / img.Height;
                    break;
                case "CUT"://指定高宽裁减（不变形）                
                    if ((double)img.Width / (double)img.Height > (double)towidth / (double)toheight) {
                        oh = img.Height;
                        ow = img.Height * towidth / toheight;
                        y = 0;
                        x = (img.Width - ow) / 2;
                    }
                    else {
                        ow = img.Width;
                        oh = img.Width * maxHeight / towidth;
                        x = 0;
                        y = (img.Height - oh) / 2;
                    }
                    break;
                default:
                    break;
            }
            Bitmap outBmp = new Bitmap(towidth, toheight);
            Graphics g = Graphics.FromImage(outBmp);
            // 设置画布的描绘质量
            g.CompositingQuality = CompositingQuality.HighQuality;
            g.SmoothingMode = SmoothingMode.HighQuality;
            g.InterpolationMode = InterpolationMode.HighQualityBicubic;
            g.DrawImage(img, new Rectangle(0, 0, towidth, toheight), x, y, ow, oh, GraphicsUnit.Pixel);
            g.Dispose();
            // 以下代码为保存图片时,设置压缩质量
            EncoderParameters encoderParams = new EncoderParameters();
            long[] quality = new long[1];
            quality[0] = qualityNum;//图片质量1-100
            EncoderParameter encoderParam = new EncoderParameter(System.Drawing.Imaging.Encoder.Quality, quality);
            encoderParams.Param[0] = encoderParam;
            //获得包含有关内置图像编码解码器的信息的ImageCodecInfo 对象.
            ImageCodecInfo[] arrayICI = ImageCodecInfo.GetImageEncoders();
            ImageCodecInfo jpegICI = null;
            for (int index = 0; index < arrayICI.Length; index++) {
                if (arrayICI[index].FormatDescription.Equals("JPEG")) {
                    jpegICI = arrayICI[index];
                    //设置JPEG编码
                    break;
                }
            }
            if (jpegICI != null) {
                outBmp.Save(newFile, jpegICI, encoderParams);
            }
            else {
                outBmp.Save(newFile, thisFormat);
            }
            img.Dispose();
            outBmp.Dispose();
        }
    }
}

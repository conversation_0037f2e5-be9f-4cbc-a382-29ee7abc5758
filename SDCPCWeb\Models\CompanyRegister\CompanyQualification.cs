﻿using Newtonsoft.Json;
using SDCPCWeb.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using SDCPCWeb.Models.BusinessFlow;
using SDCPCWeb.Models.System;

namespace SDCPCWeb.Models.CompanyRegister {
    /// <summary>
    /// 单位资质信息
    /// </summary>
    public class CompanyQualificationModel {
        /// <summary>
        /// ID
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 关联已通过的申请ID，或者变更ID
        /// </summary>
        public string RelationRequestId { get; set; }
        /// <summary>
        /// 资质证书号
        /// </summary>
        public string CertificateNo { get; set; }
        /// <summary>
        /// 证书有效期
        /// </summary>
        public DateTime? ValidityTime { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Notes { get; set; }
        /// <summary>
        /// 测绘最高资质等级
        /// </summary>
        public string QualificationLevel { get; set; }
        /// <summary>
        /// 多测合一业务范围
        /// </summary>
        public string BusinessRange { get; set; }
        /// <summary>
        /// 附件信息
        /// </summary>
        public string AttachmentInfo { get; set; }

    }

    public class CompanyQualification : CompanyQualificationModel, IOracleDataTable {
        public CompanyQualificationModel ToModel() {
            return JsonConvert.DeserializeObject<CompanyQualificationModel>(JsonConvert.SerializeObject(this));
        }
    }

    public class CompanyQualificationShowModel : CompanyQualificationModel {
        /// <summary>
        /// 多测合一业务范围（业务代码）
        /// </summary>
        public string BusinessRangeNames { get; set; }
        public string BusinessRangeTree { get; set; }
        public List<object> BusinessRangeJSON { get; set; }

        /// <summary>
        /// 根据模型转换，显示名称和单位信息在这时候转换过来
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public static CompanyQualificationShowModel FromModel(CompanyQualificationModel model) {
            var showModel = JsonConvert.DeserializeObject<CompanyQualificationShowModel>(JsonConvert.SerializeObject(model));
            showModel.BusinessRangeNames = showModel.BusinessRangeClassesToNames();
            showModel.BusinessRangeTree = showModel.BusinessRangeClassesToTree();
            showModel.BusinessRangeJSON = showModel.BusinessRangeClassesToJSON();
            return showModel;
        }

        public CompanyQualification ToEntity() {
            var entity = JsonConvert.DeserializeObject<CompanyQualification>(JsonConvert.SerializeObject(this));
            return entity;
        }

        private string BusinessRangeClassesToNames() {
            if (string.IsNullOrWhiteSpace(BusinessRange)) {
                return string.Empty;
            }

            var classes = JsonConvert.DeserializeObject<List<string>>(BusinessRange);
            var result = new List<List<string>>();
            foreach (var businessClass in classes) {
                var flow = BusinessFlowConfig.GetFlow(Type.GetType($"SDCPCWeb.Models.BusinessFlow.{businessClass}"));
                if (flow != null) {
                    var catalog = flow.Catalog;
                    var flowName = flow.FlowName;
                    result.Add(new List<string>() { catalog, flowName });
                }
            }

            return result.Any() ? JsonConvert.SerializeObject(result) : string.Empty;
        }

        private string BusinessRangeClassesToTree() {
            if (string.IsNullOrWhiteSpace(BusinessRange)) {
                return string.Empty;
            }

            var classes = JsonConvert.DeserializeObject<List<string>>(BusinessRange);
            var result = new List<List<string>>();
            foreach (var businessClass in classes) {
                var flow = BusinessFlowConfig.GetFlow(Type.GetType($"SDCPCWeb.Models.BusinessFlow.{businessClass}"));
                if (flow != null) {
                    var catalog = flow.Catalog;
                    result.Add(new List<string>() { catalog, businessClass });
                }
            }

            return result.Any() ? JsonConvert.SerializeObject(result) : string.Empty;
        }

        private List<object> BusinessRangeClassesToJSON() {
            if (string.IsNullOrWhiteSpace(BusinessRange)) {
                return new List<object>();
            }

            var classes = JsonConvert.DeserializeObject<List<string>>(BusinessRange);
            var result = new List<object>();
            foreach (var businessClass in classes) {
                var flow = BusinessFlowConfig.GetFlow(Type.GetType($"SDCPCWeb.Models.BusinessFlow.{businessClass}"));
                if (flow != null) {
                    result.Add(new {
                        BusinessClass = businessClass,
                        BusinessName = flow.FlowName,
                        flow.HasSurvey,
                        flow.Enable,
                        flow.Visible
                    });
                }
            }

            return result;
        }
    }
}
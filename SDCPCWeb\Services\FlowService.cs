﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Oracle.ManagedDataAccess.Client;
using SDCPCWeb.Models.BusinessFlow;
using SDCPCWeb.Models.System;

namespace SDCPCWeb.Services {
    /// <summary>
    /// 流转服务
    /// </summary>
    public class FlowService<T> where T : BusinessFlowBase {
        /// <summary>
        /// 数据库服务
        /// </summary>
        //private OracleDataService oracle;

        private T Flow = BusinessFlowConfig.GetFlow<T>();

        public FlowService() {

        }

        /// <summary>
        /// 用数据库服务实例化流程流转服务
        /// </summary>
        /// <param name="oracle"></param>
        public FlowService(OracleDataService oracle) {
            //this.oracle = oracle;
        }

        /// <summary>
        /// 流程提交到下一个环节，调用此方法前，需判断用户是否有权限提交到下一步
        /// </summary>
        public void FlowPostToNext(string projectid) {
            using (OracleDataService oracle = new OracleDataService()) {
                var flow = Flow as BusinessFlowBase;
                if (flow != null) {
                    var baseinfo = oracle.GetById<BusinessBaseInfo>(projectid);
                    var businessLinkInfos = oracle.GetList<BusinessLinkInfo>($"BusinessId='{projectid}' And StateCode=1");
                    if (!businessLinkInfos.Any()) {
                        return;
                    }
                    var currentBusinessLinkInfo = businessLinkInfos.Last();
                    //流程可以提交下一步，必须满足条件
                    //1、状态为办理中
                    //2、具有签收时间
                    //3、具有当前用户ID
                    if (!(currentBusinessLinkInfo.StateCode == 1 && currentBusinessLinkInfo.SignatureTime != null && !string.IsNullOrWhiteSpace(currentBusinessLinkInfo.CurrentUserID))) {
                        return;
                    }
                    var currentActionId = currentBusinessLinkInfo?.ActionId;
                    if (currentActionId != null) {
                        var flowAction = flow.FlowActionInfo.Actions.FirstOrDefault(a => a.ID == currentActionId);
                        if (flowAction != null) {
                            //提交验证，未设置时则验证通过
                            var b = flow.FlowActionInfo.ValidateActionPost?.Invoke(baseinfo, currentBusinessLinkInfo) ?? true;
                            if (!b) {
                                //验证失败
                                return;
                            }

                            //响应提交前事件，当发生异常时，终止提交
                            try {
                                flow.FlowActionInfo.OnBeforeActionPost(baseinfo, currentBusinessLinkInfo);
                            }
                            catch (Exception e) {
                                LogService.Debug(e.Message + Environment.NewLine + e.StackTrace);
                                return;
                            }

                            //此处开始执行提交逻辑处理，处理完成后，后续事件若发生异常，则直接跳过继续往下执行，异常信息仅记录在日志文件中

                            //当前环节置为已完成
                            currentBusinessLinkInfo.StateCode = 2;
                            currentBusinessLinkInfo.EndTime = DateTime.Now;
                            oracle.UpdateOne(currentBusinessLinkInfo);

                            //当前环节ID属于结束环节
                            if (flow.FlowActionInfo.EndActionIds.Contains(flowAction.ID)) {
                                //更新业务基本信息为结束
                                baseinfo.FinishTime = DateTime.Now;
                                baseinfo.StateCode = 2;
                                oracle.UpdateOne(baseinfo);
                            }
                            else {
                                //获取提交的环节路由，流程定义一定要正确，不正确则会在这里抛出异常
                                var route = flow.FlowActionInfo.Routes.FirstOrDefault(r => r.FromActionId == flowAction.ID);
                                var toAction = flow.FlowActionInfo.Actions.First(a => a.ID == route.ToActionId);
                                var actionRoles = toAction.ActionRoles;
                                var roles = string.Join(",", actionRoles.Select(r => r.ToString()));
                                var newBusinessLinkInfo = new BusinessLinkInfo {
                                    ID = Guid.NewGuid().ToString("N"),
                                    BusinessId = currentBusinessLinkInfo.BusinessId,
                                    CurrentUserID = null,
                                    StartTime = DateTime.Now,
                                    ActionId = toAction.ID,
                                    LinkName = toAction.Name,
                                    StateCode = 0,
                                    ActionUserRole = roles
                                };
                                if (toAction.ActionRoles.Contains(UserRole.Myself)) {
                                    //如果下个环节的执行者是自己，则直接为当前用户在办
                                    newBusinessLinkInfo.StateCode = 1;
                                    newBusinessLinkInfo.SignatureTime = DateTime.Now;
                                    newBusinessLinkInfo.CurrentUserID = currentBusinessLinkInfo.CurrentUserID;
                                }
                                else if (toAction.ActionRoles.Contains(UserRole.ProjectCreator)) {
                                    //提交返回项目创建者，则已经有明确的用户目标，直接指定待签收者
                                    newBusinessLinkInfo.CurrentUserID = baseinfo.CreateUserId;
                                }

                                if (actionRoles.All(r => r != UserRole.Myself && r != UserRole.ProjectCreator)) {
                                    //如果环节不是提交给自己或者业务创建者，则需要建立流程环节签收索引，环节签收后删除索引
                                    foreach (var userRole in actionRoles) {
                                        var role = (int)userRole;
                                        var sign = new BusinessLinkToSign {
                                            ID = Guid.NewGuid().ToString("N"),
                                            BusinessLinkInfoID = newBusinessLinkInfo.ID,
                                            RoleCode = role
                                        };
                                        oracle.InsertOne(sign);
                                    }
                                }
                                oracle.InsertOne(newBusinessLinkInfo);
                                baseinfo.StateCode = newBusinessLinkInfo.StateCode;
                                oracle.UpdateOne(baseinfo);//业务基本信息同步更新为待签收
                                //当流程到最后一个岗位为自动结束，并且当前提交流向是提交至最后一岗
                                if (flow.FlowActionInfo.FinishWhenIntoEndAction == true && flow.FlowActionInfo.EndActionIds.Contains(route.ToActionId)) {
                                    newBusinessLinkInfo.EndTime = DateTime.Now;
                                    newBusinessLinkInfo.StateCode = 2;
                                    oracle.UpdateOne(newBusinessLinkInfo);
                                    baseinfo.StateCode = 2;
                                    baseinfo.FinishTime = DateTime.Now;
                                    oracle.UpdateOne(baseinfo);
                                }
                                //响应路由事件
                                route?.OnRoutePassed(baseinfo, currentBusinessLinkInfo);
                            }
                            //响应提交后事件
                            try {
                                flow.FlowActionInfo.OnActionPosted(baseinfo, currentBusinessLinkInfo);
                            }
                            catch (Exception e) {
                                LogService.Debug(e.Message + Environment.NewLine + e.StackTrace);
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 流程回退至前面的环节
        /// </summary>
        /// <param name="projectid"></param>
        /// <param name="notice">流转意见</param>
        /// <param name="isUploadMDBOnly">是否替换MDB</param>
        public void FlowPostToLast(string projectid, string notice, bool? isUploadMDBOnly = false) {
            using (OracleDataService oracle = new OracleDataService()) {
                var flow = Flow as BusinessFlowBase;
                if (flow != null) {
                    var baseinfo = oracle.GetById<BusinessBaseInfo>(projectid);
                    var BusinessLinkInfos = oracle.GetList<BusinessLinkInfo>($"BusinessId='{projectid}'");
                    if (!BusinessLinkInfos.Any()) {
                        return;
                    }

                    var currentBusinessLinkInfo = BusinessLinkInfos.OrderByDescending(a => a.StartTime).Last(a => a.StateCode == 1);
                    //流程可以回退，必须满足条件
                    //1、状态为办理中
                    //2、具有签收时间
                    //3、具有当前用户ID
                    if (!(currentBusinessLinkInfo.StateCode == 1 && currentBusinessLinkInfo.SignatureTime != null &&
                          !string.IsNullOrWhiteSpace(currentBusinessLinkInfo.CurrentUserID))) {
                        return;
                    }

                    var currentActionId = currentBusinessLinkInfo.ActionId;
                    var flowAction = flow.FlowActionInfo.Actions.First(a => a.ID == currentActionId);
                    if (flowAction != null) {
                        //获取提交的环节路由，流程定义一定要正确，不正确则会在这里抛出异常
                        var route = flow.FlowActionInfo.BackRoutes.First(r => r.FromActionId == flowAction.ID);
                        if (route.NeedResponseReason && string.IsNullOrWhiteSpace(notice)) {
                            throw new ArgumentException("请填写退回意见", "notice");
                        }
                        //提交验证，未设置时则验证通过
                        var b = flow.FlowActionInfo.ValidateActionPost?.Invoke(baseinfo, currentBusinessLinkInfo) ?? true;
                        if (!b) {
                            //验证失败
                            return;
                        }

                        //需要增加一个回退事件
                        try {
                            flow.FlowActionInfo.OnBeforeActionPost(baseinfo, currentBusinessLinkInfo);
                        }
                        catch (Exception e) {
                            LogService.Debug(e.Message + Environment.NewLine + e.StackTrace);
                            return;
                        }

                        //此处开始执行提交逻辑处理，处理完成后，后续事件若发生异常，则直接跳过继续往下执行，异常信息仅记录在日志文件中

                        //当前环节置为已完成
                        currentBusinessLinkInfo.StateCode = 2;
                        currentBusinessLinkInfo.EndTime = DateTime.Now;
                        oracle.UpdateOne(currentBusinessLinkInfo);
                        //否则，上一个目标环节是谁经办，就退给谁
                        var prevAction = BusinessLinkInfos.OrderByDescending(a => a.StartTime).First(a => a.ActionId == route.ToActionId);
                        var newBusinessLinkInfo = new BusinessLinkInfo {
                            ID = Guid.NewGuid().ToString("N"),
                            BusinessId = currentBusinessLinkInfo.BusinessId,
                            CurrentUserID = prevAction.CurrentUserID,
                            StartTime = DateTime.Now,
                            ActionId = prevAction.ActionId,
                            LinkName = prevAction.LinkName,
                            StateCode = 3, //已退回
                            ActionUserRole = prevAction.ActionUserRole
                        };
                        if (route.IsBackToMyself) {
                            //如果流程设定退回给自己，则直接签收进入在办
                            newBusinessLinkInfo.StateCode = 1;
                            newBusinessLinkInfo.SignatureTime = DateTime.Now;
                            newBusinessLinkInfo.CurrentUserID = currentBusinessLinkInfo.CurrentUserID;
                        }

                        if (route.NeedResponseReason) {
                            newBusinessLinkInfo.FlowNotice = isUploadMDBOnly == true ? $"修改意见：{notice}" : $"退回意见：{notice}";
                        }

                        baseinfo.StateCode = newBusinessLinkInfo.StateCode;
                        oracle.InsertOne(newBusinessLinkInfo);
                        oracle.UpdateOne(baseinfo); //业务基本信息同步更新为已退回
                        //响应路由事件
                        route?.OnRoutePassed(baseinfo, newBusinessLinkInfo);

                        //响应提交后事件
                        try {
                            flow.FlowActionInfo.OnActionPosted(baseinfo, newBusinessLinkInfo);
                        }
                        catch (Exception e) {
                            LogService.Debug(e.Message + Environment.NewLine + e.StackTrace);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 判断当前环节是否已处理
        /// </summary>
        /// <param name="actionId">环节Id</param>
        /// <returns>true：已处理；false：未处理</returns>
        public bool CheckActionIdIsProcessed(string actionId) {
            string whereText = $"ID=:actionId AND ENDTIME IS NOT NULL";
            using (OracleDataService oracle = new OracleDataService()) {
                return oracle.Exists<BusinessLinkInfo>(whereText,
                    new OracleParameter(":actionId", OracleDbType.Varchar2) {Value = actionId});
            }
        }

        /// <summary>
        /// 获取当前(未处理)的环节id
        /// </summary>
        /// <param name="projectid"></param>
        /// <returns></returns>
        public string GetCurrentActionId(string projectid) {
            string whereText = $"BUSINESSID=:projectid AND ENDTIME IS NULL";
            using (OracleDataService oracle = new OracleDataService()) {
                var linkInfo = oracle.GetList<BusinessLinkInfo>(whereText,
                    new OracleParameter(":projectid", OracleDbType.Varchar2) { Value = projectid })
                    .FirstOrDefault();
                return linkInfo?.ID;
            }
        }
    }
}
﻿using Newtonsoft.Json;
using SDCPCWeb.Models.Attachment;
using SDCPCWeb.Models.BusinessContent.YanXian;
using SDCPCWeb.Models.System;
using SDCPCWeb.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using Newtonsoft.Json.Linq;
using SDCPCWeb.Extensions;

namespace SDCPCWeb.Models.BusinessFlow {
    /// <summary>
    /// 不动产预测绘楼盘表变更业务(Copy自RealEstatePreSurveyProject)
    /// </summary>
    public class RealEstatePreSurveyBuildingTableChangeProject : BaseProject {
        //private static OracleDataService service = new OracleDataService();
        /// <summary>
        /// 基本信息
        /// </summary>
        public override BusinessBaseInfoModel BaseInfo { get; set; }
        /// <summary>
        /// 内容信息
        /// </summary>
        public PreSurvey_ContentInfo ContentInfo { get; set; }
        /// <summary>
        /// 附件信息
        /// </summary>
        public override List<AttachmentInfoModel> Attachments { get; set; }
        /// <summary>
        /// 当前经过的流转环节信息
        /// </summary>
        public override List<BusinessLinkInfoModel> ActionsInfos { get; set; }
        /// <summary>
        /// 最新的环节，当前环节
        /// </summary>
        public override BusinessLinkInfoModel CurrentAction { get; set; }
        /// <summary>
        /// 工作流定义信息
        /// </summary>
        public override BusinessFlowBase FlowInfo { get; } = BusinessFlowConfig.RealEstatePreSurveyBuildingTableChangeFlow;
        /// <summary>
        /// 根据ID获取信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static RealEstatePreSurveyBuildingTableChangeProject GetByBusinessID(string id) {
            RealEstatePreSurveyBuildingTableChangeProject project = new RealEstatePreSurveyBuildingTableChangeProject();
            using (OracleDataService service = new OracleDataService()) {
                project.BaseInfo = service.GetById<BusinessBaseInfo>(id);
                project.ContentInfo = service.GetById<PreSurvey_ContentInfo>(id);
                project.Attachments = service.GetList<AttachmentInfo>("BusinessID='" + id + "' and StateCode=0")?.Select(a => a.ToModel()).ToList();
                project.ActionsInfos = service.GetList<BusinessLinkInfo>("BusinessID='" + id + "'")?.Select(a => a.ToModel()).OrderBy(a => a.StartTime).ToList();
                project.CurrentAction = project.ActionsInfos?.OrderByDescending(a => a.StartTime).FirstOrDefault();
                return project;
            }
        }

        /// <summary>
        /// 根据ID获取信息
        /// </summary>
        /// <param name="id"></param>
        /// <param name="getExtendAttr">是否获取扩展信息</param>
        /// <returns></returns>
        public static async Task<RealEstatePreSurveyBuildingTableChangeProject> GetByBusinessID(string id, bool? getExtendAttr) {
            var project = GetByBusinessID(id);

            if (getExtendAttr == true) {
                if (project.CurrentAction != null) {
                    var BusinessLinkInfoModelEx = new BusinessLinkInfoModelEx(project.CurrentAction);
                    BusinessLinkInfoModelEx.ExtendAttr =
                        JObject.FromObject(new {
                            @XCloudSteps = (project.CurrentAction.ActionId == 1) ? await BusinessFlowConfig.CreateXCloudSteps(id, nameof(RealEstatePreSurveyBuildingTableChangeFlow)) : null, //获取云平台流程步骤
                            @XCloudSPYJ = (project.CurrentAction.ActionId == 1) ? await XCloudService.GetSpyjByBusinessId(id, nameof(RealEstatePreSurveyBuildingTableChangeFlow)) : null, //获取云平台审批意见
                            @XCloudSuspendInfo = (project.CurrentAction.ActionId == 1) ? await XCloudService.GetSuspendInfo(id, nameof(RealEstatePreSurveyBuildingTableChangeFlow)) : null, //获取云平台挂起信息
                        });

                    project.CurrentAction = BusinessLinkInfoModelEx;
                }
            }

            return project;
        }

        /// <summary>
        /// 保存信息
        /// </summary>
        /// <param name="project"></param>
        /// <returns></returns>
        public static string Save(RealEstatePreSurveyBuildingTableChangeProject project) {
            string result = "";
            using (OracleDataService service = new OracleDataService()) {
                try {
                    var record = service.GetById<BusinessBaseInfo>(project.BaseInfo.ID);
                    //更新业务基本信息表
                    record.BusinessName = project.BaseInfo.BusinessName;
                    record.ExtendInfo = project.BaseInfo.ExtendInfo;
                    service.UpdateOne(record);
                    //判断业务内容信息是否存在
                    PreSurvey_ContentInfo content = service.GetById<PreSurvey_ContentInfo>(project.BaseInfo.ID);
                    if (content == null) {
                        content = project.ContentInfo;
                        content.ID = content.ID ?? Guid.NewGuid().ToString("N");
                        service.InsertOne(content);
                    } else {
                        content.GroundCode = project.ContentInfo.GroundCode;
                        content.ProjectPlanPermission = project.ContentInfo.ProjectPlanPermission;
                        service.UpdateOne(content);
                    }
                } catch (Exception e) {
                    result = e.Message;
                }
            }
            return result;
        }

        /// <summary>
        /// 流程服务
        /// </summary>
        public static FlowService<RealEstatePreSurveyBuildingTableChangeFlow> FlowService => new FlowService<RealEstatePreSurveyBuildingTableChangeFlow>();
    }
    /// <summary>
    /// 不动产预测绘楼盘表变更流程定义(Copy自RealEstatePreSurveyFlow)
    /// </summary>
    public sealed class RealEstatePreSurveyBuildingTableChangeFlow : BusinessFlowBase {
        public RealEstatePreSurveyBuildingTableChangeFlow() {
            //实例化时，在构造函数定义工作流程
            FlowName = "不动产预测绘楼盘表维护";
            Catalog = "规划验线";
            FlowActionInfo = new BusinessFlowActionInfo() {
                Actions = new[] {
                    new BusinessFlowAction() {
                        ID = 0,
                        Index = 0,
                        ActionRoles = new[] {UserRole.BusinessAdmin, UserRole.BusinessNormal},
                        Name = "填写申请信息"
                    },
                    new BusinessFlowAction() {
                        ID = 1,
                        Index = 1,
                        ActionRoles = new[] {UserRole.Myself},
                        Name = "提交申请"
                    }
                },
                StartActionId = 0,
                EndActionIds = new[] { 1 },
                Routes = new[] {
                    new BusinessFlowRoute() {FromActionId = 0,ToActionId = 1},
                },
                BackRoutes = new[] {
                    new BusinessFlowBackRoute() {FromActionId = 1,ToActionId = 0,NeedResponseReason = true,IsBackToMyself = true }
                },
                FinishWhenIntoEndAction = false
            };

            //增加路由事件
            var backRouteF1T0 = FlowActionInfo.BackRoutes.FirstOrDefault(r => r.FromActionId == 1 && r.ToActionId == 0);
            if (backRouteF1T0 != null) {
                backRouteF1T0.RoutePassed += BackRouteF1T0_RoutePassed;
            }
        }

        private void BackRouteF1T0_RoutePassed(BusinessBaseInfoModel baseInfo, BusinessLinkInfoModel linkInfo) {
            //退回委托方，要移除已上传的测绘成果
            var businessId = baseInfo.ID;
            var oracle = new OracleDataService();

            var ps_contentInfo = oracle.GetById<PreSurvey_ContentInfo>(businessId);
            if (ps_contentInfo != null) {
                ps_contentInfo.DataCheckState = 0;
                oracle.UpdateOne(ps_contentInfo);
            }
        }


        #region Overrides of BusinessFlowBase
        /// <summary>
        /// 流程环节定义信息JSON
        /// </summary>
        /// <returns></returns>
        public override string FlowActionJSON {
            get {
                return JsonConvert.SerializeObject(FlowActionInfo.Actions.Select(a => new {
                    a.ID,
                    a.Name
                }));
            }
        }

        /// <summary>
        /// 是否包含测绘作业
        /// </summary>
        public override bool HasSurvey { get; } = false;

        /// <summary>
        /// 流程是否启用
        /// </summary>
        public override bool Enable => BusinessFlowConfig.EnableFlows.Contains(GetType().Name);

        /// <summary>
        /// 该流程是否需要协议备案
        /// </summary>
        public override bool NeedBDCProtocol { get; } = false;

        /// <summary>
        /// 该流程是否在办理业务中可见
        /// </summary>
        public override bool Visible => BusinessFlowConfig.VisibleFlows.Contains(GetType().Name);
        #endregion
    }
}
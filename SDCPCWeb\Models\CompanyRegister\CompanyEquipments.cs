﻿using Newtonsoft.Json;
using SDCPCWeb.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace SDCPCWeb.Models.CompanyRegister {
    /// <summary>
    /// 单位设备信息
    /// </summary>
    public class CompanyEquipmentsModel {
        /// <summary>
        /// ID
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 关联已通过的申请ID，或者变更ID
        /// </summary>
        public string RelationRequestId { get; set; }
        /// <summary>
        /// 仪器名称
        /// </summary>
        public string InstrumentName { get; set; }
        /// <summary>
        /// 品牌型号
        /// </summary>
        public string InstrumentModel { get; set; }
        /// <summary>
        /// 出厂编号
        /// </summary>
        public string FactoryNumber { get; set; }
        /// <summary>
        /// 检定日期
        /// </summary>
        public DateTime? TestTime { get; set; }
        /// <summary>
        /// 检定有效日期
        /// </summary>
        public DateTime? ValidTime { get; set; }
        /// <summary>
        /// 检定机构
        /// </summary>
        public string TestOrganization { get; set; }
        /// <summary>
        /// 检定证书号
        /// </summary>
        public string TestCertificateNo { get; set; }
        /// <summary>
        /// 发票代码
        /// </summary>
        public string InvoiceCode { get; set; }
        /// <summary>
        /// 附件信息
        /// </summary>
        public string AttachmentInfo { get; set; }

    }

    public class CompanyEquipments : CompanyEquipmentsModel, IOracleDataTable {
        public CompanyEquipmentsModel ToModel() {
            return JsonConvert.DeserializeObject<CompanyEquipmentsModel>(JsonConvert.SerializeObject(this));
        }
    }
}
﻿



# 接口一、创建单位注册申请

## 接口信息

- **接口地址：** https://practice.nngeo.com/sdcapi/CompanyRegister/CreateNewCompanyRequest

- **请求方式：**POST  

- **Content-Type：**application/json, text/json

- **请求参数**：

  ```json
  {
      "CompanyType": "测绘单位",
      "CompanyName": "测试",
      "CompanyNo": "124501000075762645",
      "DetailInfo": "{\"CompanyBaseInfo\":{\"CompanyName\":\"测试-测绘单位注册\",\"CompanyType\":\"测绘单位\",\"CompanyNo\":\"124501000075762645\",\"legalpersonName\":\"测试法人\",\"legalpersonNo\":\"450205197701010212\",\"linkMan\":\"测试联系人\",\"contactPhone\":\"***********\",\"CompanyAddress\":\"测试\"},\"CompanyDetailsInfo\":{\"CompanyNature\":\"私营企业\",\"QualificationLevel\":\"甲级\",\"RegistrationNumber\":\"123456\",\"OfficeArea\":\"200\",\"Notes\":\"1\"},\"CompanyQualification\":{\"CertificateNo\":\"甲测资字101201\",\"ValidityTime\":\"2020-09-30\",\"QualificationContent\":\"资质内容\",\"BusinessRange\":\"规划定点,勘测定界\",\"Notes\":\"\"},\"CompanyEmployees\":[{\"PersonName\":\"张三\",\"PersonNumber\":\"150426199003022123\",\"PersonPhone\":\"***********\",\"PersonSex\":\"男\",\"PersonAge\":\"45\",\"PersonCategory\":\"计入专业技术人员类别\",\"PersonRole\":\"角色\",\"PersonMajor\":\"所学专业\",\"ProfessionalTitle\":\"职称专业\",\"RegisteredSurveyorNo\":\"注册测绘师证书编号\",\"ValidityTime\":\"2025-01-01\"},{\"PersonName\":\"李四\",\"PersonNumber\":\"150426199002012568\",\"PersonPhone\":\"***********\",\"PersonSex\":\"女\",\"PersonAge\":\"35\",\"PersonCategory\":\"计入专业技术人员类别\",\"PersonRole\":\"角色\",\"PersonMajor\":\"所学专业\",\"ProfessionalTitle\":\"职称专业\",\"RegisteredSurveyorNo\":\"注册测绘师证书编号\",\"ValidityTime\":\"2025-01-01\"}],\"CompanyEquipments\":[{\"InstrumentName\":\"仪器名称\",\"InstrumentModel\":\"品牌型号\",\"FactoryNumber\":\"出厂编号\",\"TestTime\":\"2015-05-01\",\"ValidTime\":\"2026-02-03\",\"TestOrganization\":\"检定机构\",\"TestCertificateNo\":\"检定证书号\",\"InvoiceCode\":\"发票代码\"},{\"InstrumentName\":\"仪器名称\",\"InstrumentModel\":\"品牌型号\",\"FactoryNumber\":\"出厂编号\",\"TestTime\":\"2015-05-01\",\"ValidTime\":\"2026-02-03\",\"TestOrganization\":\"检定机构\",\"TestCertificateNo\":\"检定证书号\",\"InvoiceCode\":\"发票代码\"}]}"
  }
  ```

## 接口返回值示例：

```json
{
  //接口状态值 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
  "Data": {}
}
```

### 字段说明：

##### CompanyBaseInfo：单位基本信息

| 字段名称          | 字段描述           |
| ----------------- | ------------------ |
| CompanyName       | 单位名称           |
| CompanyType       | 单位类型           |
| CreditCode        | 统一社会信用代码   |
| LegalPersonName   | 法定代表人姓名     |
| LegalPersonNumber | 法定代表人身份证号 |
| CompanyAddress    | 单位地址           |
| Contacter         | 联系人             |
| ContacterPhone    | 联系电话           |

##### CompanyDetailsInfo：单位详细信息

| 字段名称           | 字段描述         |
| ------------------ | ---------------- |
| CompanyNature      | 单位性质         |
| QualificationLevel | 测绘最高资质等级 |
| RegistrationNumber | 工商注册号       |
| OfficeArea         | 办公场所面积     |
| Notes              | 备注             |
| AttachmentInfo     | 附件信息         |

##### CompanyQualification：单位资质信息

| 字段名称             | 字段描述         |
| -------------------- | ---------------- |
| CertificateNo        | 资质证书号       |
| ValidityTime         | 证书有效期       |
| QualificationContent | 资质内容         |
| BusinessRange        | 多测合一业务范围 |
| Notes                | 备注             |
| AttachmentInfo       | 附件信息         |

##### CompanyEmployees：单位人员信息

| 字段名称             | 字段描述               |
| -------------------- | ---------------------- |
| PersonName           | 姓名                   |
| PersonNumber         | 身份证号               |
| PersonPhone          | 手机号                 |
| PersonSex            | 性别                   |
| PersonAge            | 年龄                   |
| PersonCategory       | 计入专业技术人员类别   |
| PersonRole           | 角色                   |
| PersonMajor          | 所学专业               |
| ProfessionalTitle    | 职称专业               |
| RegisteredSurveyorNo | 注册测绘师证书编号     |
| ValidityTime         | 注册测绘师证书有效日期 |
| AttachmentInfo       | 附件信息               |

##### CompanyEquipments：单位设备信息

| 字段名称          | 字段描述   |
| ----------------- | ---------- |
| InstrumentName    | 仪器名称   |
| InstrumentModel   | 品牌型号   |
| FactoryNumber     | 出厂编号   |
| TestTime          | 检定日期   |
| ValidTime         | 检定有效期 |
| TestOrganization  | 检定机构   |
| TestCertificateNo | 检定证书号 |
| InvoiceCode       | 发票代码   |
| AttachmentInfo    | 附件信息   |



# 接口二、保存单位申请信息

## 接口信息

- **接口地址：** https://practice.nngeo.com/sdcapi/CompanyRegister/SaveCompanyRequest

- **请求方式：**POST  

- **Content-Type：**application/json, text/json

- **请求参数**：

  ```json
  {
      "ID":"3d79dfd3b59643cf947f40129ae9580a",
      "CompanyType": "测绘单位",
      "CompanyName": "测试",
      "CompanyNo": "124501000075762645",
      "DetailInfo": "{\"CompanyBaseInfo\":{\"CompanyName\":\"测试-测绘单位注册\",\"CompanyType\":\"测绘单位\",\"CompanyNo\":\"124501000075762645\",\"legalpersonName\":\"测试法人\",\"legalpersonNo\":\"450205197701010212\",\"linkMan\":\"测试联系人\",\"contactPhone\":\"***********\",\"CompanyAddress\":\"测试\"},\"CompanyDetailsInfo\":{\"CompanyNature\":\"私营企业\",\"QualificationLevel\":\"甲级\",\"RegistrationNumber\":\"123456\",\"OfficeArea\":\"200\",\"Notes\":\"1\"},\"CompanyQualification\":{\"CertificateNo\":\"甲测资字101201\",\"ValidityTime\":\"2020-09-30\",\"QualificationContent\":\"资质内容\",\"BusinessRange\":\"规划定点,勘测定界\",\"Notes\":\"\"},\"CompanyEmployees\":[{\"PersonName\":\"张三\",\"PersonNumber\":\"150426199003022123\",\"PersonPhone\":\"***********\",\"PersonSex\":\"男\",\"PersonAge\":\"45\",\"PersonCategory\":\"计入专业技术人员类别\",\"PersonRole\":\"角色\",\"PersonMajor\":\"所学专业\",\"ProfessionalTitle\":\"职称专业\",\"RegisteredSurveyorNo\":\"注册测绘师证书编号\",\"ValidityTime\":\"2025-01-01\"},{\"PersonName\":\"李四\",\"PersonNumber\":\"150426199002012568\",\"PersonPhone\":\"***********\",\"PersonSex\":\"女\",\"PersonAge\":\"35\",\"PersonCategory\":\"计入专业技术人员类别\",\"PersonRole\":\"角色\",\"PersonMajor\":\"所学专业\",\"ProfessionalTitle\":\"职称专业\",\"RegisteredSurveyorNo\":\"注册测绘师证书编号\",\"ValidityTime\":\"2025-01-01\"}],\"CompanyEquipments\":[{\"InstrumentName\":\"仪器名称\",\"InstrumentModel\":\"品牌型号\",\"FactoryNumber\":\"出厂编号\",\"TestTime\":\"2015-05-01\",\"ValidTime\":\"2026-02-03\",\"TestOrganization\":\"检定机构\",\"TestCertificateNo\":\"检定证书号\",\"InvoiceCode\":\"发票代码\"},{\"InstrumentName\":\"仪器名称\",\"InstrumentModel\":\"品牌型号\",\"FactoryNumber\":\"出厂编号\",\"TestTime\":\"2015-05-01\",\"ValidTime\":\"2026-02-03\",\"TestOrganization\":\"检定机构\",\"TestCertificateNo\":\"检定证书号\",\"InvoiceCode\":\"发票代码\"}]}"
  }
  ```

## 接口返回值示例：

```json
{
  //接口状态 是否成功 1保存成功 0保存失败
  "StateCode": 1,
  //接口返回信息
  "Message": "保存成功!",
  //接口返回数据
  "Data": {}
}
```

# 接口三、提交单位信息至审核

## 接口信息

- **接口地址：** https://practice.nngeo.com/sdcapi/CompanyRegister/SubmitCompanyAuditRequest

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

  id=3d79dfd3b59643cf947f40129ae9580a
  
  id：单位ID

## 接口返回值示例：

```json
{
  //接口状态 是否成功 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "提交成功",
  //接口返回数据
  "Data": {}
}
```

# 接口四、判断单位是否审核通过

## 接口信息

- **接口地址：** https://practice.nngeo.com/sdcapi/CompanyRegister/JudgeExamineRequest

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

  id=3d79dfd3b59643cf947f40129ae9580a
  
  id：单位ID

## 接口返回值示例：

```json
{
  //接口状态 是否成功 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "审核通过",
  //接口返回数据
  "Data": {}
}
```

# 接口五、单位信息初始化(弃)

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/CompanyRegister/RecordsGenerate

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

  id=3d79dfd3b59643cf947f40129ae9580a
  
  id：单位ID

## 接口返回值示例：

```json
{
  //接口状态 是否成功 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "生成记录成功",
  //接口返回数据
  "Data": {}
}
```

# 接口六、获取单位信息

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/CompanyRegister/GetCompanyDetailsInfo

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

  id=3d79dfd3b59643cf947f40129ae9580a
  
  id：单位ID

## 接口返回值示例：

```json
{
    "StateCode": 1,
    "Message": "",
    "Data": "{\"CompanyBaseInfo\":{\"CompanyName\":\"测试-测绘单位注册\",\"CompanyType\":\"测绘单位\",\"CompanyNo\":\"124501000075762645\",\"legalpersonName\":\"测试法人\",\"legalpersonNo\":\"450205197701010212\",\"linkMan\":\"测试联系人\",\"contactPhone\":\"***********\",\"CompanyAddress\":\"测试\"},\"CompanyDetailsInfo\":{\"CompanyNature\":\"私营企业\",\"QualificationLevel\":\"甲级\",\"RegistrationNumber\":\"123456\",\"OfficeArea\":\"200\",\"Notes\":\"1\"},\"CompanyQualification\":{\"CertificateNo\":\"甲测资字101201\",\"ValidityTime\":\"2020-09-30\",\"QualificationContent\":\"资质内容\",\"BusinessRange\":\"规划定点,勘测定界\",\"Notes\":\"\"},\"CompanyEmployees\":[{\"PersonName\":\"张三\",\"PersonNumber\":\"150426199003022123\",\"PersonPhone\":\"***********\",\"PersonSex\":\"男\",\"PersonAge\":\"45\",\"PersonCategory\":\"计入专业技术人员类别\",\"PersonRole\":\"角色\",\"PersonMajor\":\"所学专业\",\"ProfessionalTitle\":\"职称专业\",\"RegisteredSurveyorNo\":\"注册测绘师证书编号\",\"ValidityTime\":\"2025-01-01\"},{\"PersonName\":\"李四\",\"PersonNumber\":\"150426199002012568\",\"PersonPhone\":\"***********\",\"PersonSex\":\"女\",\"PersonAge\":\"35\",\"PersonCategory\":\"计入专业技术人员类别\",\"PersonRole\":\"角色\",\"PersonMajor\":\"所学专业\",\"ProfessionalTitle\":\"职称专业\",\"RegisteredSurveyorNo\":\"注册测绘师证书编号\",\"ValidityTime\":\"2025-01-01\"}],\"CompanyEquipments\":[{\"InstrumentName\":\"仪器名称\",\"InstrumentModel\":\"品牌型号\",\"FactoryNumber\":\"出厂编号\",\"TestTime\":\"2015-05-01\",\"ValidTime\":\"2026-02-03\",\"TestOrganization\":\"检定机构\",\"TestCertificateNo\":\"检定证书号\",\"InvoiceCode\":\"发票代码\"},{\"InstrumentName\":\"仪器名称\",\"InstrumentModel\":\"品牌型号\",\"FactoryNumber\":\"出厂编号\",\"TestTime\":\"2015-05-01\",\"ValidTime\":\"2026-02-03\",\"TestOrganization\":\"检定机构\",\"TestCertificateNo\":\"检定证书号\",\"InvoiceCode\":\"发票代码\"}]}"
}
```

# 接口七、逻辑删除单位申请信息

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/CompanyRegister/DeleteCompanyRequest

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

  id=3d79dfd3b59643cf947f40129ae9580a

  id：单位ID

## 接口返回值示例：

```json
{
  //接口状态 是否成功 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "删除成功",
  //接口返回数据
  "Data": {}
}
```

# 接口八、根据用户信息获取单位信息

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/CompanyRegister/GetCompanyInfoByUserInfo

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded


## 接口返回值示例：

```json
{
  //接口状态 是否成功 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
  "Data": "{\"CID\":\"3d79dfd3b59643cf947f40129ae9580a\",\"CName\":\"测试-测绘单位注册\",\"CType\":\"测绘单位\",\"State\":3}"
}
```

# 接口九、生成附件记录

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/AttachmentManage/CreateAttachmentRequest

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- ##### 请求参数：

```json
{
    "BusinessType": "单位详细信息",
    "BusinessID": "f80f613ce9994db4b0a984a4220da8e1",
    "AttachmentType": "申请材料附件",
    "AttachmentCategories": "办公场所照片"
}
```

## 接口返回值示例：

```json
{ 
   //接口状态 是否成功 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "",
  //接口返回数据
  "DATA": "f80f613ce9994db4b0a984a4220da8e1",
}
```

# 接口十、附件上传

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/AttachmentManage/AttachmentUploadRequest

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- ##### 请求参数：

  id=f80f613ce9994db4b0a984a4220da8e1

  id：附件唯一id

## 接口返回值示例：

```json
{
    "StateCode": 1,
    "Message": "上传成功",
    "Data": {
        "ID": "c935e647b1cc4d578dbb880f1e731c88",
        "AttachmentName": "mr",
        "AttachmentExt": ".jpg",
        "AttachmentLength": 101817,
        "AttachmentType": "申请材料附件",
        "AttachmentCategories": "企业法人营业执照或者事业单位法人证书",
        "SLPath": "C:\\Uploads\\2020\\6\\28\\ed58bf27a6c14ebe8ab02bf9d598b735\\mr.thumb"
    }
}
```

### 字段说明：

##### AttachmentInfo：附件基本信息

| 字段名称             | 字段描述                             |
| -------------------- | ------------------------------------ |
| ID                   | 附件唯一标识码                       |
| BusinessType         | 附件关联类型                         |
| BusinessID           | 附件关联ID                           |
| AttachmentName       | 附件名称                             |
| AttachmentExt        | 附件后缀名                           |
| AttachmentType       | 附件类型                             |
| AttachmentCategories | 附件类别                             |
| UploadTime           | 附件上传时间                         |
| AttachmentPath       | 附件路径                             |
| AttachmentThumbPath  | 附件缩略图路径                       |
| StateCode            | 附件状态（逻辑删除）0未删除，1已删除 |

# 接口十一、附件下载

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/AttachmentManage/AttachmentDownloadRequest

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- ##### 请求参数：

  id=f80f613ce9994db4b0a984a4220da8e1

  id：附件唯一id

## 接口返回值说明：

1、接口调用成功会直接通过浏览器进行下载

2、接口调用失败时，根据HttpStatusCode进行判断

​      a)当ID为空或NULL时，HttpStatusCode=400，错误请求。

​      b)当查询不到附件信息时，HttpStatusCode=404，未找到。

​      c)发生异常时，HttpStatusCode=500。

# 接口十二、附件逻辑删除

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/AttachmentManage/AttachmentDeleteRequest

- **请求方式：**POST

- **Content-Type：**application/x-www-form-urlencoded

- ##### 请求参数：

  id=f80f613ce9994db4b0a984a4220da8e1

  id：附件唯一id

## 接口返回值示例：

```json
{ 
   //接口状态 是否成功 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "删除成功",
  //接口返回数据
  "DATA": "",
}
```

# 接口十三、获取附件列表

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/AttachmentManage/GetAttachmentListRequest

- **请求方式：**POST

- **Content-Type：**application/x-www-form-urlencoded

- ##### 请求参数：

  id=ed58bf27a6c14ebe8ab02bf9d598b735

  id：附件关联id

## 接口返回值示例：

```json
{
    "StateCode": 1,
    "Message": "",
    "Data": [
        {
            "ID": "c935e647b1cc4d578dbb880f1e731c88",
            "BusinessType": "CompanyRegisterRequest",
            "BusinessID": "ed58bf27a6c14ebe8ab02bf9d598b735",
            "AttachmentName": null,
            "AttachmentExt": null,
            "AttachmentType": "申请材料附件",
            "AttachmentCategories": "企业法人营业执照或者事业单位法人证书",
            "AttachmentPath": null,
            "StateCode": 0
        },
        {
            "ID": "b6b8b5e78a7040278fffe8031dc0851b",
            "BusinessType": "CompanyRegisterRequest",
            "BusinessID": "ed58bf27a6c14ebe8ab02bf9d598b735",
            "AttachmentName": null,
            "AttachmentExt": null,
            "AttachmentType": "申请材料附件",
            "AttachmentCategories": "企业法人营业执照或者事业单位法人证书",
            "AttachmentPath": null,
            "StateCode": 0
        }
    ]
}
```

# 接口十四、签收单位信息审核

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/CompanyRegister/AcceptCompanyAuditRequest

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

  id=3d79dfd3b59643cf947f40129ae9580a

  id：单位ID

## 接口返回值示例：

```json
{ 
   //接口状态 是否成功 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "签收成功",
  //接口返回数据
  "DATA": "",
}
```

# 接口十五、通过单位信息审核

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/CompanyRegister/ThroughCompanyAuditRequest

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

  id=3d79dfd3b59643cf947f40129ae9580a

  id：单位ID

## 接口返回值示例：

```json
{ 
   //接口状态 是否成功 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "审核成功",
  //接口返回数据
  "DATA": "",
}
```

# 接口十六、退回单位信息审核

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/CompanyRegister/BackCompanyAuditRequest

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

  ```json
  {
      "ID": "3d79dfd3b59643cf947f40129ae9580a",
      "ResponseMessage": "未上传XXX文件"
  }
  ```

## 接口返回值示例：

```json
{ 
   //接口状态 是否成功 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "退回成功",
  //接口返回数据
  "DATA": "",
}
```

# 接口十七、关闭单位信息审核

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/CompanyRegister/CloseCompanyAuditRequest

- **请求方式：**POST  

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

  ```json
  {
      "ID": "3d79dfd3b59643cf947f40129ae9580a",
      "ResponseMessage": "不予受理"
  }
  ```

## 接口返回值示例：

```json
{ 
   //接口状态 是否成功 1成功 0失败
  "StateCode": 1,
  //接口返回信息
  "Message": "关闭成功",
  //接口返回数据
  "DATA": "",
}
```

# 接口十八、图片附件预览

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/AttachmentManage/ShowImage

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- ##### 请求参数：

  id=f80f613ce9994db4b0a984a4220da8e1

  id：附件唯一id

## 接口返回值说明：

1、接口调用成功会直接进行图片预览

2、接口调用失败时，根据HttpStatusCode进行判断

​      a)当ID为空或NULL时，HttpStatusCode=400，错误请求。

​      b)当查询不到附件信息时，HttpStatusCode=404，未找到。

​      c)发生异常时，HttpStatusCode=500。

# 接口十九、图片附件缩略图预览

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/AttachmentManage/ShowThumbnail

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- ##### 请求参数：

  id=f80f613ce9994db4b0a984a4220da8e1

  id：附件唯一id

## 接口返回值说明：

1、接口调用成功会直接进行图片预览

2、接口调用失败时，根据HttpStatusCode进行判断

​      a)当ID为空或NULL时，HttpStatusCode=400，错误请求。

​      b)当查询不到附件信息时，HttpStatusCode=404，未找到。

​      c)发生异常时，HttpStatusCode=500。

# 接口二十、根据分类名称获取分类集合

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/DataDirectory/GetDirectoryListOrTree

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- ##### 请求参数：

  name=测试分类&type=tree

  name：分类名称   1、甲级专业范围 2、乙级专业范围 3、丙级专业范围 4、丁级专业范围 5、多测合一业务范围 6、单位性质

  type：集合类型  （tree 树结构  list 普通列表）

## 接口返回值示例：

```json
//专业范围
{
    "StateCode": 1,
    "Message": "",
    "Data": [
        {
            "value": "82806cefe03a4d6eb4ef07e6583f3695",
            "label": "工程测量专业",
            "children": [
                {
                    "value": "a970f8a7cfa34b768af5c76ece05772e",
                    "label": "控制测量",
                    "children": null
                },
                {
                    "value": "a01f4de2127646cf88c4e16b359f6a26",
                    "label": "工程测量监理",
                    "children": null
                }
            ]
        },
        {
            "value": "f4dfc072febd434788836f2e1b58ca55",
            "label": "海洋测绘专业",
            "children": [
                {
                    "value": "5003f4ec027d42119f5f692314e2c04c",
                    "label": "海域权属测绘",
                    "children": null
                },
                {
                    "value": "f025165d73c24d34b408ba3efd76a029",
                    "label": "海岸地形测量",
                    "children": null
                }
            ]
        }
    ]
}
//多测合一业务范围
{
    "StateCode": 1,
    "Message": "",
    "Data": [
        {
            "value": "7e9406be06f5414a847c676e74dc2eb3",
            "label": "勘测定界",
            "children": [
                {
                    "value": "6e85e30833ff4b0288c49ed93a609d25",
                    "label": "蓝线图",
                    "children": null
                },
                {
                    "value": "78dde67cd9b54d27926313637810f30e",
                    "label": "用地预审",
                    "children": null
                },
                {
                    "value": "724ad69ac1d746058e7bde9d3dd7588c",
                    "label": "权属初审",
                    "children": null
                }
            ]
        },
        {
            "value": "b5103934e40b4ba18343356d9dfb2e4c",
            "label": "规划定点",
            "children": [
                {
                    "value": "4b16437446194f709c3f2223e6d757a6",
                    "label": "拨地定桩",
                    "children": null
                },
                {
                    "value": "b15c787e1b88486f9fda524239176278",
                    "label": "面积审核",
                    "children": null
                },
                {
                    "value": "abaa7f6ed4034c39b2fbffecfe787507",
                    "label": "宗地不动产权籍调查",
                    "children": null
                }
            ]
        },
        {
            "value": "4bc844a0a14c459eb5efe5cd843fe1e3",
            "label": "验线",
            "children": [
                {
                    "value": "6bdf21c03f7c4794aec10c49298aa546",
                    "label": "放线测量",
                    "children": null
                },
                {
                    "value": "ba71bf90cce94c4480ba5b58bc4cd313",
                    "label": "不动产预测绘",
                    "children": null
                }
            ]
        },
        {
            "value": "16ede6c6de554e7680c7762017fbdc22",
            "label": "规划核实",
            "children": [
                {
                    "value": "2c23b2d611684161b8e115de832301f0",
                    "label": "规划核实测量",
                    "children": null
                },
                {
                    "value": "fff594e805604e6682bf39ec2dd69603",
                    "label": "不动产实测绘",
                    "children": null
                }
            ]
        }
    ]
}
//单位性质
{
    "StateCode": 1,
    "Message": "",
    "Data": [
        {
            "id": "55beb3c5d0ea48838ad3341449ba36a4",
            "name": "国有单位"
        },
        {
            "id": "580d851516f542f0ad40175c215647bc",
            "name": "私营单位"
        },
        {
            "id": "57cc9805b9c04c83a116b4ae861de8f7",
            "name": "有限责任公司"
        },
        {
            "id": "f8ef9a06cef6423c916a3e07a7bc3809",
            "name": "股份合作制单位"
        },
        {
            "id": "f00518a4077b4191ae905a2a7ccf3efc",
            "name": "股份有限公司"
        }
    ]
}
```

# 接口二十一、获取单位审核信息列表

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/CompanyRegister/GetCompanyListInfo

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- ##### 请求参数：

  StateCode=6&CompanyName=开发商测试&PageIndex=1&PageSize=5&Sort=PostTime

  StateCode：单位状态   6 获取全部

  CompanyName：单位名称

  PageIndex：分页页码

  PageSize：分页大小
  
  Sort：排序字段

## 接口返回值示例：

```json
{
    "StateCode": 1,
    "Message": "",
    "Data": {
        "DataTable": [
            {
                "ID": "d27aed0403ec42ec878e2eb342135eac",
                "CompanyName": "CEHUI测试保存",
                "CompanyNo": "12450100054381216C",
                "CompanyType": "测绘单位",
                "CreateUserID": "bb1e5004-2bcf-41ac-b28b-ecffb75975e1",
                "CreateUserName": "111",
                "CreateUserPersonNo": "1111",
                "CreateTime": "2020-7-1 10:07:48",
                "PostTime": null,
                "AcceptAuditTime": null,
                "SuccessTime": null,
                "CloseTime": null,
                "StateCode": 3,
                "ResponseMessage": null,
                "ResponsePerson": null
            }
        ],
        "Count": {
            "Total": 25,
            //总记录
            "DQS": 3,
            //待签收
            "SHZ": 0,
            //审核中
            "YTG": 3,
            //已通过
            "YTH": 0,
            //已退回
            "YGB": 19//已关闭
        },
        "Page": {
            "PageIndex": 1,
            //当前页码
            "PageSize": 5,
            //当前页大小
            "Total": 5//当前查询结果总记录数
        }
    }
}
```

# 接口二十二、根据单位id获取审核详情

## 接口信息

- **接口地址：** https://practice.nngeo.com/sdcapi/CompanyRegister/GetCompanyAuditInfo

- **请求方式：**Get

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

  id=3d79dfd3b59643cf947f40129ae9580a

  id：单位ID

## 接口返回值示例：

```json
{
    "StateCode": 1,
    "Message": "",
    "Data": {
        "DetailInfo": "{\"CompanyBaseInfo\":{\"CompanyName\":\"开发商测试\",\"CompanyType\":\"开发商\",\"CompanyNo\":null,\"LegalpersonName\":\"cs\",\"LegalPersonNumber\":\"00000\",\"Contacter\":\"梁秀明\",\"ContacterPhone\":\"000\",\"CompanyAddress\":\"测试\",\"CreditCode\":\"555555555555555555\"}}",
        "CompanyAuditInfo": {
            "StateCode": 5,
            "ResponsePerson": "cs",
            "ResponseMessage": "haha",
            "ResponseTime": "2020-7-1 16:25:01"
        }
    }
}
```

# 接口二十三、业务创建

## 接口信息

- **接口地址：** https://practice.nngeo.com/sdcapi/BusinessFlow/CreateNewBusiness

- **请求方式：**POST

- **Content-Type：**application/json, text/json

- **请求参数**：

  ```json
  {
      //业务名称
      "BusinessName": "测试",
      //业务逻辑类  基础数据下载 ：BaseSurveyDataDownloadFlow
      "BusinessClass": "BaseSurveyDataDownloadFlow"
  }
  ```

## 接口返回值示例：

```json
{
    "StateCode": 1,
    "Message": "创建成功",
    "Data": "9986b30ac092466dbe3f8cdf24102417"
}
```

# 接口二十四、业务保存

## 接口信息

- **接口地址：** https://practice.nngeo.com/sdcapi/BusinessFlow/SaveBusiness

- **请求方式：**POST

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

  ```json
  {
  	//业务ID
      "id": "9986b30ac092466dbe3f8cdf24102417",
      //业务信息
      "model":{"BaseInfo":{"ID":"9986b30ac092466dbe3f8cdf24102417","BusinessNumber":"2020070900011","BusinessName":"wy_cs1","BusinessType":"基础测绘地理信息数据申请","BusinessClass":"BaseSurveyDataDownloadFlow","StateCode":0,"CreateUserId":"1","CreatePersonName":"1","CreatePersonNo":"1","CreatePersonPhone":"1","ExtendInfo":null,"CreateTime":"2020-07-09T18:57:47","FinishTime":null},"ContentInfo":{"ID":"9986b30ac092466dbe3f8cdf24102417","ProjectName":"wy_cs","ProjectClass":"CS1","DataUsage":"CS2","DataItems":"CS3"},"Attachments":[],"ActionsInfos":[{"ID":"ddd60b1b379e480193e92e56296b84b6","BusinessId":"9986b30ac092466dbe3f8cdf24102417","LinkName":"上传范围和附件","StateCode":0,"StartTime":"2020-07-09T21:22:35","SignatureTime":null,"EndTime":null,"CurrentUserID":null,"ActionId":1},{"ID":"2efe9ad6415e42cd87fe79dd31b68750","BusinessId":"9986b30ac092466dbe3f8cdf24102417","LinkName":"填写申请信息","StateCode":2,"StartTime":"2020-07-09T18:57:47","SignatureTime":"2020-07-09T21:18:46","EndTime":"2020-07-09T21:22:35","CurrentUserID":"1","ActionId":0}]} 
  }
  ```

## 接口返回值示例：

```json
{
    "StateCode": 1,
    "Message": "保存成功",
    "Data": ""
}
```

# 接口二十五、业务签收

## 接口信息

- **接口地址：** https://practice.nngeo.com/sdcapi/BusinessFlow/AcceptBusiness

- **请求方式：**POST

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

  id=9986b30ac092466dbe3f8cdf24102417

  id：业务ID

## 接口返回值示例：

```
{
    "StateCode": 1,
    "Message": "签收成功",
    "Data": ""
}
```

# 接口二十六、业务提交

## 接口信息

- **接口地址：** https://practice.nngeo.com/sdcapi/BusinessFlow/SubmmitBusiness

- **请求方式：**POST

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

  id=9986b30ac092466dbe3f8cdf24102417

  id：业务ID

## 接口返回值示例：

```
{
    "StateCode": 1,
    "Message": "提交成功",
    "Data": ""
}
```

# 接口二十七、根据业务ID获取业务信息

## 接口信息

- **接口地址：** https://practice.nngeo.com/sdcapi/BusinessFlow/GetBusinessByID

- **请求方式：**POST

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

  id=9986b30ac092466dbe3f8cdf24102417

  id：业务ID

## 接口返回值示例：

```json
{
    "StateCode": 1,
    "Message": "",
    "Data": {
    	//业务基本信息
        "BaseInfo": {
            "ID": "9986b30ac092466dbe3f8cdf24102417",
            //业务编号
            "BusinessNumber": "2020070900011",
            //业务名称
            "BusinessName": "wy_cs1",
            //业务类型
            "BusinessType": "基础测绘地理信息数据申请",
            //业务逻辑类
            "BusinessClass": "BaseSurveyDataDownloadFlow",
            //业务状态
            "StateCode": 0,
            //创建业务的用户ID
            "CreateUserId": "1",
            //创建业务的用户姓名
            "CreatePersonName": null,
            //创建业务的用户身份证号
            "CreatePersonNo": "1",
            //创建业务的用户联系电话
            "CreatePersonPhone": "1",
            //预留的扩展信息，Clob类型
            "ExtendInfo": null,
            //创建时间
            "CreateTime": "2020-07-09T18:57:47",
            //结束时间
            "FinishTime": null,
            //开发商名称
            "DeveloperName": null,
            //测绘单位社会统一信用代码
            "DeveloperNo": null,
            "SurveyCompanyName": null,
            //测绘单位社会统一信用代码
            "SurveyCompanyNo": null,
        },
        //业务内容信息，不同业务内容不同
        "ContentInfo": {
        	//主键ID，与BusinessBaseInfo表的ID关联的外键
            "ID": "99f393ff5b274cd4ab4da4edc7777546",
            //项目名称
            "ProjectName": "1",
            //立项依据
            "ProjectBasis": "2",
            //项目范围
            "ProjectScope": "3"
        },
        //附件信息列表
        "Attachments": [
        ],
        //当前经过的流转环节信息
        "ActionsInfos": [
            {
            	//环节ID
                "ID": "ddd60b1b379e480193e92e56296b84b6",
                //业务ID
                "BusinessId": "9986b30ac092466dbe3f8cdf24102417",
                //环节名称
                "LinkName": "上传范围和附件",
                // 环节状态，0待签收，1办理中，2已完成
                "StateCode": 0,
                //环节开始时间
                "StartTime": "2020-07-09T21:22:35",
                //签收时间
                "SignatureTime": null,
                //环节完成时间
                "EndTime": null,
                //环节执行用户ID
                "CurrentUserID": null,
                //流程环节ID
                "ActionId": 1
            },
            {
                "ID": "2efe9ad6415e42cd87fe79dd31b68750",
                "BusinessId": "9986b30ac092466dbe3f8cdf24102417",
                "LinkName": "填写申请信息",
                "StateCode": 2,
                "StartTime": "2020-07-09T18:57:47",
                "SignatureTime": "2020-07-09T21:18:46",
                "EndTime": "2020-07-09T21:22:35",
                "CurrentUserID": "1",
                "ActionId": 0
            }
        ],
        //工作流定义信息
        "FlowInfo": {
        	//流程环节定义信息JSON
            "FlowActionJSON": "[{\"ID\":0,\"Name\":\"填写申请信息\"},{\"ID\":1,\"Name\":\"上传范围和附件\"},{\"ID\":2,\"Name\":\"审核\"},{\"ID\":3,\"Name\":\"下载数据\"}]",
            //流程名称
            "FlowName": "基础测绘地理信息数据申请",
            //所属大类
            "Catalog": "勘测定界",
            //流程环节信息
            "FlowActionInfo": {
            	//提交验证方法
                "ValidateActionPost": null,
                "Actions": [
                    {
                        "ID": 0,
                        "Index": 0,
                        "Name": "填写申请信息",
                        //有权限的角色
                        "ActionRoles": [
                            1,
                            0
                        ]
                    },
                    {
                        "ID": 1,
                        "Index": 1,
                        "Name": "上传范围和附件",
                        "ActionRoles": [
                            8
                        ]
                    },
                    {
                        "ID": 2,
                        "Index": 2,
                        "Name": "审核",
                        "ActionRoles": [
                            7
                        ]
                    },
                    {
                        "ID": 3,
                        "Index": 3,
                        "Name": "下载数据",
                        "ActionRoles": [
                            8
                        ]
                    }
                ],
                //开始环节ID
                "StartActionId": 0,
                //结束环节ID集合
                "EndActionIds": [
                    3
                ],
                //路由，用于连接两个环节
                "Routes": [
                    {
                    	//路由标签
                        "RouteLabel": null,
                        //路由开始环节Id
                        "FromActionId": 0,
                        //路由结束环节Id
                        "ToActionId": 1
                    },
                    {
                        "RouteLabel": null,
                        "FromActionId": 1,
                        "ToActionId": 2
                    },
                    {
                        "RouteLabel": null,
                        "FromActionId": 2,
                        "ToActionId": 3
                    }
                ]
            }
        }
    }
}
```

# 接口二十八、开发商人员信息保存

## 接口信息

- **接口地址：** https://practice.nngeo.com/sdcapi/CompanyRegister/SaveDeveloperUserInfo

- **请求方式：**POST

- **Content-Type：**application/json, text/json

- **请求参数**：

  ```json
  {
  	//人员ID  为空时后端自动生成执行新增  不为空时执行更新
      "ID": "9986b30ac092466dbe3f8cdf24102417",
      //单位ID
      "RelationRequestId":"1",
      //角色
      "PersonRole": "单位管理员",
      //身份证号
      "PersonNumber": "150426199903025033",
      //姓名
      "PersonName": "测试",
      //性别
      "PersonSex": "男",
      //手机号
      "PersonPhone": "13625363526",
      //年龄
      "PersonAge": "3",
  }
  ```

## 接口返回值示例：

```
{
    "StateCode": 1,
    "Message": "保存成功",
    "Data": ""
}
```

# 接口二十九、开发商人员信息删除

## 接口信息

- **接口地址：** https://practice.nngeo.com/sdcapi/CompanyRegister/DeleteDeveloperUserInfo

- **请求方式：**POST

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

  id=9986b30ac092466dbe3f8cdf24102417

  id：人员ID

## 接口返回值示例：

```
{
    "StateCode": 1,
    "Message": "删除成功",
    "Data": ""
}
```

# 接口三十、根据单位ID获取人员信息列表

## 接口信息

- **接口地址：** https://practice.nngeo.com/sdcapi/CompanyRegister/GetDeveloperUserList

- **请求方式：**GET

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

  id=d6b92e284bd041fcbfaa02fe6ff2cee0&PageIndex=1&PageSize=20

  id：单位ID

  PageIndex：分页页码

  PageSize：分页大小

## 接口返回值示例：

```
{
    "StateCode": 1,
    "Message": null,
    "Data": {
        "DataTable": [
            {
                "ID": "43f23dc6d8174676a5fc0e9c75bf7a15",
                "RelationRequestId": "d6b92e284bd041fcbfaa02fe6ff2cee0",
                "PersonName": "cs",
                "PersonNumber": "000000",
                "PersonPhone": "000",
                "PersonSex": "男",
                "PersonAge": "25",
                "PersonCategory": null,
                "PersonRole": "单位管理员",
                "PersonMajor": null,
                "ProfessionalTitle": null,
                "RegisteredSurveyorNo": null,
                "ValidityTime": null,
                "AttachmentInfo": "{\"PostCertificate\":[],\"GraduateCertificate\":[],\"PersonNumberImg\":[],\"LaborContract\":[],\"SocialSecurityCertificate\":[],\"SurveyorCertificateImg\":[],\"SurveyorCertificateChapter\":[]}"
            }
        ],
        "Page": {
            "PageIndex": 1,
            "PageSize": 20,
            "Total": 1
        }
    }
}
```

# 接口三十一、根据业务逻辑信息判断用户是否能创建业务

## 接口信息

- **接口地址：** https://practice.nngeo.com/sdcapi/BusinessFlow/IsFlowCanCreate

- **请求方式：**GET

- **Content-Type：**application/x-www-form-urlencoded

- **请求参数**：

  businessClass=BaseSurveyDataDownloadFlow

  businessClass：业务逻辑类

## 接口返回值示例：

```
{
    "StateCode": 1,
    "Message": "用户可创建此类型业务",
    "Data": true
}
Or
{
    "StateCode": 1,
    "Message": "用户没有创建此类型业务的权限",
    "Data": false
}
```

# 接口三十一、获取业务列表

## 接口信息

- **接口地址：** https://practice.nngeo.com/sdcapi/BusinessFlow/GetFlowList
- **请求方式：**GET
- **Content-Type：**application/x-www-form-urlencoded

## 接口返回值示例：

```
{
    "StateCode": 1,
    "Message": null,
    "Data": [
        {
            "value": 1,
            "label": "勘测定界",
            "children": [
                {
                    "value": 5,
                    "label": "基础测绘地理信息数据申请",
                    "businessClass": "BaseSurveyDataDownloadFlow",
                    "url": "/user/business/base-data",
                    "children": null
                },
                {
                    "value": 6,
                    "label": "蓝线图",
                    "businessClass": "待定",
                    "url": "/user/business/blue-line",
                    "children": null
                }
            ]
        },
        {
            "value": 2,
            "label": "规划定点",
            "children": [
                {
                    "value": 7,
                    "label": "拨地定桩",
                    "businessClass": "待定",
                    "url": "/user/business/staking",
                    "children": null
                }
            ]
        },
        {
            "value": 3,
            "label": "验线",
            "children": [
                {
                    "value": 8,
                    "label": "放线测量",
                    "businessClass": "MeasurePutLineFlow",
                    "url": "/user/business/setout-survey",
                    "children": null
                },
                {
                    "value": 9,
                    "label": "不动产预测绘",
                    "businessClass": "RealEstatePreSurveyFlow",
                    "url": "/user/business/pre-mapping",
                    "children": null
                }
            ]
        },
        {
            "value": 4,
            "label": "规划核实",
            "children": [
                {
                    "value": 10,
                    "label": "规划核实测量",
                    "businessClass": "待定",
                    "url": "/user/business/proj-measure",
                    "children": null
                },
                {
                    "value": 11,
                    "label": "不动产实测绘",
                    "businessClass": "待定",
                    "url": "/user/business/actual-mapping",
                    "children": null
                }
            ]
        }
    ]
}
```

# 接口三十二、PDF附件预览

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/AttachmentManage/ShowPDF

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- ##### 请求参数：

  id=f80f613ce9994db4b0a984a4220da8e1

  id：附件唯一id

## 接口返回值说明：

1、接口调用成功会直接进行图片预览

2、接口调用失败时，根据HttpStatusCode进行判断

​      a)当ID为空或NULL时，HttpStatusCode=400，错误请求。

​      b)当查询不到附件信息时，HttpStatusCode=404，未找到。

​      c)发生异常时，HttpStatusCode=500。

# 接口三十三、获取工程规划许可证信息

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/BusinessFlow/GetProjectPlanPermissionInfo

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- ##### 请求参数：

  businessId=111&businessClass=MeasurePutLineFlow&code=450101201001244

  businessId：业务id
  
  businessClass：业务逻辑类
  
  code：工程规划许可证号

## 接口返回值说明：

```
{
    "StateCode": 1,
    "Message": null,
    "Data": {
        "ConstructCompany": "南宁市海茵地产开发公司",
        "Address": "青秀区仙葫大道中16号",
        "ProjectName": "5-1号楼",
        "AppendixImgNumber": "GC0101201001700",
        "Buildings": [
            {
                "Type": "商铺",
                "Area": 381.59
            },
            {
                "Type": "住宅",
                "Area": 2152.18
            },
            {
                "Type": "架空绿化",
                "Area": 216.78
            }
        ],
        "BuildingNature": "新建",
        "BuildingStructure": "框架",
        "TotalArea": {
            "Aboveground": 2750.55,
            "Underground": 0.0
        },
        "BaseArea": 617.87,
        "Floors": {
            "Aboveground": 6.0,
            "Underground": 0.0
        },
        "BuildingHeight": 21.6,
        "BuildingBlockNumber": 1,
        "Invest": 316.2,
        "SetType": [
            {
                "Name": "大于90平米",
                "Number": 12,
                "Area": 1514.82
            },
            {
                "Name": "小于90平米",
                "Number": 8,
                "Area": 637.36
            }
        ],
        "Others": {
            "Name": null,
            "Number": 0,
            "Area": 0.0
        },
        "Remark": null
    }
}
```

# 接口三十四、获取我的业务列表

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/BusinessFlow/GetMyBusinessList

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- ##### 请求参数：

  q&stateCode=1&type=0&pageIndex=1&pageSize=20

  q：筛选字符   业务名称or测绘单位名称or开发商名称

  stateCode：业务状态  -1默认（全部状态）  0待签收  1办理中 2已完成 3已退回 4已关闭
  
  type：类型    0当前业务，1已办业务
  
  pageIndex：分页页码
  
  pageSize：分页大小

## 接口返回值说明：

```json
{
    "StateCode": 1,
    "Message": null,
    "Data": {
        "PageIndex": 1,
        "PageSize": 20,
        "TotalCount": 2,
        "List": [
            {
                //业务ID
                "ID": "5e85ff64945c40b79c3882c9cc9a392b",
                //创建人ID
                "CreateUserId": "bb1e5004-2bcf-41ac-b28b-ecffb75975e1",
                //业务编号
                "BusinessNumber": "2020072200005",
                //业务名称
                "BusinessName": "WY_CS0721_01",
                //业务类型
                "BusinessType": "放线测量",
                //业务逻辑类
                "BusinessClass": "MeasurePutLineFlow",
                //创建时间
                "CreateTime": "2020-07-22 10:52:17",
                //创建用户名称
                "CreatePersonName": "cs",
                //开发商名称
                "DeveloperName": "自然资源测绘单位Test",
                //开发商社会统一信用代码
                "DeveloperNo": "12450100007743536H",
                //测绘单位名称
                "SurveyCompanyName": null,
                //测绘单位社会统一信用代码
                "SurveyCompanyNo": null,
                //业务状态
                "StateCode": 1,
                //环节名称
                "LinkName": "填写申请信息",
                //开始时间
                "StartTime": "2020-07-22 10:52:25",
                //签收时间
                "SignatureTime": "2020-07-22 10:52:25",
                //当前用户ID
                "CurrentUserId": "bb1e5004-2bcf-41ac-b28b-ecffb75975e1",
                //完成时间
                "FinishTime": null
            }
        ]
    }
}
```

# 接口三十五、获取选择测绘单位列表

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/BusinessFlow/GetSurveyCompanyList

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- ##### 请求参数：

  BusinessClass=RealEstatePreSurveyFlow&PageIndex=1&PageSize=20&CompanyName=

  BusinessClass：业务逻辑类

  PageIndex：当前页码

  PageSize：每页显示记录数

  CompanyName：单位名称 （模糊匹配）

## 接口返回值说明：

```json
{
    "StateCode": 1,
    "Message": null,
    "Data": {
        "DataTable": [
            {
                "CompanyName": "自然资源测绘单位Test",
                "CreditCode": "12450100007743536H",
                "CreditRating": "A"
            },
            {
                "CompanyName": "南宁test测绘单位",
                "CreditCode": "91450100MA5K123654",
                "CreditRating": "A"
            }
        ],
        "Page": {
            "PageIndex": 1,
            "PageSize": 20,
            "Total": 2
        }
    }
}
```

# 接口三十六、委托测绘单位并提交业务

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/BusinessFlow/EntrustSurveyCompany

- **请求方式：**POST

- **Content-Type：**application/x-www-form-urlencoded

- ##### 请求参数：

  BusinessId=1&CreditCode=2

  BusinessId：业务ID

  CreditCode：测绘单位统一社会信用代码


## 接口返回值说明：

```json
{
    "StateCode": 1,
    "Message": "委托成功",
    "Data": null
}
```

# 接口三十七、业务退回

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/BusinessFlow/BackBusiness

- **请求方式：**POST

- **Content-Type：**application/x-www-form-urlencoded

- ##### 请求参数：

  Id=1&backReason=1111

  Id：业务ID
  
  backReason：退回原因

## 接口返回值说明：

```
{
    "StateCode": 1,
    "Message": "退回成功",
    "Data": null
}
```

# 接口三十八、注册测绘师确认测绘成果

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/BusinessFlow/ConfirmSurveyResult

- **请求方式：**POST

- **Content-Type：**application/x-www-form-urlencoded

- ##### 请求参数：

  Id=1

  Id：业务ID


## 接口返回值说明：

```json
{
    "StateCode": 1,
    "Message": "确认成功",
    "Data": null//未做详细说明 具体参照接口说明二十七
}
```

# 接口三十九、测绘成果确认验收

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/BusinessFlow/AcceptSurveyProjectResult

- **请求方式：**POST

- **Content-Type：**application/x-www-form-urlencoded

- ##### 请求参数：

  Id=1

  Id：业务ID


## 接口返回值说明：

```json
{
    "StateCode": 1,
    "Message": "验收成功",
    "Data": null
}
```

# 接口四十、获取预测绘项目相关信息

## 接口信息

- **接口地址：**  https://practice.nngeo.com/internal/epsapi/GetPreSurveyProjectInfo

- **请求方式：**GET

- **Content-Type：**application/x-www-form-urlencoded

- ##### 请求参数：

  Id=1

  Id：业务ID


## 接口返回值说明：

```json
{
    "StateCode": 1,
    "Message": "获取成功",
    "Data": {
        "Code": "450101201001244",
        "ProjectName": "5-1号楼",
        "Address": "青秀区仙葫大道中16号"
    }
}
```

# 接口四十一、提交测绘成果到多测合一系统

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/BusinessFlow/PostSurveyProjectResult

- **请求方式：**POST

- **Content-Type：**application/x-www-form-urlencoded

- ##### 请求参数：

  Id=1

  Id：业务ID


## 接口返回值说明：

```json
{
    "StateCode": 1,
    "Message": "提交成功",
    "Data": "aaaaaaaaa",
}
```

# 接口四十二、获取成果检查状态

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/BusinessFlow/GetSurveyResultCheckState

- **请求方式：**GET

- **Content-Type：**application/x-www-form-urlencoded

- ##### 请求参数：

  checkId=1

  checkId：检查ID


## 接口返回值说明：

```json
{
    "StateCode": 1,
    "Message": "获取成功",
    "Data": 1,//0 检查中  1 检查通过 2 检查不通过
}
```

# 接口四十三、提交项目范围到多测合一系统

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/BusinessFlow/PostProjectScopeResult

- **请求方式：**POST

- **Content-Type：**application/x-www-form-urlencoded

- ##### 请求参数：

  Id=1

  Id：业务ID


## 接口返回值说明：

```json
{
    "StateCode": 1,
    "Message": "提交成功",
    "Data": "aaaaaaaaa",
}
```

# 接口四十四、获取项目范围检查状态

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/BusinessFlow/GetProjectScopeCheckState

- **请求方式：**GET

- **Content-Type：**application/x-www-form-urlencoded

- ##### 请求参数：

  checkId=1&businessClass=BlueLineSurveyFlow

  checkId：检查ID
  
  businessClass：业务逻辑类


## 接口返回值说明：

```json
{
    "StateCode": 1,
    "Message": "获取成功",
    "Data": 1,//0 检查中  1 检查通过 2 检查不通过
}
```

# 接口四十五、启动EPS进行数据切图

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/BusinessFlow/PostEpsDownLoadResult

- **请求方式：**POST

- **Content-Type：**application/x-www-form-urlencoded

- ##### 请求参数：

  Id=1

  Id：业务ID


## 接口返回值说明：

```json
{
    "StateCode": 1,
    "Message": "提交成功",
    "Data": "aaaaaaaaa",
}
```

# 接口四十六、获取EPS下载状态

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/BusinessFlow/GetEPSDownLoadState

- **请求方式：**GET

- **Content-Type：**application/x-www-form-urlencoded

- ##### 请求参数：

  downloadid=1&businessClass=BlueLineSurveyFlow

  downloadid：检查ID
  
  businessClass：业务逻辑类


## 接口返回值说明：

```json
{
    "StateCode": 1,
    "Message": "获取成功",
    "Data": 1,//0 检查中  1 下载成功 2 下载失败
}
```

# 接口四十七、蓝线图、拨地定桩汇交成果

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/BusinessFlow/PostAchievementResult

- **请求方式：**POST

- **Content-Type：**application/x-www-form-urlencoded

- ##### 请求参数：

  Id=1

  Id：业务ID


## 接口返回值说明：

```json
{
    "StateCode": 1,
    "Message": "提交成功",
    "Data": "aaaaaaaaa",
}
```

# 接口四十八、获取汇交成果检查状态

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/BusinessFlow/GetAchievementCheckState

- **请求方式：**GET

- **Content-Type：**application/x-www-form-urlencoded

- ##### 请求参数：

  checkid=1&businessClass=BlueLineSurveyFlow

  checkid：汇交成果检查ID

  businessClass：业务逻辑类


## 接口返回值说明：

```json
{
    "StateCode": 1,
    "Message": "获取成功",
    "Data": 1,//0 检查中  1 检查通过 2 检查不通过
}
```

# 接口四十九、系统附件下载

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/AttachmentManage/SystemAttachmentDownload

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- ##### 请求参数：

  fileName=拨地定桩（成果样例）.mdb

  fileName：附件名称

## 接口返回值说明：

1、接口调用成功会直接通过浏览器进行下载

2、接口调用失败时，根据HttpStatusCode进行判断

​      a)当ID为空或NULL时，HttpStatusCode=400，错误请求。

​      b)当查询不到附件信息时，HttpStatusCode=404，未找到。

​      c)发生异常时，HttpStatusCode=500。

# 接口五十、根据单位id查看资质证书副本

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/AttachmentManage/ShowQualificationImage

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- ##### 请求参数：

  id=f80f613ce9994db4b0a984a4220da8e1

  id：单位ID

## 接口返回值说明：

1、接口调用成功会直接进行图片预览

2、接口调用失败时，根据HttpStatusCode进行判断

​      a)当ID为空或NULL时，HttpStatusCode=400，错误请求。

​      b)当查询不到附件信息时，HttpStatusCode=404，未找到。

​      c)发生异常时，HttpStatusCode=500。

# 接口五十一、基础数据附件下载

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/AttachmentManage/BaseDataDownloadRequest

- **请求方式：**GET  

- **Content-Type：**application/x-www-form-urlencoded

- ##### 请求参数：

  id=f80f613ce9994db4b0a984a4220da8e1&validateNumber=F7j6

  id：附件id

  validateNumber：提取码

## 接口返回值说明：

1、接口调用成功会直接下载

2、接口调用失败时，根据HttpStatusCode进行判断

​      a)当ID为空或NULL时/提取码错误/附件过期/附件锁定，HttpStatusCode=400，错误请求。

​      b)当查询不到附件信息时，HttpStatusCode=404，未找到。

​      c)发生异常时，HttpStatusCode=500。

# 接口五十二、附件锁定

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/AttachmentManage/AttachmentLockRequest

- **请求方式：**POST

- **Content-Type：**application/x-www-form-urlencoded

- ##### 请求参数：

  id=f80f613ce9994db4b0a984a4220da8e1

  id：附件id


## 接口返回值说明：

```
{
    "StateCode": 1,
    "Message": "锁定成功",
    "Data": "",
}
```

# 接口五十三、获取立项依据文件内容

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/AttachmentManage/GetAttachementFileStr

- **请求方式：**GET

- **Content-Type：**application/x-www-form-urlencoded

- ##### 请求参数：

  id=f80f613ce9994db4b0a984a4220da8e1

  id：附件id


## 接口返回值说明：

```json
{
    "StateCode": 1,
    "Message": "获取成功",
    "Data": "1,2528568.66741092,36532622.7139649,0/r/n2,2528558.39840584,36532538.0208658,0"
}
```

# 接口五十四、根据宗地号获取楼盘表信息

## 接口信息

- **接口地址：**  https://practice.nngeo.com/sdcapi/BusinessFlow/GetBuildingTableByZDH

- **请求方式：**GET

- **Content-Type：**application/x-www-form-urlencoded

- ##### 请求参数：

  zdh=450107010201GN00002&chzt=1

  zdh：宗地号。//示例：450107010201GN00002 预测绘；450103004002GB00116实测绘

  chzt：测绘状态，1为预测绘，2为实测绘

## 接口返回值说明：

```json
{
  "StateCode": 1,
  "Message": "",
  "Data": [
    {
      "ZDDM": "450107010201GN00002", //宗地号
      "ZRZGUID": "{9A617802-5D5F-4962-AF61-35179C08A26C}", //自然幢GUID
      "BDCDYH": "450103004002GB00116F00040000", //不动产单元号
      "ZL": "南宁市青秀区嘉宾路南一里1号逸景园4号楼" //坐落
    }
  ]
}
```
